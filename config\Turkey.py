import torch
from torch import nn
import os
from glob import glob
import albumentations as albu
import argparse
from pathlib import Path
import numpy as np
# from net.HRNet2_plus import HRNet, HRNet_S2
# from net.HRNet3 import HRNet, HRNet_S2
# from net.HRNet3_plus import HRNet_S2_DeConv
# from net.HRNet3_plusv4 import HRNet_S2_DeConv
from net.HRNet3_plusv4 import HRNet_S2_DeConv
from loss.loss import OhemCELoss, MaskLoss, BCEFocalLoss, one_hot_CrossEntropy, myOhemCELoss, BCE_OhemCELoss, WBCE_OhemCELoss
from dataset.Turkey_S2 import Turkey_S2, dataset_collate

# =============================训练参数============================ #
batch_size = 4
lr = 1e-4
size = 384
epochs = 50
output_building = 2
output_damaged = 2
data_name = "Turkey"
model_name = "HRNet3_plusv4"
save_path = os.path.join("./result", data_name, model_name, "6_dd_0209")
os.makedirs(save_path, exist_ok=True)

model = HRNet_S2_DeConv(pretrained=False, s2_inchannel=17, frame=16, num_classes=3, damage_classes=output_damaged)
pre_train_model_path = None
# pre_train_model_path = "/mnt/d2/zxq/BuildingDamage/result/Turkey/HRNet3_plusv4/6_dd3_0209/9.pt"
pre_train_model_path = "/mnt/d2/zxq/BuildingDamage/result/UKRxBD_HRnet2/HRNet3_plusv4/5_angle/53.pt"

criterion1 = OhemCELoss(thresh=0.7, n_min=batch_size*size*size//16, ignore_index=255)
criterion2 = nn.L1Loss()
criterion3 = nn.KLDivLoss(reduction="mean")

# 数据集加载 + 多GPU训练
device = 'cuda'
train_dataset = Turkey_S2('train')
val_dataset   = Turkey_S2('test')
collate_fn = dataset_collate
# =============================训练参数============================ #


# =============================测试参数============================ #
data_name = "turkey"
save_name = "6_d_0415"
test_base_path = os.path.join("./dataset/Turkey", "test2") # xBDS2 UKRS2
test_base_save_path = os.path.join("./result", data_name)
model_path = os.path.join("./result/", data_name, model_name, save_name, "38.pt")
time_series_path = os.path.join(test_base_path, "images")
s2_pre_path = os.path.join(test_base_path, "S2")
s2_post_path = os.path.join(test_base_path, "S2_post")

pre_high_image_save_path  = os.path.join(test_base_save_path, "pre_high_image")
post_high_image_save_path  = os.path.join(test_base_save_path, "post_high_image")
pre_s2_image_save_path = os.path.join(test_base_save_path, "pre_s2_image")
post_s2_image_post_save_path = os.path.join(test_base_save_path, "post_s2_image")
pre_label_save_path = os.path.join(test_base_save_path, "pre_label")
post_label_save_path = os.path.join(test_base_save_path, "post_label")

pre_high_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "pre_high_label")
post_high_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "post_high_label")
pre_s2_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "pre_s2_label")
post_s2_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "post_s2_label")
high_gray_save_path = os.path.join(test_base_save_path, model_name, save_name, "high_gray")
s2_gray_save_path = os.path.join(test_base_save_path, model_name, save_name, "s2_gray")
plot_save_path = os.path.join(test_base_save_path, model_name, save_name, "plot")

os.makedirs(pre_high_image_save_path, exist_ok=True)
os.makedirs(post_high_image_save_path, exist_ok=True)

os.makedirs(pre_s2_image_save_path, exist_ok=True)
os.makedirs(post_s2_image_post_save_path, exist_ok=True)

os.makedirs(pre_label_save_path, exist_ok=True)
os.makedirs(post_label_save_path, exist_ok=True)

os.makedirs(pre_high_label_save_path, exist_ok=True)
os.makedirs(post_high_label_save_path, exist_ok=True)
os.makedirs(pre_s2_label_save_path, exist_ok=True)
os.makedirs(post_s2_label_save_path, exist_ok=True)

os.makedirs(high_gray_save_path, exist_ok=True)
os.makedirs(s2_gray_save_path, exist_ok=True)
os.makedirs(plot_save_path, exist_ok=True)

def get_pre_image_name(image_name):
    pre_images = glob(os.path.join(time_series_path, image_name + ".tif"))
    pre_images.sort()
    return pre_images[-1]

# print(names)
pre_high_image_paths = sorted(glob(os.path.join(test_base_path, "images", "**.tif")))
post_high_image_paths = []
pre_s2_image_paths = []
post_s2_image_paths = []
pre_label_paths = []
post_label_paths = []
# print(len(pre_high_image_paths))

for pre_high_image_path in pre_high_image_paths:
    image_name = pre_high_image_path.split(".tif")[0]
    # pre_high_image_paths.append(pre_high_image_path)
    post_high_image_paths.append(pre_high_image_path)
    pre_s2_image_paths.append(image_name.replace("images", "S2"))
    post_s2_image_paths.append(image_name.replace("images", "S2_post_other_time"))
    pre_label_paths.append(image_name.replace("images", "labels").replace("_pre_disaster_", "_building_damage_")+".tif")
    post_label_paths.append(image_name.replace("images", "labels").replace("_pre_disaster_", "_building_damage_")+".tif")

# print(len(post_high_image_paths))

s2_mean_std = np.loadtxt("./dataset/Turkey/turkey_s2_mean_std.txt")
s2_mean, s2_std = s2_mean_std[0,:], s2_mean_std[1,:]

label_keys = [
    [  0,   0,   0], # 0 非建筑物区域
    [255, 255, 255], # 1 未损毁
    [  0,   0, 255], # 2 Moderate
    [  0, 255,   0], # 3 Severe
    [255,   0,   0], # 4 Destroyed
    # [255, 255,   0]  # 5 未确定的值
]

# =============================测试参数============================ #
