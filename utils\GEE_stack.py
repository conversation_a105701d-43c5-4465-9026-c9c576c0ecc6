import ee
import time
import os
import geemap
from glob import glob
from functools import reduce

from tqdm import tqdm

import ee
import os
from tqdm import tqdm
import geemap
from glob import glob

# 初始化 Earth Engine
ee.Initialize()

def add_angles(image):
    """为单张 Sentinel‑2 图像添加角度信息，并保持原投影（10 m）"""
    proj_10m = image.select('B2').projection()

    def get_const(name, default=45):
        val = image.get(name)
        return ee.Image.constant(ee.Algorithms.If(val, val, default)).toFloat()

    solar_zenith = get_const('MEAN_SOLAR_ZENITH_ANGLE')
    solar_azimuth = get_const('MEAN_SOLAR_AZIMUTH_ANGLE')
    view_zenith_B8A = get_const('MEAN_INCIDENCE_ZENITH_ANGLE_B8')
    view_azimuth_B8A = get_const('MEAN_INCIDENCE_AZIMUTH_ANGLE_B8')

    # angles = ee.Image.cat([solar_zenith, solar_azimuth, view_zenith, view_azimuth]) \
    #     .rename(['mean_solar_zenith','mean_solar_azimuth','mean_view_zenith','mean_view_azimuth']) \
    #     .reproject(crs=proj_10m)

    bands_zenith = ['B1','B2','B3','B4','B5','B6','B7','B8','B8A','B9','B10','B11','B12']

    zenith_numbers = [ee.Number(image.get(f'MEAN_INCIDENCE_ZENITH_ANGLE_{band}')) for band in bands_zenith]
    azimuth_numbers = [ee.Number(image.get(f'MEAN_INCIDENCE_AZIMUTH_ANGLE_{band}')) for band in bands_zenith]

    mean_zenith = reduce(lambda a, b: a.add(b), zenith_numbers).divide(len(bands_zenith))
    mean_azimuth = reduce(lambda a, b: a.add(b), azimuth_numbers).divide(len(bands_zenith))

    # print(mean_azimuth.getInfo())

    # 创建新的常数影像波段
    mean_zenith_img = ee.Image.constant(mean_zenith).rename('mean_incidence_zenith_angle').toFloat()
    mean_azimuth_img = ee.Image.constant(mean_azimuth).rename('mean_incidence_azimuth_angle').toFloat()

    angles = ee.Image.cat([solar_zenith, solar_azimuth, mean_zenith_img, mean_azimuth_img, view_zenith_B8A, view_azimuth_B8A])\
        .rename(['SZ', 'SA', 'VZ', 'VA', 'VZ8A', 'VA8A'])\
        .reproject(crs=proj_10m)

    # final_img = image.addBands(angles)


    return image.addBands(angles)

def export_image_to_local(image, description, region_geojson, scale=10, save_path='.'):
    """使用 geemap.download_ee_image 下载影像到本地"""
    outfile = os.path.join(save_path, f"{description}.tif")
    if os.path.exists(outfile):
        print(f"[Skip] 已存在 {outfile}")
        return
    os.makedirs(os.path.dirname(outfile), exist_ok=True)
    try:
        geemap.download_ee_image(
            image=image,
            filename=outfile,
            region=region_geojson,
            scale=scale,
            max_tile_size=16
        )
        print(f"[Saved] {outfile}")
    except Exception as e:
        print(f"[Error] 导出失败: {e}")

def find_S2_data(studyarea, studyarea_name, start_date, end_date,
                 exist_names=(), save_path='.', cloud_threshold=95):
    coll = ee.ImageCollection('COPERNICUS/S2_SR_HARMONIZED') \
        .filterBounds(studyarea) \
        .filterDate(start_date, end_date) \
        .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', cloud_threshold))

    coll = coll.map(add_angles)
    dates = coll.aggregate_array('system:time_start') \
                .map(lambda t: ee.Date(t).format('YYYY-MM-dd')).distinct().getInfo()

    print(f"共找到 {len(dates)} 个日期影像")

    for date in tqdm(dates, desc=f"Processing {studyarea_name}"):
        start = ee.Date(date)
        end = start.advance(1, 'day')
        daily = coll.filterDate(start, end)

        if daily.size().getInfo() == 0:
            print(f"⚠️ {date}: 无影像")
            continue
        # 从 daily 中第一张影像获取 projection
        sample_proj = daily.first().select('B2').projection()
        med = daily.median().clip(studyarea).toFloat().reproject(sample_proj)
        # 计算每日影像的中位数
        # med = daily.median().clip(studyarea).toFloat()
        desc = f"{studyarea_name}_S2_{date.replace('-', '')}"
        if desc not in exist_names:
            continue

        region_geo = studyarea.geometry().bounds().getInfo()
        export_image_to_local(med, desc, region_geo, scale=10, save_path=save_path)

def main_eaton():
    ee.Initialize()
    studyarea_name = f"eaton_fire"
    asset_id = f"projects/green-talent-283008/assets/{studyarea_name}"
    studyarea = ee.FeatureCollection(asset_id)

    start_date = '2024-08-01'
    end_date = '2025-01-15'

    file_names = glob(os.path.join("/mnt/E/Dataset/LA2025/eaton-fire/S2", "*.tif"))
    exist_names = [os.path.basename(f).split(".tif")[0].replace("-", "_") for f in file_names]
    print(f"已存在影像文件: {exist_names}, length: {len(exist_names)}")
    
    save_path = "/mnt/D/PythonProject/BDS12/utils/GEE"

    find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names = exist_names, save_path=save_path, cloud_threshold=80)

if __name__ == "__main__":
    # main_santa()
    # main_harvey()
    # main_hawaii()
    # main_palisades()
    main_eaton()
