import ee
import time
import os
import geemap
from glob import glob
from functools import reduce
from tqdm import tqdm
# ee.Authenticate(auth_mode='notebook')  # 或 auth_mode='paste'
# ee.Initialize()
def export_image_to_local(image, description, region, scale=10, save_path=None):
    """下载影像到本地文件夹"""
    out_tif = os.path.join(save_path, f"{description}.tif") # 
    if os.path.exists(out_tif):
        print(f"文件 {out_tif} 已存在，跳过下载。")
        return
    os.makedirs(os.path.dirname(out_tif), exist_ok=True)
    # 尝试简化区域边界为bounds，降低复杂度
    if 'coordinates' in region:
        region = ee.Geometry(region).bounds().getInfo()
    
    try:
        geemap.download_ee_image(
            image=image,
            filename=out_tif,
            region=region,
            scale=scale,
            # crs='EPSG:3857',  # 使用WGS84坐标系
            max_tile_size=16,  # 试着更小切片
        )
        print(f"影像已成功下载至 {out_tif}")
    except Exception as e:
        print(f"下载失败，尝试分块下载或导出资产: {e}")

def process_image(image, studyarea):
    """仅添加角度信息，不做波段处理"""
    proj_10m = image.select('B2').projection()  # 使用原始B2（blue）波段确定投影

    solar_zenith = ee.Image.constant(image.get('MEAN_SOLAR_ZENITH_ANGLE'))
    solar_azimuth = ee.Image.constant(image.get('MEAN_SOLAR_AZIMUTH_ANGLE'))
    view_zenith_B8A = ee.Image.constant(image.get('MEAN_INCIDENCE_ZENITH_ANGLE_B8A')).rename('mean_view_zenith')
    view_azimuth_B8A = ee.Image.constant(image.get('MEAN_INCIDENCE_AZIMUTH_ANGLE_B8A')).rename('mean_view_azimuth')


    bands_zenith = ['B1','B2','B3','B4','B5','B6','B7','B8','B8A','B9','B10','B11','B12']

    zenith_numbers = [ee.Number(image.get(f'MEAN_INCIDENCE_ZENITH_ANGLE_{band}')) for band in bands_zenith]
    azimuth_numbers = [ee.Number(image.get(f'MEAN_INCIDENCE_AZIMUTH_ANGLE_{band}')) for band in bands_zenith]

    mean_zenith = reduce(lambda a, b: a.add(b), zenith_numbers).divide(len(bands_zenith))
    mean_azimuth = reduce(lambda a, b: a.add(b), azimuth_numbers).divide(len(bands_zenith))

    # print(mean_azimuth.getInfo())

    # 创建新的常数影像波段
    mean_zenith_img = ee.Image.constant(mean_zenith).rename('mean_incidence_zenith_angle')
    mean_azimuth_img = ee.Image.constant(mean_azimuth).rename('mean_incidence_azimuth_angle')

    angles = ee.Image.cat([solar_zenith, solar_azimuth, mean_zenith_img, mean_azimuth_img, view_zenith_B8A, view_azimuth_B8A])\
        .rename(['SZ', 'SA', 'VZ', 'VA', 'VZ8A', 'VA8A'])\
        .reproject(crs=proj_10m)

    final_img = image.addBands(angles)

    return final_img.clip(studyarea)


def calculate_coverage_ratio(image, studyarea):
    studyarea_geom = studyarea.geometry()
    studyarea_area = studyarea_geom.area(maxError=1).getInfo()

    clipped_img = image.clip(studyarea_geom)

    # 处理掩膜，转为整数二值掩膜
    mask = clipped_img.mask().reduce(ee.Reducer.min()).gt(0).toByte()

    coverage_geom = mask.reduceToVectors(
        geometry=studyarea_geom,
        scale=10,
        geometryType='polygon',
        eightConnected=True,
        labelProperty='mask',
        bestEffort=True,
        maxPixels=1e13
    )

    coverage_area = coverage_geom.geometry().area().getInfo()

    coverage_ratio = coverage_area / studyarea_area if studyarea_area > 0 else 0

    print(f"研究区面积: {studyarea_area:.2f} 平方米")
    print(f"Sentinel-2影像覆盖面积: {coverage_area:.2f} 平方米")
    print(f"覆盖比例: {coverage_ratio:.4f}")

    return coverage_ratio


def find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names=[], save_path="", CLOUDY_PIXEL_PERCENTAGE=90):
    """查找Sentinel-2影像并处理、下载"""
    collection = (
        ee.ImageCollection('COPERNICUS/S2_HARMONIZED')
        .filterBounds(studyarea)
        .filterDate(start_date, end_date)
        .filter(ee.Filter.lt('CLOUDY_PIXEL_PERCENTAGE', CLOUDY_PIXEL_PERCENTAGE))
        # .distinct('system:time_start')  # 仅保留每一天一张影像
    )

    images = collection.toList(collection.size())
    n = images.size().getInfo()

    print(f"找到 {n} 张影像")

    for i in tqdm(range(n), desc=f"处理影像 {studyarea_name}"):
        try:
            img = ee.Image(images.get(i))
            date_str = ee.Date(img.get('system:time_start')).format('YYYYMMdd').getInfo()
            desc = f"{studyarea_name}_S2_{date_str}"
            print(f"处理第{i}张影像: {desc}")
            # coverage_ratio = calculate_coverage_ratio(img, studyarea)
            studyarea_area = studyarea.geometry().area(maxError=1).getInfo()
            image_geom = img.clip(studyarea).geometry()
            intersection = image_geom.intersection(studyarea.geometry(), ee.ErrorMargin(1))
            coverage_ratio = intersection.area(ee.ErrorMargin(1)).divide(studyarea.geometry().area(ee.ErrorMargin(1))).getInfo()

            print(studyarea_area, coverage_ratio)
            if coverage_ratio < 0.9:
                print(f"影像 {desc} 覆盖比例过低，跳过下载。")
                continue
            if desc in exist_names:
                processed_img = process_image(img, studyarea)

                print(f"开始下载影像: {desc}")
                # x = input()
                export_image_to_local(processed_img, desc, studyarea.geometry().getInfo(), 10, save_path)

                # time.sleep(5)
        except Exception as e:
            print(f"处理第{i}张影像时出错: {e}")

def main_santa():
    ee.Initialize()
    studyarea_name = "santa_rosa_wildfire"
    asset_id = f"projects/green-talent-283008/assets/{studyarea_name}"
    studyarea = ee.FeatureCollection(asset_id)

    start_date = '2015-09-17'
    end_date = '2017-11-02'

    file_names = glob(os.path.join("/mnt/E/Dataset/xBD/original/xview2_geotiff.tar/xview2_geotiff/geotiffs/GeoTransform/santa-rosa-wildfire/S2", "*.tif"))
    file_names+= glob(os.path.join("/mnt/E/Dataset/xBD/original/xview2_geotiff.tar/xview2_geotiff/geotiffs/GeoTransform/santa-rosa-wildfire/S2_post", "*.tif"))
    exist_names = [os.path.basename(f).split(".tif")[0].replace("-", "_") for f in file_names]
    print(f"已存在影像文件: {exist_names}")
    # x = input()
    
    save_path = "/mnt/D/PythonProject/BDS12/utils/GEE"
    find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names = exist_names, save_path=save_path)

def main_harvey():
    ee.Initialize()
    for id in [8]: # 2, 3, 4, 6, 7, 
        studyarea_name = f"hurricane_harvey{id}"
        asset_id = f"projects/green-talent-283008/assets/{studyarea_name}"
        studyarea = ee.FeatureCollection(asset_id)

        start_date = '2015-01-01'
        end_date = '2017-09-10'

        file_names = glob(os.path.join("/mnt/E/Dataset/xBD/original/xview2_geotiff.tar/xview2_geotiff/geotiffs/GeoTransform/hurricane-harvey/S2/drive-download-20241126T012201Z-001", "*.tif"))
        file_names+= glob(os.path.join("/mnt/E/Dataset/xBD/original/xview2_geotiff.tar/xview2_geotiff/geotiffs/GeoTransform/hurricane-harvey/S2/drive-download-20241126T031510Z-001", "*.tif"))
        exist_names = [os.path.basename(f).split(".tif")[0].replace("-", "_") for f in file_names]
        # print(f"已存在影像文件: {exist_names}, length: {len(exist_names)}")
        
        save_path = "/mnt/D/PythonProject/BDS12/utils/GEE"

        find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names = exist_names, save_path=save_path, CLOUDY_PIXEL_PERCENTAGE=10)

def main_hawaii():
    ee.Initialize()
    studyarea_name = f"hawaii_wildfire"
    asset_id = f"projects/green-talent-283008/assets/{studyarea_name}"
    studyarea = ee.FeatureCollection(asset_id)

    start_date = '2022-09-01'
    end_date = '2023-08-15'

    file_names = glob(os.path.join("/mnt/E/Dataset/Hawaii/S2_resample", "*.tif"))
    exist_names = [os.path.basename(f).split(".tif")[0].replace("-", "_") for f in file_names]
    print(f"已存在影像文件: {exist_names}, length: {len(exist_names)}")
    
    save_path = "/mnt/D/PythonProject/BDS12/utils/GEE"

    find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names = exist_names, save_path=save_path, CLOUDY_PIXEL_PERCENTAGE=80)

def main_palisades():
    ee.Initialize()
    studyarea_name = f"palisades_fire"
    asset_id = f"projects/green-talent-283008/assets/{studyarea_name}"
    studyarea = ee.FeatureCollection(asset_id)

    start_date = '2024-08-01'
    end_date = '2025-01-15'

    file_names = glob(os.path.join("/mnt/E/Dataset/LA2025/palisades-fire/S2", "*.tif"))
    exist_names = [os.path.basename(f).split(".tif")[0].replace("-", "_") for f in file_names]
    print(f"已存在影像文件: {exist_names}, length: {len(exist_names)}")
    
    save_path = "/mnt/D/PythonProject/BDS12/utils/GEE"

    find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names = exist_names, save_path=save_path, CLOUDY_PIXEL_PERCENTAGE=80)


def main_eaton():
    ee.Initialize()
    studyarea_name = f"eaton-fire"
    asset_id = f"projects/green-talent-283008/assets/{studyarea_name}"
    studyarea = ee.FeatureCollection(asset_id)

    start_date = '2024-08-01'
    end_date = '2025-01-15'

    file_names = glob(os.path.join("/mnt/E/Dataset/LA2025/eaton-fire/S2", "*.tif"))
    exist_names = [os.path.basename(f).split(".tif")[0].replace("-", "_") for f in file_names]
    print(f"已存在影像文件: {exist_names}, length: {len(exist_names)}")
    
    save_path = "/mnt/D/PythonProject/BDS12/utils/GEE"

    find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names = exist_names, save_path=save_path, CLOUDY_PIXEL_PERCENTAGE=80)

def main_palu():
    ee.Initialize()
    studyarea_name = f"palu_tsunami"
    asset_id = f"projects/green-talent-283008/assets/{studyarea_name}"
    studyarea = ee.FeatureCollection(asset_id)

    start_date = '2016-07-10'
    end_date = '2018-12-12'

    file_names = glob(os.path.join("/mnt/E/Dataset/xBD/original/xview2_geotiff.tar/xview2_geotiff/geotiffs/GeoTransform/palu-tsunami/S2/original", "*.tif"))
    exist_names = [os.path.basename(f).split(".tif")[0].replace("-", "_") for f in file_names]
    print(f"已存在影像文件: {exist_names}, length: {len(exist_names)}")
    
    save_path = "/mnt/D/PythonProject/BDS12/utils/GEE"

    find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names = exist_names, save_path=save_path, CLOUDY_PIXEL_PERCENTAGE=80)

def main_gaza():
    ee.Initialize()
    studyarea_name = f"gaza"
    asset_id = f"projects/green-talent-283008/assets/{studyarea_name}"
    studyarea = ee.FeatureCollection(asset_id)

    start_date = '2023-03-01'
    end_date = '2024-09-12'

    file_names = glob(os.path.join("/mnt/E/Dataset/gaza/S2/gaza/resample", "*.tif"))
    exist_names = [os.path.basename(f).split(".tif")[0].replace("-", "_") for f in file_names]
    print(f"已存在影像文件: {exist_names}, length: {len(exist_names)}")
    
    save_path = "/mnt/D/PythonProject/BDS12/utils/GEE"

    find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names = exist_names, save_path=save_path, CLOUDY_PIXEL_PERCENTAGE=100)


def main_turkey():
    ee.Initialize()
    studyarea_name = f"turkey_earthquake3"
    asset_id = f"projects/green-talent-283008/assets/{studyarea_name}"
    studyarea = ee.FeatureCollection(asset_id)

    start_date = '2022-08-01'
    end_date = '2023-02-25'

    file_names = glob(os.path.join("/mnt/E/Dataset/Turkey/Kahramanmara-earthquake/S2_resample", "*.tif"))
    exist_names = [os.path.basename(f).split(".tif")[0].replace("-", "_") for f in file_names]
    print(f"已存在影像文件: {exist_names}, length: {len(exist_names)}")
    
    save_path = "/mnt/D/PythonProject/BDS12/utils/GEE"

    find_S2_data(studyarea, studyarea_name, start_date, end_date, exist_names = exist_names, save_path=save_path, CLOUDY_PIXEL_PERCENTAGE=100)


if __name__ == "__main__":
    # main_santa()
    # main_harvey()
    # main_hawaii()
    # main_palisades()
    # main_eaton()

    main_palu()
    # main_gaza()
    # main_turkey()