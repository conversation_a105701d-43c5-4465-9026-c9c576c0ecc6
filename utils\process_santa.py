import math
from osgeo import gdal, osr
import numpy as np
import os
from glob import glob
import matplotlib.pyplot as plt
from multiprocessing import Pool
import shutil
import random
from tqdm import tqdm
from PIL import Image
from shapely.geometry import Polygon, box
import geopandas as gpd

def tif_resample(input_file, output_file, xRes=0.5, yRes=0.5, dstSRS="EPSG:32611"):
    gdal.Warp(output_file, input_file, xRes=xRes,  yRes=yRes, resampleAlg=gdal.GRA_Bilinear, dstSRS=dstSRS)

def save_to_tif(pred_result, save_path, test_image_path):
    """
    pred_result: 需要保存的图像
    save_path: 保存路径
    test_image_path: 空间参考系
    """
    img = pred_result
    im_geotrans, im_prof = [], []
    tif = gdal.Open(os.path.join(test_image_path))
    im_geotrans.append(tif.GetGeoTransform())
    im_prof.append(tif.GetProjection())
    if 'uint8' in img.dtype.name:
        datatype = gdal.GDT_Byte
    elif 'uint16' in img.dtype.name:
        datatype = gdal.GDT_UInt16
    else:
        datatype = gdal.GDT_Float32
    # print(img.dtype.name)
    #判读数组维数
    if len(img.shape) == 3:
        im_bands, im_height, im_width = img.shape
    else:
        im_bands, (im_height, im_width) = 1, img.shape

    #创建文件
    driver  = gdal.GetDriverByName("GTiff")                #数据类型必须有，因为要计算需要多大内存空间
    dataset = driver.Create(save_path, im_width, im_height, im_bands, datatype)
    dataset.SetGeoTransform(im_geotrans[-1])              #写入仿射变换参数
    dataset.SetProjection(im_prof[-1])                    #写入投影
    if im_bands == 1:
        dataset.GetRasterBand(1).WriteArray(img)  #写入数组数据
    else:
        for i in range(im_bands):
            dataset.GetRasterBand(i+1).WriteArray(img[i])
    del dataset
    # print('保存成功')


def clip_high_tif_2_patch(high_tif_path):
    dataset = gdal.Open(high_tif_path)
    geotransform = dataset.GetGeoTransform()
    projection = dataset.GetProjection()


    if os.path.exists(os.path.join(os.path.dirname(high_tif_path).replace("High/UTM", "label/PNG/hold"), os.path.basename(high_tif_path).replace(".tif", ".png"))):
        mode = "hold"
    if os.path.exists(os.path.join(os.path.dirname(high_tif_path).replace("High/UTM", "label/PNG/test"), os.path.basename(high_tif_path).replace(".tif", ".png"))):
        mode = "test"
    if os.path.exists(os.path.join(os.path.dirname(high_tif_path).replace("High/UTM", "label/PNG/train"), os.path.basename(high_tif_path).replace(".tif", ".png"))):
        mode = "train"
    # print(f"当前影像模式: {mode}")

    image_array = dataset.ReadAsArray()
    image_array = np.array(image_array).astype(np.float32)
    _, h, w = image_array.shape
    # print(h, w)
    # x = input()
    split_withd = 512
    r           = 0
    row         = int(np.ceil((h-r*2)/(split_withd-r*2)))
    col         = int(np.ceil((w-r*2)/(split_withd-r*2)))
    # print(h, w, split_withd, r, row, col)
    for i in range(row):
        for j in range(col):
            if i < (row-1) and j < (col-1):
                x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
                y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd         
            
            #-------------------------------------------------#
            #   裁剪的图像在最后一行
            elif i == row-1 and j < (col-1):
                x1, x2 = h-split_withd, h
                y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd
            
            #-------------------------------------------------#
            #   裁剪的图像在最后一列
            elif i < row-1 and j == (col-1):
                x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
                y1, y2 = w-split_withd, w

            #-------------------------------------------------#
            #   裁剪的图像在右下角
            elif i == row-1 and j == (col-1):
                x1, x2 = h-split_withd, h
                y1, y2 = w-split_withd, w

            min_x = geotransform[0] + y1 * geotransform[1]
            max_y = geotransform[3] + x1 * geotransform[5]
            max_x = min_x + split_withd * geotransform[1]
            min_y = max_y + split_withd * geotransform[5]          


            patch_path = os.path.join(os.path.dirname(high_tif_path).replace("High/UTM", f"datasets_512/{mode}/images"), os.path.basename(high_tif_path).replace(".tif", f"_{i}_{j}.tif"))
            os.makedirs(os.path.dirname(patch_path), exist_ok=True)

            gdal.Warp(patch_path, high_tif_path, outputBounds=(min_x, min_y, max_x, max_y), dstSRS=projection, outputType=gdal.GDT_Byte)


            # 同步裁剪 post_tif, pre_label, post_label

            post_tif_path = high_tif_path.replace("High/UTM", "High/PNG").replace("pre", "post").replace(".tif", ".png")
            post_tif = Image.open(post_tif_path).resize((w, h), resample=Image.Resampling.NEAREST)
            post_tif = np.array(post_tif).astype(np.uint).transpose(2, 0, 1)
            # print(post_tif.shape)
            post_tif = post_tif[:, x1:x2, y1:y2]
            post_tif_save_path = patch_path.replace("pre", "post")
            os.makedirs(os.path.dirname(post_tif_save_path), exist_ok=True)
            # post_tif.save(post_tif_save_path)
            save_to_tif(np.array(post_tif).astype(np.uint), post_tif_save_path, patch_path)


            pre_label_path = high_tif_path.replace("High/UTM", f"label/PNG/{mode}").replace(".tif", ".png")
            pre_label = Image.open(pre_label_path).resize((w, h), resample=Image.Resampling.NEAREST)
            pre_label = np.array(pre_label).astype(np.uint8)
            pre_label = pre_label[x1:x2, y1:y2]
            pre_label_save_path = patch_path.replace("images", "labels").replace("pre", "pre")
            os.makedirs(os.path.dirname(pre_label_save_path), exist_ok=True)
            save_to_tif(pre_label, pre_label_save_path, patch_path)


            post_tif_path = high_tif_path.replace("High/UTM", f"label/PNG/{mode}").replace("pre", "post").replace(".tif", ".png")
            post_label = Image.open(post_tif_path).resize((w, h), resample=Image.Resampling.NEAREST)
            post_label = np.array(post_label).astype(np.uint8)
            post_label = post_label[x1:x2, y1:y2]  # 裁剪对应区域
            post_label_save_path = patch_path.replace("images", "labels").replace("pre", "post")
            os.makedirs(os.path.dirname(post_label_save_path), exist_ok=True)
            save_to_tif(post_label, post_label_save_path, patch_path)
            # print(post_tif.shape, pre_label.shape, post_label.shape)

    # x = input()


def TIF2RGB(tif_path, save_path):
    """将TIF文件转换为RGB图像并保存为PNG格式"""
    dataset = gdal.Open(tif_path)
    if not dataset:
        raise ValueError(f"无法打开TIF文件: {tif_path}")
    
    # 读取波段
    red_band = dataset.GetRasterBand(1).ReadAsArray()
    green_band = dataset.GetRasterBand(2).ReadAsArray()
    blue_band = dataset.GetRasterBand(3).ReadAsArray()

    # 合并波段
    rgb_image = np.stack((red_band, green_band, blue_band), axis=-1)

    # 保存为PNG
    Image.fromarray(rgb_image.astype(np.uint8)).save(save_path)


def classId2rgb(out):
    label_keys = [
        [  0,   0,   0], # 0 非建筑物区域
        [255, 255, 255], # 1 未损毁
        [  0,   0, 255], # 2 Moderate
        [  0, 255,   0], # 3 Severe
        [255,   0,   0], # 4 Destroyed
    ]
    out = np.array(out).astype(np.float32)
    result = np.zeros((out.shape[0], out.shape[1], 3)).astype(np.int8)
    for i in range(len(label_keys)):
        result[out==i, :] = label_keys[i]
    result = Image.fromarray(result.astype(np.uint8))
    return result

def get_tif_bounds(tif_path):
    """获取TIF文件的地理范围 (min_x, min_y, max_x, max_y)"""
    dataset = gdal.Open(tif_path)
    if not dataset:
        raise ValueError(f"无法打开TIF文件: {tif_path}")
    
    gt = dataset.GetGeoTransform()
    width = dataset.RasterXSize
    height = dataset.RasterYSize

    min_x = gt[0]
    max_y = gt[3]
    max_x = min_x + width * gt[1]
    min_y = max_y + height * gt[5]
    
    # gdal以像素中心为基准，所以要确保坐标顺序正确
    if max_x < min_x: min_x, max_x = max_x, min_x
    if max_y < min_y: min_y, max_y = max_y, min_y
    
    return (min_x, min_y, max_x, max_y)

# def align_bounds_to_resolution(bounds, resolution):
#     target_res = 10
#     target_size = 21  # 210米对应的像素大小，10米分辨率下为21个像素
#     """将 bounds 对齐到给定分辨率"""
#     min_x, min_y, max_x, max_y = bounds

#     center_x = (min_x + max_x) / 2
#     center_y = (min_y + max_y) / 2

#     half_extent = (target_size * target_res) / 2  # = 210 / 2 = 105 米

#     aligned_min_x = np.floor((center_x - half_extent) / target_res) * target_res
#     aligned_max_x = aligned_min_x + target_size * target_res

#     aligned_min_y = np.floor((center_y - half_extent) / target_res) * target_res
#     aligned_max_y = aligned_min_y + target_size * target_res

#     return aligned_min_x, aligned_min_y, aligned_max_x, aligned_max_y

def align_bounds_to_resolution(bounds, resolution):
    """将 bounds 对齐到给定分辨率"""
    min_x, min_y, max_x, max_y = bounds
    aligned_min_x = np.floor(min_x / resolution) * resolution
    aligned_min_y = np.floor(min_y / resolution) * resolution
    aligned_max_x = np.ceil(max_x / resolution) * resolution
    aligned_max_y = np.ceil(max_y / resolution) * resolution
    return aligned_min_x, aligned_min_y, aligned_max_x, aligned_max_y

def clip_big_tif_by_small(small_tif_path, big_tif_path, save_path):
    small_bounds = get_tif_bounds(small_tif_path)
    # aligned_bounds  = align_bounds_to_resolution(small_bounds, resolution=8)

    # s_min_x, s_min_y, s_max_x, s_max_y = small_bounds
    
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    options = gdal.WarpOptions(
    format="GTiff",
    outputBounds=small_bounds,
    xRes=8, yRes=8,  # 明确指定输出分辨率
    resampleAlg='near',  # 最近邻，保持原始值（比如分类或整数波段）
    dstNodata=255,
    outputType=gdal.GDT_Float32
    )

    # options = gdal.WarpOptions(format="GTiff", outputBounds=(s_min_x, s_min_y, s_max_x, s_max_y), cutlineBlend=0, dstNodata=255, outputType=gdal.GDT_Float32, options=["PHOTOMETRIC=MINISBLACK"], xRes=8, yRes=8,)
    gdal.Warp(destNameOrDestDS=save_path,  srcDSOrSrcDSTab=big_tif_path,  options=options)


def clip_big_tif_by_small_margin(small_tif_path, big_tif_path, save_path):
    small_bounds = get_tif_bounds(small_tif_path)
    s_min_x, s_min_y, s_max_x, s_max_y = small_bounds
    aligned_left = math.floor(s_min_x / 10) * 10
    aligned_top  = math.ceil(s_max_y  / 10) * 10
    # 计算右下角坐标
    aligned_right = aligned_left + 22*10
    aligned_bottom = aligned_top - 22*10

    output_bounds = (aligned_left, aligned_bottom, aligned_right, aligned_top)
    contains = (aligned_left <= s_min_x and
                aligned_right >= s_max_x and
                aligned_top >= s_max_y and
                aligned_bottom <= s_min_y)    
    if not contains:
        raise ValueError(
            f"[裁剪范围错误] 输出裁剪区域未能完全包含高分图像范围。\n"
            f"输出裁剪 bounds: {output_bounds}\n"
            f"高分影像 bounds: {(s_min_x, s_min_y, s_max_x, s_max_y)} \n"
            f"[保存路径]{save_path}"
        )
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    # options = gdal.WarpOptions(format="GTiff", outputBounds=(s_min_x, s_min_y, s_max_x, s_max_y), cutlineBlend=0, dstNodata=255, outputType=gdal.GDT_Float32, options=["PHOTOMETRIC=MINISBLACK"])
    options = gdal.WarpOptions(
        format="GTiff",
        outputBounds=output_bounds,
        xRes=10, yRes=10,  # 明确指定输出分辨率
        resampleAlg='near',  # 最近邻，保持原始值（比如分类或整数波段）
        dstNodata=None,
        outputType=gdal.GDT_Float32
    )
    gdal.Warp(destNameOrDestDS=save_path,  srcDSOrSrcDSTab=big_tif_path,  options=options)


if __name__ == "__main__":
    event_name = "santa-rosa-wildfire"
    event_base_path = f"/mnt/E/Dataset/xBD/original/xview2_geotiff.tar/xview2_geotiff/geotiffs/GeoTransform/{event_name}/"

    # 1 对高分的84转 0.625米 的 UTM
    # small_tif_paths = glob(os.path.join(event_base_path, "High/WGS84/*pre*.tif"))
    # print(f"找到 {len(small_tif_paths)} 张小TIF影像")
    # for small_tif_path in tqdm(small_tif_paths, desc="Resampling small TIFs"):
    #     save_path = small_tif_path.replace("WGS84", "UTM_0.625")
    #     os.makedirs(os.path.dirname(save_path), exist_ok=True)
    #     tif_resample(small_tif_path, save_path, xRes=0.625, yRes=0.625, dstSRS="EPSG:32610")

    # 2 将UTM_0.625数据裁剪为 384x384 的小块
    # small_tif_paths = glob(os.path.join(event_base_path, "High/PNG/*pre*.png"))
    # print(f"找到 {len(small_tif_paths)} 张小TIF影像")
    # args = []
    # for small_tif_path in tqdm(small_tif_paths, desc="Clipping small TIFs"):
    #     small_tif_path = small_tif_path.replace("/PNG/", "/UTM/").replace(".png", ".tif")
    #     print(small_tif_path)
    #     # clip_high_tif_2_patch(small_tif_path)
    #     args.append((small_tif_path,))
    # with Pool(os.cpu_count()) as pool:
    #     list(tqdm(pool.starmap(clip_high_tif_2_patch, args), total=len(args), desc="clip high tif"))


    # 3 将小块的TIF转换为RGB图像
    # small_tif_paths = glob(os.path.join(event_base_path, "datasets_512/**/images/**.tif"))
    # print(f"找到 {len(small_tif_paths)} 张小TIF影像")
    # for small_tif_path in tqdm(small_tif_paths, desc="Converting TIFs to RGB"):
    #     save_path = small_tif_path.replace("images", "images_RGB").replace(".tif", ".png")
    #     os.makedirs(os.path.dirname(save_path), exist_ok=True)
    #     TIF2RGB(small_tif_path, save_path)

    # # 4 将小块的Label转换为PNG图像
    # small_tif_paths = glob(os.path.join(event_base_path, "datasets_512/**/labels/**.tif"))
    # print(f"找到 {len(small_tif_paths)} 张小TIF影像")
    # for small_tif_path in tqdm(small_tif_paths, desc="Converting Labels to PNG"):
    #     save_path = small_tif_path.replace("labels", "labels_RGB").replace(".tif", ".png")
    #     os.makedirs(os.path.dirname(save_path), exist_ok=True)
    #     label_array = gdal.Open(small_tif_path).ReadAsArray()
    #     label_array = classId2rgb(label_array)
    #     label_array.save(save_path)


    # 5裁剪 S2 数据
    small_tif_paths = glob(os.path.join(event_base_path, "dataset/train/images/*pre*.tif"))
    big_tif_paths = glob(os.path.join(event_base_path, "S2_Python/**.tif"))
    small_tif_paths.sort()
    big_tif_paths.sort()
    args = []
    print(len(small_tif_paths), len(big_tif_paths))
    small_count = 0
    for small_tif_path in tqdm(small_tif_paths):
        # small_bounds = get_tif_bounds(small_tif_path)
        big_count = 0
        small_count += 1
        for big_id, big_tif_path in enumerate(big_tif_paths):
            # big_bounds = get_tif_bounds(big_tif_path)
            # if is_small_in_big(small_bounds, big_bounds):
            if len(big_tif_paths)-1 == big_id:
                save_path = os.path.dirname(small_tif_path.replace("/images/", "/S2_Python_Margin/S2_post/"))
                save_name = os.path.basename(small_tif_path).split(".")[0].replace("pre", "post") + "_S2_" + os.path.basename(big_tif_path).split("_S2_")[-1]
            else:
                save_path = os.path.dirname(small_tif_path.replace("/images/", "/S2_Python_Margin/S2/"))
                save_name = os.path.basename(small_tif_path).split(".")[0] + "_S2_" + os.path.basename(big_tif_path).split("_S2_")[-1]

            os.makedirs(save_path, exist_ok=True)
            big_count += 1
            # print(small_tif_path, big_tif_path, os.path.join(save_path, save_name))
            args.append((small_tif_path, big_tif_path, os.path.join(save_path, save_name)))
    #     if len(args) > 100:
    #         break
    print(f"Total: {len(args)}")
    num_workers = os.cpu_count()
    with Pool(num_workers) as pool:
        list(tqdm(pool.starmap(clip_big_tif_by_small_margin, args), total=len(args), desc="clip S2 tif"))
        