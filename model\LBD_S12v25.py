import os
from thop import profile
import torch
import torch.nn as nn
import torch.nn.functional as F
import timm
from timm.models.layers import DropPath, to_2tuple, trunc_normal_
from timm.models.registry import register_model
from timm.models.vision_transformer import _cfg
from timm.models.registry import register_model
import math
import warnings
warnings.filterwarnings('ignore')

from einops import rearrange
from dcn_v2 import DCN
from model.UANet import *

class HRNet_single(nn.Module):
    def __init__(self, pretrained=False, s2_inchannel=18, num_classes=3):
        super(HRNet_single, self).__init__()
        self.num_classes = num_classes
        self.rgb_backbone = timm.create_model('hrnet_w48', pretrained=pretrained, features_only=True, out_indices=(1, 2, 3, 4))
        self.pre_train_model_path = "./net/hrnetv2_w48_imagenet_pretrained.pth"
        last_inp_channels = np.int32(np.sum(self.rgb_backbone.feature_info.channels()))
        decoder_channnel = self.rgb_backbone.feature_info.channels()[0]
        # print(self.rgb_backbone.feature_info.channels())

        self.rgb_decoder_layer = nn.Sequential(
            nn.Conv2d(in_channels=last_inp_channels, out_channels=decoder_channnel, kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=decoder_channnel, out_channels=64, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(64, momentum= 0.1),
            nn.ReLU(inplace=True),
        )
        self.class_layer1 = nn.Conv2d(in_channels=decoder_channnel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)

    def _load_weight(self, net):
        if self.pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.pre_train_model_path)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
    
    def decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x

    def forward(self, images, device, mode="pre", output={}):
        B, C, H, W = images.shape
        high_features = self.rgb_backbone(images.to(device).to(torch.float32))
        high_features = self.decoder(high_features, self.rgb_decoder_layer)
        high_outs = self.class_layer1(high_features)
        high_outs = F.interpolate(high_outs, size=(H, W), mode='bilinear', align_corners=True)
        output[f"{mode}_high_building"] = high_outs
        output[f"{mode}_high_features"] = high_features
        return output

class Decoder(nn.Module):
    def __init__(self, decoder_channels_list, decoder_channel):
        super().__init__()
        # Lateral convs：统一通道数
        decoder_channels_list = decoder_channels_list[::-1]
        self.lateral_convs = nn.ModuleList([BasicConv2d(in_ch, decoder_channels_list[-1], 3, 1, 1) for in_ch in decoder_channels_list])

        # Plus convs：输出所有层
        self.conv1 = nn.Sequential(
            BasicConv2d(decoder_channels_list[-1], decoder_channels_list[-1], 3, 1, 1),
            nn.UpsamplingBilinear2d(scale_factor=2),
            BasicConv2d(decoder_channels_list[-1], decoder_channels_list[-1], 3, 1, 1),
            nn.UpsamplingBilinear2d(scale_factor=2),
            BasicConv2d(decoder_channels_list[-1], decoder_channel, 3, 1, 1),         
        )

        self.conv2 = nn.Sequential(
            BasicConv2d(decoder_channels_list[-1], decoder_channels_list[-1], 3, 1, 1),
            nn.UpsamplingBilinear2d(scale_factor=2),
            BasicConv2d(decoder_channels_list[-1], decoder_channel, 3, 1, 1),       
        )

        self.conv3 =  BasicConv2d(decoder_channels_list[-1], decoder_channel, 3, 1, 1)

    def forward(self, features):
        # reverse: 从 top-down 顺序处理，如 [C5, C4, C3, C2]
        features = features[::-1]
        laterals = [l_conv(f) for l_conv, f in zip(self.lateral_convs, features)]

        # top-down 融合
        for i in range(1, len(laterals)):
            up = F.interpolate(laterals[i - 1], size=laterals[i].shape[2:], mode='bilinear', align_corners=False)
            laterals[i] = laterals[i] + up  # 融合上一级特征

        # plus 层
        out = self.conv1(laterals[1]) + self.conv2(laterals[2]) + self.conv3(laterals[3])

        return out
    
class DCN_guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.dcn = DCN(decoder_channel*2, decoder_channel, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, current_frames, referent_frames):
        B, frame, C, H, W = referent_frames.shape
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

        assert current_frames.shape == referent_frames.shape

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")

        # s2_angle = s2_angle - s2_angle[:, -1:,]
        # s2_angle = rearrange(s2_angle, "b t c h w -> (b t) c h w")

        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)

        fusion_frames, offset = self.dcn(fusion_frames)
        fusion_frames = self.relu(fusion_frames)

        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames, offset

class time_fusion(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        frame = 16
        self.conv_time_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=int(decoder_channel*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.BatchNorm2d(decoder_channel*frame),
            nn.ReLU(inplace=True),
        )
        self.conv_spatial_fusion = BasicConv2d(decoder_channel, decoder_channel, 3, 1, 1)

    def forward(self, x):
        B, frame, C, H, W = x.shape
        x = rearrange(x, "b t c h w -> b c (h w) t", b=B, t=frame, c=C, h=H, w=W)
        fusion_frames = rearrange(self.conv_time_fusion(x).squeeze(-1), "b (t c) (h w) -> (b t) c h w", b=B, t=frame, c=C, h=H, w=W)
        fusion_frames = rearrange(self.conv_spatial_fusion(fusion_frames), "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)
        return fusion_frames

class ResBlock(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.conv_path = nn.Sequential(
            BasicConv2d(decoder_channel, decoder_channel, 3, 1, 1),
            BasicConv2d(decoder_channel, decoder_channel, 3, 1, 1)
        )
        self.res = nn.Conv2d(decoder_channel, decoder_channel, kernel_size=1)
        self.fusion = BasicConv2d(decoder_channel, decoder_channel, 3, 1, 1)

    def forward(self, x):
        x = self.conv_path(x) + self.res(x)
        x = self.fusion(x)
        return x

class SuperResolution(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()

        self.ts = time_fusion(decoder_channel)
        self.sr = nn.Sequential(
            nn.PixelShuffle(upscale_factor=2),
            BasicConv2d(decoder_channel*4, decoder_channel, 3, 1, 1),
            ResBlock(decoder_channel),
        )

    def forward(self, x):
        x = self.ts(x)
        x = self.sr(rearrange(x, "b t c h w -> b (t c) h w"))
        return x

class S2_Building_Damage(nn.Module):
    def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        decoder_channel = 64

        self.s2_backbone, self.s2_decoder_layer = self._construct_low_backbone(s2_inchannel, num_classes)
        self.s2_decoder = Decoder(decoder_channels_list=self.s2_backbone.feature_info.channels(), decoder_channel=decoder_channel)

        self.fcn = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])
        self.offset_guide = DCN_guide(decoder_channel)

        self.sr_net = SuperResolution(decoder_channel)
        # self.s2_last_layer = BasicConv2d(decoder_channel, decoder_channel, 3, 1, 1)
        self.building_layer = nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes+1, kernel_size=1, stride=1, padding=0)

    def _construct_low_backbone(self, inchannels, num_classes):
        net = HRNet_single(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BuildingDamage/result/xBD_512/S2_HRNet2_single/27.pt"
        net = self._load_weight(net, pre_train_model_path)
        backbone = net.rgb_backbone
        backbone.conv1.stride = 1
        backbone.conv2.stride = 1
        conv1_weights = backbone.conv1.weight
        conv1_weights_mean = conv1_weights.mean(dim=1, keepdim=True)
        new_conv1_weights = conv1_weights_mean.repeat(1, inchannels, 1, 1)
        backbone.conv1.in_inchannel = inchannels
        backbone.conv1.weight.data = new_conv1_weights        
        return backbone, net.rgb_decoder_layer

    def _decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net

    def _feature_transfer(self, time_serise_feature):
        B, total_frame, C, H, W = time_serise_feature.shape
        # print("shape is", B, total_frame, C, H, W)
        aligned_features = []
        T15 = time_serise_feature[:, -2]
        
        for t in range(total_frame):  # 对于T1到T17
            curr_feat = time_serise_feature[:, t]  # [B, C, H, W]
            feat_pair = torch.cat([curr_feat, T15], dim=1)
            A = self.fcn[t](feat_pair)
            aligned_feat = torch.mul(curr_feat, A).squeeze(1)
            aligned_features.append(aligned_feat)
        aligned_features = torch.stack(aligned_features, dim=1) # [B, 17, C, H, W]
        return aligned_features
 
    def _encoder_feature_constraint(self, inputs, device, high_features, low_decoder_features, outputs):
        pre_label = inputs[3].unsqueeze(1).to(device).to(torch.float32)
        post_label = inputs[4].unsqueeze(1).to(device).to(torch.float32)
        # pseudo_label = 1 - inputs[5].unsqueeze(1).unsqueeze(1).to(device).to(torch.long)
        B, _, H, W = pre_label.shape
        B, frame, _, h, w = low_decoder_features.shape

        high_features = F.interpolate(high_features, size=(h, w), mode='bilinear', align_corners=True)
        outputs["loss"] = F.mse_loss(F.normalize(low_decoder_features[:, -2], dim=1), F.normalize(high_features.detach(), dim=1))

        T15 = F.interpolate(low_decoder_features[:, -2], size=(H, W), mode='bilinear')
        T16 = F.interpolate(low_decoder_features[:, -1], size=(H, W), mode='bilinear')

        pre_label = torch.where(pre_label == 1, 1, 0)
        post_label = torch.where(post_label == 1, 0, 1)#*pseudo_label

        # 计算损毁区域
        # outputs["loss"] += F.mse_loss(F.normalize(T16 * post_label), F.normalize(T15.detach() * post_label), reduction='mean')

        for frame_id in range(frame - 2):
            frame_feature = F.normalize(low_decoder_features[:, frame_id])
            outputs["loss"] += F.mse_loss(frame_feature, F.normalize(low_decoder_features[:, -2].detach()), reduction='mean')

        return outputs

    def forward(self, inputs, device, outputs, H, W):
        s2_images = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
        B, frame, C, h, w = s2_images.shape

        low_encoder_features_list = self.s2_backbone(s2_images.view(B*frame, C, h, w))
        # for low_encoder_features in low_encoder_features_list:
        #     print(f"low_encoder_features:{low_encoder_features.shape}")
        # low_encoder_features = self.s2_decoder(low_encoder_features_list).view(B, frame, -1, h, w)
        # print(f"low_encoder_features:{low_encoder_features.shape}")
        low_encoder_features = self._decoder(low_encoder_features_list, self.s2_decoder_layer).view(B, frame, -1, h, w)

        low_encoder_features = self._feature_transfer(low_encoder_features)
        low_pre_features_offset, offset = self.offset_guide(current_frames=low_encoder_features[:, -2], referent_frames=low_encoder_features[:, :-1])
        low_encoder_features = torch.cat((low_pre_features_offset, low_encoder_features[:, -1:]), dim=1)

        if self.training:
            outputs = self._encoder_feature_constraint(inputs, device, outputs["pre_high_features"], low_encoder_features, outputs)

        low_pre_features = self.sr_net(low_pre_features_offset)
        # low_pre_features = self.s2_last_layer(low_pre_features)
        low_outs = self.building_layer(low_pre_features)
        outputs["pre_low_features"] = F.interpolate(low_pre_features, size=(H//4, W//4), mode='bilinear', align_corners=True)
        outputs["pre_low_building"] = F.interpolate(low_outs[:,0:2], size=(H, W), mode='bilinear', align_corners=True)
        outputs["pre_low_gray"] = F.interpolate(low_outs[:, 2:], size=(H, W), mode='bilinear', align_corners=True)

        outputs["post_low_features"] = F.interpolate(low_pre_features, size=(H//4, W//4), mode='bilinear', align_corners=True)
        outputs["post_low_damage"] = F.interpolate(low_outs[:,0:2], size=(H, W), mode='bilinear', align_corners=True)
        return outputs



class LBDv25_S2(nn.Module):
    def __init__(self, backbone="pvt_v2_b5_s2", s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        # 1 High RGB Net
        decoder_channel = 64
        self.high_net = UANet_pvt(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/13.pt"
        self.high_net = self._load_weight(self.high_net, pre_train_model_path)

        self.low_net = S2_Building_Damage(s2_inchannel=s2_inchannel, frame=frame, num_classes=num_classes, damage_classes=damage_classes)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
 

    def forward(self, inputs, device):
        outputs = {}
        # with torch.no_grad():
        B, _, H, W = inputs[0].to(device).to(torch.float32).shape
        outputs = self.high_net(inputs[0].to(device).to(torch.float32), device, mode="pre", outputs=outputs)
        outputs = self.low_net(inputs, device, outputs, H, W)

        return outputs



if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '3'
    # deep_model = UANet_Sentinel().cuda()

    deep_model = LBDv25_S2(backbone="pvt_v2_b2_s2", s2_inchannel=17, frame=16, num_classes=2, damage_classes=2).cuda()
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384).cuda()
    low_images1 = torch.rand(bs, 16, 17, 48, 48).cuda()
    low_images2 = torch.rand(bs, 1, 17, 48, 48).cuda()
    pre_label = torch.rand(bs, 384, 384).cuda()
    post_label = torch.rand(bs, 384, 384).cuda()
    inputs = [hight_image, low_images1, low_images2, pre_label, post_label]
    device = torch.device("cuda")

    outputss = deep_model(inputs, device)

    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
