import torch
import torch.nn as nn
import timm
import torch.nn.functional as F
from thop import profile

class MLPDecoderHead(nn.Module):
    def __init__(self, in_channels_list, decoder_dim, num_classes):
        super().__init__()
        self.linear_layers = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_ch, decoder_dim, kernel_size=3, padding=1, stride=1),
                nn.BatchNorm2d(decoder_dim),
                nn.ReLU(inplace=True)
            ) for in_ch in in_channels_list
        ])

        self.fuse = nn.Sequential(
            nn.Conv2d(decoder_dim * len(in_channels_list), decoder_dim, kernel_size=3, padding=1, stride=1),
            nn.BatchNorm2d(decoder_dim),
            nn.ReLU(inplace=True)
        )

        self.classifier = nn.Conv2d(decoder_dim, num_classes, kernel_size=1)

    def forward(self, features):
        sizes = features[0].shape[2:]

        processed = [
            F.interpolate(self.linear_layers[i](feat), size=sizes, mode='bilinear', align_corners=False)
            for i, feat in enumerate(features)
        ]

        fused = torch.cat(processed, dim=1)
        fused = self.fuse(fused)
        out = self.classifier(fused)

        return out, fused


class PVTv2_Segmentation(nn.Module):
    def __init__(self, backbone_name='pvt_v2_b5', decoder_dim=128, num_classes=6):
        super().__init__()
        self.backbone = timm.create_model(backbone_name, pretrained=False, features_only=True)
        in_channels_list = [f['num_chs'] for f in self.backbone.feature_info]
        self.decoder = MLPDecoderHead(in_channels_list, decoder_dim, num_classes)

        self.pre_train_model_path = "/mnt/d2/zxq/BDS12/model/PTH/pvt_v2_b5.pth"
        self.backbone = self._load_weight(self.backbone)

    def _load_weight(self, net):
        if self.pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.pre_train_model_path)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net

    def forward(self, images, device, mode="pre", outputs={}):
        B, C, H, W = images.shape
        feats = self.backbone(images)
        seg_output, fused_feature = self.decoder(feats)
        seg_output = F.interpolate(seg_output, size=(H, W), mode='bilinear', align_corners=True)
        outputs[f"{mode}_high_building"] = seg_output
        outputs[f"{mode}_high_features"] = fused_feature
        return outputs
    

if __name__ == '__main__':
    model = PVTv2_Segmentation(backbone_name='pvt_v2_b5', decoder_dim=256, num_classes=6)
    model.eval()

    dummy_input = torch.randn(1, 3, 384, 384)
    outputs = model(dummy_input)

    print("Segmentation output:", outputs["pre_high_building"].shape)       # [1, 6, 128, 128] (depends on feature size)
    print("Fused feature:", outputs["pre_high_features"].shape)           # [1, 256, 128, 128]

    flops, params = profile(model, inputs=(dummy_input, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
