"""
Building Segmentation Residual Diffusion Denoising Model (Building-RDDM)
基于RDDM思想的建筑物分割边缘优化模型

主要功能：
- 输入低分辨率建筑物分割结果（边缘圆滑）
- 输出高质量建筑物分割结果（边缘清晰）
- 使用残差扩散模型学习从低质量到高质量的映射

作者：基于CVPR2024 RDDM工作改进
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
from PIL import Image
import os
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt

# 导入RDDM相关模块
from res_df import UnetRes, ResidualDiffusion, Trainer, normalize_to_neg_one_to_one, unnormalize_to_zero_to_one


class BuildingSegmentationDataset(torch.utils.data.Dataset):
    """
    建筑物分割数据集
    """
    def __init__(self, low_seg_paths, high_seg_paths, image_size=256, mode='train'):
        """
        Args:
            low_seg_paths: 低质量分割结果路径列表
            high_seg_paths: 高质量分割结果路径列表  
            image_size: 图像尺寸
            mode: 'train' 或 'test'
        """
        self.low_seg_paths = low_seg_paths
        self.high_seg_paths = high_seg_paths
        self.image_size = image_size
        self.mode = mode
        
        assert len(low_seg_paths) == len(high_seg_paths), "Low and high seg paths must have same length"
    
    def __len__(self):
        return len(self.low_seg_paths)
    
    def load_segmentation(self, path):
        """加载分割结果"""
        if path.endswith('.tif') or path.endswith('.tiff'):
            # 使用GDAL加载TIFF文件
            from osgeo import gdal
            dataset = gdal.Open(path)
            seg = dataset.ReadAsArray()
            if len(seg.shape) == 3:
                seg = seg[0]  # 取第一个通道
        else:
            # 使用PIL加载其他格式
            seg = np.array(Image.open(path).convert('L'))
        
        # 归一化到[0,1]
        seg = seg.astype(np.float32)
        if seg.max() > 1:
            seg = seg / 255.0
            
        return seg
    
    def __getitem__(self, idx):
        # 加载低质量和高质量分割结果
        low_seg = self.load_segmentation(self.low_seg_paths[idx])
        high_seg = self.load_segmentation(self.high_seg_paths[idx])
        
        # 调整尺寸
        low_seg = cv2.resize(low_seg, (self.image_size, self.image_size), interpolation=cv2.INTER_NEAREST)
        high_seg = cv2.resize(high_seg, (self.image_size, self.image_size), interpolation=cv2.INTER_NEAREST)
        
        # 转换为tensor并添加通道维度
        low_seg = torch.from_numpy(low_seg).unsqueeze(0)  # [1, H, W]
        high_seg = torch.from_numpy(high_seg).unsqueeze(0)  # [1, H, W]
        
        # 数据增强（仅训练时）
        if self.mode == 'train':
            # 随机翻转
            if torch.rand(1) > 0.5:
                low_seg = torch.flip(low_seg, [2])
                high_seg = torch.flip(high_seg, [2])
            if torch.rand(1) > 0.5:
                low_seg = torch.flip(low_seg, [1])
                high_seg = torch.flip(high_seg, [1])
            
            # 随机旋转90度
            if torch.rand(1) > 0.5:
                k = torch.randint(1, 4, (1,)).item()
                low_seg = torch.rot90(low_seg, k, [1, 2])
                high_seg = torch.rot90(high_seg, k, [1, 2])
        
        return [high_seg, low_seg]  # [target, condition]


class BuildingRDDM:
    """
    建筑物分割残差扩散去噪模型
    """
    def __init__(self, 
                 image_size=256,
                 channels=1,  # 分割图为单通道
                 dim=64,
                 dim_mults=(1, 2, 4, 8),
                 timesteps=1000,
                 sampling_timesteps=250,
                 loss_type='l1',
                 objective='pred_res_noise',
                 device='cuda'):
        
        self.device = device
        self.image_size = image_size
        self.channels = channels
        
        # 创建UNet模型（残差版本）
        self.model = UnetRes(
            dim=dim,
            channels=channels,
            dim_mults=dim_mults,
            condition=True,  # 启用条件生成
            input_condition=False,
            share_encoder=1  # 共享编码器
        )
        
        # 创建扩散模型
        self.diffusion = ResidualDiffusion(
            self.model,
            image_size=image_size,
            timesteps=timesteps,
            sampling_timesteps=sampling_timesteps,
            loss_type=loss_type,
            objective=objective,
            condition=True,
            sum_scale=0.01  # 条件扩散的噪声尺度
        )
        
        self.diffusion.to(device)
    
    def train_model(self, 
                   train_dataset,
                   val_dataset=None,
                   batch_size=8,
                   num_epochs=100,
                   learning_rate=1e-4,
                   save_every=10,
                   results_folder='./results/building_rddm'):
        """
        训练模型
        """
        # 创建数据加载器
        train_loader = torch.utils.data.DataLoader(
            train_dataset, 
            batch_size=batch_size, 
            shuffle=True,
            num_workers=4,
            pin_memory=True
        )
        
        if val_dataset:
            val_loader = torch.utils.data.DataLoader(
                val_dataset,
                batch_size=batch_size,
                shuffle=False,
                num_workers=4
            )
        
        # 创建优化器
        optimizer = torch.optim.Adam(self.diffusion.parameters(), lr=learning_rate)
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=num_epochs)
        
        # 创建结果文件夹
        os.makedirs(results_folder, exist_ok=True)
        
        best_loss = float('inf')
        
        for epoch in range(num_epochs):
            # 训练阶段
            self.diffusion.train()
            train_loss = 0.0
            
            pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs}')
            for batch_idx, batch in enumerate(pbar):
                # batch: [high_seg, low_seg]
                batch = [item.to(self.device) for item in batch]
                
                optimizer.zero_grad()
                
                # 计算损失
                loss = self.diffusion(batch)
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.diffusion.parameters(), 1.0)
                
                optimizer.step()
                
                train_loss += loss.item()
                pbar.set_postfix({'loss': loss.item()})
            
            train_loss /= len(train_loader)
            
            # 验证阶段
            if val_dataset:
                self.diffusion.eval()
                val_loss = 0.0
                
                with torch.no_grad():
                    for batch in val_loader:
                        batch = [item.to(self.device) for item in batch]
                        loss = self.diffusion(batch)
                        val_loss += loss.item()
                
                val_loss /= len(val_loader)
                print(f'Epoch {epoch+1}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')
                
                # 保存最佳模型
                if val_loss < best_loss:
                    best_loss = val_loss
                    torch.save({
                        'model_state_dict': self.diffusion.state_dict(),
                        'optimizer_state_dict': optimizer.state_dict(),
                        'epoch': epoch,
                        'loss': val_loss
                    }, os.path.join(results_folder, 'best_model.pt'))
            else:
                print(f'Epoch {epoch+1}: Train Loss: {train_loss:.6f}')
            
            # 定期保存模型
            if (epoch + 1) % save_every == 0:
                torch.save({
                    'model_state_dict': self.diffusion.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'loss': train_loss
                }, os.path.join(results_folder, f'model_epoch_{epoch+1}.pt'))
                
                # 生成样本进行可视化
                self.generate_samples(val_dataset if val_dataset else train_dataset, 
                                    results_folder, epoch+1)
            
            scheduler.step()
    
    def generate_samples(self, dataset, save_folder, epoch, num_samples=4):
        """生成样本进行可视化"""
        self.diffusion.eval()
        
        with torch.no_grad():
            # 随机选择样本
            indices = torch.randperm(len(dataset))[:num_samples]
            
            fig, axes = plt.subplots(num_samples, 3, figsize=(12, 4*num_samples))
            if num_samples == 1:
                axes = axes.reshape(1, -1)
            
            for i, idx in enumerate(indices):
                # 获取数据
                high_seg, low_seg = dataset[idx]
                low_seg = low_seg.unsqueeze(0).to(self.device)  # [1, 1, H, W]
                
                # 生成高质量分割
                generated = self.diffusion.sample(x_input=low_seg, batch_size=1, last=True)
                generated = generated[-1].cpu()  # 取最后一个结果
                
                # 可视化
                axes[i, 0].imshow(low_seg.cpu().squeeze(), cmap='gray')
                axes[i, 0].set_title('Low Quality Input')
                axes[i, 0].axis('off')
                
                axes[i, 1].imshow(generated.squeeze(), cmap='gray')
                axes[i, 1].set_title('Generated High Quality')
                axes[i, 1].axis('off')
                
                axes[i, 2].imshow(high_seg.squeeze(), cmap='gray')
                axes[i, 2].set_title('Ground Truth')
                axes[i, 2].axis('off')
            
            plt.tight_layout()
            plt.savefig(os.path.join(save_folder, f'samples_epoch_{epoch}.png'), dpi=150)
            plt.close()
    
    def load_model(self, checkpoint_path):
        """加载模型"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        self.diffusion.load_state_dict(checkpoint['model_state_dict'])
        print(f"Model loaded from {checkpoint_path}")
    
    def enhance_segmentation(self, low_seg_input):
        """
        增强分割结果
        Args:
            low_seg_input: 低质量分割输入 [B, 1, H, W] 或 [1, H, W]
        Returns:
            enhanced_seg: 增强后的分割结果
        """
        self.diffusion.eval()
        
        with torch.no_grad():
            if len(low_seg_input.shape) == 3:
                low_seg_input = low_seg_input.unsqueeze(0)
            
            low_seg_input = low_seg_input.to(self.device)
            
            # 生成高质量分割
            enhanced = self.diffusion.sample(x_input=low_seg_input, 
                                           batch_size=low_seg_input.shape[0], 
                                           last=True)
            enhanced = enhanced[-1]  # 取最后一个结果
            
            return enhanced


def create_sample_data(save_dir='./sample_data', num_samples=100):
    """
    创建示例数据用于测试
    生成模拟的低质量和高质量建筑物分割数据
    """
    os.makedirs(save_dir, exist_ok=True)
    os.makedirs(os.path.join(save_dir, 'low_seg'), exist_ok=True)
    os.makedirs(os.path.join(save_dir, 'high_seg'), exist_ok=True)
    
    for i in range(num_samples):
        # 创建高质量分割（清晰边缘）
        high_seg = np.zeros((256, 256), dtype=np.uint8)
        
        # 添加一些矩形建筑物
        num_buildings = np.random.randint(2, 6)
        for _ in range(num_buildings):
            x1 = np.random.randint(20, 200)
            y1 = np.random.randint(20, 200)
            w = np.random.randint(20, 60)
            h = np.random.randint(20, 60)
            x2 = min(x1 + w, 235)
            y2 = min(y1 + h, 235)
            high_seg[y1:y2, x1:x2] = 255
        
        # 创建低质量分割（模糊边缘）
        low_seg = cv2.GaussianBlur(high_seg, (15, 15), 3)
        low_seg = cv2.erode(low_seg, np.ones((3,3), np.uint8), iterations=1)
        low_seg = cv2.dilate(low_seg, np.ones((5,5), np.uint8), iterations=1)
        
        # 保存
        Image.fromarray(high_seg).save(os.path.join(save_dir, 'high_seg', f'{i:04d}.png'))
        Image.fromarray(low_seg).save(os.path.join(save_dir, 'low_seg', f'{i:04d}.png'))
    
    print(f"Created {num_samples} sample pairs in {save_dir}")


if __name__ == "__main__":
    # 示例使用
    print("Building Segmentation RDDM - 建筑物分割残差扩散去噪模型")
    
    # 创建示例数据
    create_sample_data('./sample_data', 200)
    
    # 准备数据路径
    data_dir = './sample_data'
    low_seg_paths = sorted([os.path.join(data_dir, 'low_seg', f) 
                           for f in os.listdir(os.path.join(data_dir, 'low_seg'))])
    high_seg_paths = sorted([os.path.join(data_dir, 'high_seg', f) 
                            for f in os.listdir(os.path.join(data_dir, 'high_seg'))])
    
    # 划分训练和验证集
    split_idx = int(0.8 * len(low_seg_paths))
    train_low = low_seg_paths[:split_idx]
    train_high = high_seg_paths[:split_idx]
    val_low = low_seg_paths[split_idx:]
    val_high = high_seg_paths[split_idx:]
    
    # 创建数据集
    train_dataset = BuildingSegmentationDataset(train_low, train_high, mode='train')
    val_dataset = BuildingSegmentationDataset(val_low, val_high, mode='test')
    
    print(f"Train samples: {len(train_dataset)}, Val samples: {len(val_dataset)}")
    
    # 创建模型
    model = BuildingRDDM(
        image_size=256,
        channels=1,
        dim=64,
        timesteps=1000,
        sampling_timesteps=250
    )
    
    # 训练模型
    print("Starting training...")
    model.train_model(
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        batch_size=4,
        num_epochs=50,
        learning_rate=1e-4,
        save_every=10,
        results_folder='./results/building_rddm'
    )
    
    print("Training completed!")
