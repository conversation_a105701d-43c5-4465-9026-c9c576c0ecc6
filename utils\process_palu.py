import math
from osgeo import gdal, osr
import numpy as np
import os
from glob import glob
import matplotlib.pyplot as plt
from multiprocessing import Pool
import shutil
import random
from tqdm import tqdm
from PIL import Image
from shapely.geometry import Polygon, box
import geopandas as gpd


def get_tif_bounds(tif_path):
    """获取TIF文件的地理范围 (min_x, min_y, max_x, max_y)"""
    dataset = gdal.Open(tif_path)
    if not dataset:
        raise ValueError(f"无法打开TIF文件: {tif_path}")
    
    gt = dataset.GetGeoTransform()
    width = dataset.RasterXSize
    height = dataset.RasterYSize

    min_x = gt[0]
    max_y = gt[3]
    max_x = min_x + width * gt[1]
    min_y = max_y + height * gt[5]
    
    # gdal以像素中心为基准，所以要确保坐标顺序正确
    if max_x < min_x: min_x, max_x = max_x, min_x
    if max_y < min_y: min_y, max_y = max_y, min_y
    
    return (min_x, min_y, max_x, max_y)


def clip_big_tif_by_small_margin(small_tif_path, big_tif_path, save_path):
    small_bounds = get_tif_bounds(small_tif_path)
    s_min_x, s_min_y, s_max_x, s_max_y = small_bounds
    aligned_left = math.floor(s_min_x / 10) * 10
    aligned_top  = math.ceil(s_max_y  / 10) * 10
    # 计算右下角坐标
    aligned_right = aligned_left + 22*10
    aligned_bottom = aligned_top - 22*10

    output_bounds = (aligned_left, aligned_bottom, aligned_right, aligned_top)
    contains = (aligned_left <= s_min_x and
                aligned_right >= s_max_x and
                aligned_top >= s_max_y and
                aligned_bottom <= s_min_y)    
    if not contains:
        raise ValueError(
            f"[裁剪范围错误] 输出裁剪区域未能完全包含高分图像范围。\n"
            f"输出裁剪 bounds: {output_bounds}\n"
            f"高分影像 bounds: {(s_min_x, s_min_y, s_max_x, s_max_y)} \n"
            f"[保存路径]{save_path}"
        )
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    # options = gdal.WarpOptions(format="GTiff", outputBounds=(s_min_x, s_min_y, s_max_x, s_max_y), cutlineBlend=0, dstNodata=255, outputType=gdal.GDT_Float32, options=["PHOTOMETRIC=MINISBLACK"])
    options = gdal.WarpOptions(
        format="GTiff",
        outputBounds=output_bounds,
        xRes=10, yRes=10,  # 明确指定输出分辨率
        resampleAlg='near',  # 最近邻，保持原始值（比如分类或整数波段）
        dstNodata=None,
        outputType=gdal.GDT_Float32
    )
    gdal.Warp(destNameOrDestDS=save_path,  srcDSOrSrcDSTab=big_tif_path,  options=options)



if __name__ == "__main__":
    event_name = "palu-tsunami"
    event_base_path = f"/mnt/E/Dataset/xBD/original/xview2_geotiff.tar/xview2_geotiff/geotiffs/GeoTransform/{event_name}/"

    # 第1步：将S2数据裁剪成 384x384 的小图像
    small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images/*pre*.tif"))
    big_tif_paths = glob(os.path.join(event_base_path, "S2_Python/**.tif"))
    small_tif_paths.sort()
    big_tif_paths.sort()
    args = []
    print(len(small_tif_paths), len(big_tif_paths))
    small_count = 0
    for small_tif_path in tqdm(small_tif_paths):
        # small_bounds = get_tif_bounds(small_tif_path)
        big_count = 0
        small_count += 1
        for big_id, big_tif_path in enumerate(big_tif_paths):
            # big_bounds = get_tif_bounds(big_tif_path)
            # if is_small_in_big(small_bounds, big_bounds):
            if len(big_tif_paths)-2 <= big_id:
                save_path = os.path.dirname(small_tif_path.replace("/images/", "/S2_Python_Margin/S2_post/"))
                save_name = os.path.basename(small_tif_path).split(".")[0] + "_S2_" +os.path.basename(big_tif_path).split("_S2_")[-1]
                save_name = save_name.replace("_pre_", "_post_")
            else:
                save_path = os.path.dirname(small_tif_path.replace("/images/", "/S2_Python_Margin/S2/"))
                save_name = os.path.basename(small_tif_path).split(".")[0] + "_S2_" +os.path.basename(big_tif_path).split("_S2_")[-1]

            os.makedirs(save_path, exist_ok=True)
            big_count += 1
            # print(small_tif_path, big_tif_path, os.path.join(save_path, save_name))
            args.append((small_tif_path, big_tif_path, os.path.join(save_path, save_name)))
            # print(len(args))
        # if len(args) > 150:
        #     break
    print(f"Total: {len(args)}")
    num_workers = os.cpu_count()
    with Pool(num_workers) as pool:
        list(tqdm(pool.starmap(clip_big_tif_by_small_margin, args), total=len(args), desc="clip S2 tif"))

