from collections import OrderedDict
import copy
import os
import torch.nn.functional as F
from einops.einops import rearrange
__all__ = ['CDNeXt', 'get_cdnext',]


# Copyright (c) Meta Platforms, Inc. and affiliates.

# All rights reserved.

# This source code is licensed under the license found in the
# LICENSE file in the root directory of this source tree.


import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.layers import trunc_normal_, DropPath
from timm.models.registry import register_model

import math
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Variable
from einops.einops import rearrange
from timm.models.layers import trunc_normal_, DropPath
class Swish(nn.Module):
    def __init__(self, name=None):
        super().__init__()
        self.name = name
    def forward(self, x):
        return x * torch.sigmoid(x)

class SqueezeDoubleConvOld(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(SqueezeDoubleConvOld, self).__init__()
        self.squeeze = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(out_channels),
            nn.GELU())
        self.double_conv = nn.Sequential(
            nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.GELU(),
            nn.Conv2d(out_channels, out_channels, kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(out_channels),
            )
        self.acfun = nn.GELU()

    def forward(self, x):
        x = self.squeeze(x)
        block_x = self.double_conv(x)
        x = self.acfun(x + block_x)
        return  x

class ChannelAttention(nn.Module):
    def __init__(self, in_planes, ratio=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
           
        self.fc = nn.Sequential(nn.Conv2d(in_planes, in_planes // 16, 1, bias=False),
                               nn.ReLU(),
                               nn.Conv2d(in_planes // 16, in_planes, 1, bias=False))
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()

        self.conv1 = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv1(x)
        return self.sigmoid(x)

class CBAM(nn.Module):
    def __init__(self, in_planes, CAon=True, SAon=True, ratio=16, kernel_size=7):
        super(CBAM, self).__init__()
        self.CAon = CAon
        self.SAon = SAon
        if self.CAon: 
            self.ca = ChannelAttention(in_planes, ratio)
        if self.SAon: 
            self.sa = SpatialAttention(kernel_size)

    def forward(self, x): 
        if self.CAon:
            x = self.ca(x) * x
        if self.SAon: 
            x = self.sa(x) * x
        return x

class NonLocal2D(nn.Module):
    def __init__(self, in_channels, inter_channels=None, dimension=2, sub_sample=False, bn_layer=True):
        """
        :param in_channels:
        :param inter_channels:
        :param dimension:
        :param sub_sample:
        :param bn_layer:
        """
        super(NonLocal2D, self).__init__()

        assert dimension in [2,]

        self.dimension = dimension
        self.sub_sample = sub_sample

        self.in_channels = in_channels
        self.inter_channels = inter_channels

        if self.inter_channels is None:
            self.inter_channels = in_channels // 2
            if self.inter_channels == 0:
                self.inter_channels = 1

        if dimension == 2:
            conv_nd = nn.Conv2d
            max_pool_layer = nn.MaxPool2d(kernel_size=(2, 2))
            bn = nn.BatchNorm2d

        self.g = conv_nd(in_channels=self.in_channels, out_channels=self.inter_channels,
                         kernel_size=1, stride=1, padding=0)

        self.W = nn.Sequential(
            conv_nd(in_channels=self.inter_channels, out_channels=self.in_channels,
                    kernel_size=1, stride=1, padding=0),
            bn(self.in_channels)
        )
        nn.init.constant_(self.W[1].weight, 0)
        nn.init.constant_(self.W[1].bias, 0)

        # self.theta = conv_nd(in_channels=self.in_channels, out_channels=self.inter_channels,
        #                      kernel_size=1, stride=1, padding=0)
        # self.phi = conv_nd(in_channels=self.in_channels, out_channels=self.inter_channels,
        #                    kernel_size=1, stride=1, padding=0)

        self.theta = nn.Sequential(
            # nn.BatchNorm2d(self.inter_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                             kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(self.inter_channels)
        )
        self.phi = nn.Sequential(
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                           kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(self.inter_channels)
            )


    def forward(self, x, return_nl_map=False):
        """
        :param x: (b, c, h, w)
        :param return_nl_map: if True return z, nl_map, else only return z.
        :return:
        """
        batch_size = x.size(0)

        g_x = self.g(x).view(batch_size, self.inter_channels, -1)
        g_x = g_x.permute(0, 2, 1)

        theta_x = self.theta(x).view(batch_size, self.inter_channels, -1)
        theta_x = theta_x.permute(0, 2, 1)
        phi_x = self.phi(x).view(batch_size, self.inter_channels, -1)
        f = torch.matmul(theta_x, phi_x)#.permute(0, 2, 1)#compare with source github, this change more regular
        f_div_C = F.softmax(f, dim=-1)

        y = torch.matmul(f_div_C, g_x)
        y = y.permute(0, 2, 1).contiguous()
        y = y.view(batch_size, self.inter_channels, *x.size()[2:])
        W_y = self.W(y)
        z = W_y + x

        if return_nl_map:
            return z, f_div_C
        return z
    
class SpatiotemporalAttentionFull(nn.Module):
    def __init__(self, in_channels, inter_channels=None, dimension=2, sub_sample=False, bn_layer=True):
        """
        :param in_channels:
        :param inter_channels:
        :param dimension:
        :param sub_sample:
        :param bn_layer:
        """
        super(SpatiotemporalAttentionFull, self).__init__()
        assert dimension in [2,]
        self.dimension = dimension
        self.sub_sample = sub_sample
        self.in_channels = in_channels
        self.inter_channels = inter_channels

        if self.inter_channels is None:
            self.inter_channels = in_channels // 2
            if self.inter_channels == 0:
                self.inter_channels = 1

        self.g = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                         kernel_size=1, stride=1, padding=0)
                         )
                         
        self.W = nn.Sequential(
            nn.Conv2d(in_channels=self.inter_channels, out_channels=self.in_channels,
                    kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(self.in_channels)
        )
        self.theta = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                             kernel_size=1, stride=1, padding=0),
        )
        self.phi = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                           kernel_size=1, stride=1, padding=0),
            )
        self.energy_time_1_sf = nn.Softmax(dim=-1)
        self.energy_time_2_sf = nn.Softmax(dim=-1)
        self.energy_space_2s_sf = nn.Softmax(dim=-2)
        self.energy_space_1s_sf = nn.Softmax(dim=-2)
        
    def forward(self, x1, x2):
        """
        :param x: (b, c, h, w)
        :param return_nl_map: if True return z, nl_map, else only return z.
        :return:
        """
        batch_size = x1.size(0)
        g_x11 = self.g(x1).reshape(batch_size, self.inter_channels, -1)
        g_x12 = g_x11.permute(0, 2, 1)
        g_x21 = self.g(x2).reshape(batch_size, self.inter_channels, -1)
        g_x22 = g_x21.permute(0, 2, 1)

        theta_x1 = self.theta(x1).reshape(batch_size, self.inter_channels, -1)
        theta_x2 = theta_x1.permute(0, 2, 1)
        
        phi_x1 = self.phi(x2).reshape(batch_size, self.inter_channels, -1)
        phi_x2 = phi_x1.permute(0, 2, 1)

        energy_time_1 = torch.matmul(theta_x1, phi_x2)
        energy_time_2 = energy_time_1.permute(0, 2, 1)
        energy_space_1 = torch.matmul(theta_x2, phi_x1)
        energy_space_2 = energy_space_1.permute(0, 2, 1)

        energy_time_1s = self.energy_time_1_sf(energy_time_1) 
        energy_time_2s = self.energy_time_2_sf(energy_time_2) 
        energy_space_2s = self.energy_space_2s_sf(energy_space_1) 
        energy_space_1s = self.energy_space_1s_sf(energy_space_2) 

        # energy_time_2s*g_x11*energy_space_2s = C2*S(C1) × C1*H1W1 × S(H1W1)*H2W2 = (C2*H2W2)' is rebuild C1*H1W1
        y1 = torch.matmul(torch.matmul(energy_time_2s, g_x11), energy_space_2s).contiguous()#C2*H2W2
        # energy_time_1s*g_x12*energy_space_1s = C1*S(C2) × C2*H2W2 × S(H2W2)*H1W1 = (C1*H1W1)' is rebuild C2*H2W2
        y2 = torch.matmul(torch.matmul(energy_time_1s, g_x21), energy_space_1s).contiguous()
        y1 = y1.reshape(batch_size, self.inter_channels, *x2.size()[2:])
        y2 = y2.reshape(batch_size, self.inter_channels, *x1.size()[2:])
        return x1 + self.W(y1), x2 + self.W(y2)

class SpatiotemporalAttentionBase(nn.Module):
    def __init__(self, in_channels, inter_channels=None, dimension=2, sub_sample=False, bn_layer=True):
        """
        :param in_channels:
        :param inter_channels:
        :param dimension:
        :param sub_sample:
        :param bn_layer:
        """
        super(SpatiotemporalAttentionBase, self).__init__()
        assert dimension in [2,]
        self.dimension = dimension
        self.sub_sample = sub_sample
        self.in_channels = in_channels
        self.inter_channels = inter_channels

        if self.inter_channels is None:
            self.inter_channels = in_channels // 2
            if self.inter_channels == 0:
                self.inter_channels = 1

        self.g = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                         kernel_size=1, stride=1, padding=0)
                         )
                         
        self.W = nn.Sequential(
            nn.Conv2d(in_channels=self.inter_channels, out_channels=self.in_channels,
                    kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(self.in_channels)
        )

        self.theta = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                             kernel_size=1, stride=1, padding=0),
        )
        self.phi = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                           kernel_size=1, stride=1, padding=0),
            )
        self.energy_space_2s_sf = nn.Softmax(dim=-2)
        self.energy_space_1s_sf = nn.Softmax(dim=-2)
        
    def forward(self, x1, x2):
        """
        :param x: (b, c, h, w)
        :param return_nl_map: if True return z, nl_map, else only return z.
        :return:
        """
        batch_size = x1.size(0)
        g_x11 = self.g(x1).reshape(batch_size, self.inter_channels, -1)
        g_x21 = self.g(x2).reshape(batch_size, self.inter_channels, -1)

        theta_x1 = self.theta(x1).reshape(batch_size, self.inter_channels, -1)
        theta_x2 = theta_x1.permute(0, 2, 1)
        
        phi_x1 = self.phi(x2).reshape(batch_size, self.inter_channels, -1)

        energy_space_1 = torch.matmul(theta_x2, phi_x1)
        energy_space_2 = energy_space_1.permute(0, 2, 1)
        energy_space_2s = self.energy_space_2s_sf(energy_space_1) # S(H1W1)*H2W2
        energy_space_1s = self.energy_space_1s_sf(energy_space_2) # S(H2W2)*H1W1

        # g_x11*energy_space_2s = C1*H1W1 × S(H1W1)*H2W2 = (C1*H2W2)' is rebuild C1*H1W1
        y1 = torch.matmul(g_x11, energy_space_2s).contiguous()#C2*H2W2
        # g_x21*energy_space_1s = C2*H2W2 × S(H2W2)*H1W1 = (C2*H1W1)' is rebuild C2*H2W2
        y2 = torch.matmul(g_x21, energy_space_1s).contiguous()
        y1 = y1.reshape(batch_size, self.inter_channels, *x2.size()[2:])
        y2 = y2.reshape(batch_size, self.inter_channels, *x1.size()[2:])
        return x1 + self.W(y1), x2 + self.W(y2)

class SpatiotemporalAttentionFullNotWeightShared(nn.Module):
    def __init__(self, in_channels, inter_channels=None, dimension=2, sub_sample=False, bn_layer=True):
        """
        :param in_channels:
        :param inter_channels:
        :param dimension:
        :param sub_sample:
        :param bn_layer:
        """
        super(SpatiotemporalAttentionFullNotWeightShared, self).__init__()
        assert dimension in [2,]
        self.dimension = dimension
        self.sub_sample = sub_sample
        self.in_channels = in_channels
        self.inter_channels = inter_channels

        if self.inter_channels is None:
            self.inter_channels = in_channels // 2
            if self.inter_channels == 0:
                self.inter_channels = 1

        self.g1 = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                         kernel_size=1, stride=1, padding=0)
                         )
        self.g2 = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                         kernel_size=1, stride=1, padding=0),
                         )
                         
        self.W1 = nn.Sequential(
            nn.Conv2d(in_channels=self.inter_channels, out_channels=self.in_channels,
                    kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(self.in_channels)
        )
        self.W2 = nn.Sequential(
            nn.Conv2d(in_channels=self.inter_channels, out_channels=self.in_channels,
                    kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(self.in_channels)
        )
        self.theta = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                             kernel_size=1, stride=1, padding=0),
        )
        self.phi = nn.Sequential(
            nn.BatchNorm2d(self.in_channels),
            nn.Conv2d(in_channels=self.in_channels, out_channels=self.inter_channels,
                           kernel_size=1, stride=1, padding=0),
            )
        
    def forward(self, x1, x2):
        """
        :param x: (b, c, h, w)
        :param return_nl_map: if True return z, nl_map, else only return z.
        :return:
        """
        batch_size = x1.size(0)
        g_x11 = self.g1(x1).reshape(batch_size, self.inter_channels, -1)
        g_x12 = g_x11.permute(0, 2, 1)
        g_x21 = self.g2(x2).reshape(batch_size, self.inter_channels, -1)
        g_x22 = g_x21.permute(0, 2, 1)

        theta_x1 = self.theta(x1).reshape(batch_size, self.inter_channels, -1)
        theta_x2 = theta_x1.permute(0, 2, 1)
        
        phi_x1 = self.phi(x2).reshape(batch_size, self.inter_channels, -1)
        phi_x2 = phi_x1.permute(0, 2, 1)

        energy_time_1 = torch.matmul(theta_x1, phi_x2)
        energy_time_2 = energy_time_1.permute(0, 2, 1)
        energy_space_1 = torch.matmul(theta_x2, phi_x1)
        energy_space_2 = energy_space_1.permute(0, 2, 1)

        energy_time_1s = F.softmax(energy_time_1, dim=-1) 
        energy_time_2s = F.softmax(energy_time_2, dim=-1) 
        energy_space_2s = F.softmax(energy_space_1, dim=-2) 
        energy_space_1s = F.softmax(energy_space_2, dim=-2) 
        #  C1*S(C2) energy_time_1s * C1*H1W1 g_x12 * energy_space_1s S(H2W2)*H1W1 -> C1*H1W1
        y1 = torch.matmul(torch.matmul(energy_time_2s, g_x11), energy_space_2s).contiguous()#C2*H2W2
        #  C2*S(C1) energy_time_2s * C2*H2W2 g_x21 * energy_space_2s S(H1W1)*H2W2 -> C2*H2W2
        y2 = torch.matmul(torch.matmul(energy_time_1s, g_x21), energy_space_1s).contiguous()#C1*H1W1
        y1 = y1.reshape(batch_size, self.inter_channels, *x2.size()[2:])
        y2 = y2.reshape(batch_size, self.inter_channels, *x1.size()[2:])
        return x1 + self.W1(y1), x2 + self.W2(y2)

class DANetModule(nn.Module):
    def __init__(self, in_dim):
        super(DANetModule, self).__init__()
        self.chanel_in = in_dim
        self.sa = PAM_Module(in_dim)
        self.sc = CAM_Module(in_dim)

    def forward(self, x):
        sa_feat = self.sa(x)
        sc_feat = self.sc(x)
        return sa_feat + sc_feat


class PAM_Module(nn.Module):
    """ Position attention module"""
    #Ref from SAGAN
    def __init__(self, in_dim):
        super(PAM_Module, self).__init__()
        self.chanel_in = in_dim

        self.query_conv = nn.Sequential(
            nn.Conv2d(in_channels=in_dim, out_channels=in_dim//8, kernel_size=1),
            nn.BatchNorm2d(in_dim//8),
        )
        self.key_conv = nn.Sequential(
            nn.Conv2d(in_channels=in_dim, out_channels=in_dim//8, kernel_size=1),
            nn.BatchNorm2d(in_dim//8),
        )
        self.value_conv = nn.Sequential(
            nn.Conv2d(in_channels=in_dim, out_channels=in_dim, kernel_size=1),
            nn.BatchNorm2d(in_dim),
        )
        self.gamma = nn.Parameter(torch.zeros(1))

        self.softmax = nn.Softmax(dim=-1)
    def forward(self, x):
        """
            inputs :
                x : input feature maps( B X C X H X W)
            returns :
                out : attention value + input feature
                attention: B X (HxW) X (HxW)
        """
        m_batchsize, C, height, width = x.size()
        proj_query = self.query_conv(x).view(m_batchsize, -1, width*height).permute(0, 2, 1)
        proj_key = self.key_conv(x).view(m_batchsize, -1, width*height)
        energy = torch.bmm(proj_query, proj_key)
        attention = self.softmax(energy)
        proj_value = self.value_conv(x).view(m_batchsize, -1, width*height)

        out = torch.bmm(proj_value, attention.permute(0, 2, 1))
        out = out.view(m_batchsize, C, height, width)

        out = self.gamma*out + x
        return out

class CAM_Module(nn.Module):
    """ Channel attention module"""
    def __init__(self, in_dim):
        super(CAM_Module, self).__init__()
        self.chanel_in = in_dim

        self.query_conv = nn.Sequential(
            nn.Conv2d(in_channels=in_dim, out_channels=in_dim//8, kernel_size=1),
            nn.BatchNorm2d(in_dim//8),
        )
        self.key_conv = nn.Sequential(
            nn.Conv2d(in_channels=in_dim, out_channels=in_dim//8, kernel_size=1),
            nn.BatchNorm2d(in_dim//8),
        )
        self.value_conv = nn.Sequential(
            nn.Conv2d(in_channels=in_dim, out_channels=in_dim, kernel_size=1),
            nn.BatchNorm2d(in_dim),
        )

        self.gamma = nn.Parameter(torch.zeros(1))
        self.softmax  = nn.Softmax(dim=-1)
    def forward(self,x):
        """
            inputs :
                x : input feature maps( B X C X H X W)
            returns :
                out : attention value + input feature
                attention: B X C X C
        """
        m_batchsize, C, height, width = x.size()
        proj_query = self.query_conv(x).view(m_batchsize, C, -1)
        proj_key = self.key_conv(x).view(m_batchsize, C, -1).permute(0, 2, 1)
        energy = torch.bmm(proj_query, proj_key)
        energy_new = torch.max(energy, -1, keepdim=True)[0].expand_as(energy)-energy
        attention = self.softmax(energy_new)
        proj_value = self.value_conv(x).view(m_batchsize, C, -1)

        out = torch.bmm(attention, proj_value)
        out = out.view(m_batchsize, C, height, width)

        out = self.gamma*out + x
        return out

class Block(nn.Module):
    r""" ConvNeXt Block. There are two equivalent implementations:
    (1) DwConv -> LayerNorm (channels_first) -> 1x1 Conv -> GELU -> 1x1 Conv; all in (N, C, H, W)
    (2) DwConv -> Permute to (N, H, W, C); LayerNorm (channels_last) -> Linear -> GELU -> Linear; Permute back
    We use (2) as we find it slightly faster in PyTorch
    
    Args:
        dim (int): Number of input channels.
        drop_path (float): Stochastic depth rate. Default: 0.0
        layer_scale_init_value (float): Init value for Layer Scale. Default: 1e-6.
    """
    def __init__(self, dim, drop_path=0., layer_scale_init_value=1e-6):
        super().__init__()
        self.dwconv = nn.Conv2d(dim, dim, kernel_size=7, padding=3, groups=dim) # depthwise conv
        self.norm = LayerNorm(dim, eps=1e-6)
        self.pwconv1 = nn.Linear(dim, 4 * dim) # pointwise/1x1 convs, implemented with linear layers
        self.act = nn.GELU()
        self.pwconv2 = nn.Linear(4 * dim, dim)
        self.gamma = nn.Parameter(layer_scale_init_value * torch.ones((dim)), 
                                    requires_grad=True) if layer_scale_init_value > 0 else None
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

    def forward(self, x):
        input = x
        x = self.dwconv(x)
        x = x.permute(0, 2, 3, 1) # (N, C, H, W) -> (N, H, W, C)
        x = self.norm(x)
        x = self.pwconv1(x)
        x = self.act(x)
        x = self.pwconv2(x)
        if self.gamma is not None:
            x = self.gamma * x
        x = x.permute(0, 3, 1, 2) # (N, H, W, C) -> (N, C, H, W)

        x = input + self.drop_path(x)
        return x

class ConvNeXt(nn.Module):
    r""" ConvNeXt
        A PyTorch impl of : `A ConvNet for the 2020s`  -
          https://arxiv.org/pdf/2201.03545.pdf

    Args:
        in_chans (int): Number of input image channels. Default: 3
        num_classes (int): Number of classes for classification head. Default: 1000
        depths (tuple(int)): Number of blocks at each stage. Default: [3, 3, 9, 3]
        dims (int): Feature dimension at each stage. Default: [96, 192, 384, 768]
        drop_path_rate (float): Stochastic depth rate. Default: 0.
        layer_scale_init_value (float): Init value for Layer Scale. Default: 1e-6.
        head_init_scale (float): Init scaling value for classifier weights and biases. Default: 1.
    """
    def __init__(self, in_chans=3, num_classes=1000, 
                 depths=[3, 3, 9, 3], dims=[96, 192, 384, 768], drop_path_rate=0., 
                 layer_scale_init_value=1e-6, head_init_scale=1.,
                 ):
        super().__init__()

        self.downsample_layers = nn.ModuleList() # stem and 3 intermediate downsampling conv layers
        stem = nn.Sequential(
            nn.Conv2d(in_chans, dims[0], kernel_size=4, stride=4),
            LayerNorm(dims[0], eps=1e-6, data_format="channels_first")
        )
        self.downsample_layers.append(stem)
        for i in range(3):
            downsample_layer = nn.Sequential(
                    LayerNorm(dims[i], eps=1e-6, data_format="channels_first"),
                    nn.Conv2d(dims[i], dims[i+1], kernel_size=2, stride=2),
            )
            self.downsample_layers.append(downsample_layer)

        self.stages = nn.ModuleList() # 4 feature resolution stages, each consisting of multiple residual blocks
        dp_rates=[x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))] 
        cur = 0
        for i in range(4):
            stage = nn.Sequential(
                *[Block(dim=dims[i], drop_path=dp_rates[cur + j], 
                layer_scale_init_value=layer_scale_init_value) for j in range(depths[i])]
            )
            self.stages.append(stage)
            cur += depths[i]

        self.norm = nn.LayerNorm(dims[-1], eps=1e-6) # final norm layer
        self.head = nn.Linear(dims[-1], num_classes)

        self.apply(self._init_weights)
        self.head.weight.data.mul_(head_init_scale)
        self.head.bias.data.mul_(head_init_scale)
        self.feature = []
    def _init_weights(self, m):
        if isinstance(m, (nn.Conv2d, nn.Linear)):
            trunc_normal_(m.weight, std=.02)
            nn.init.constant_(m.bias, 0)

    def forward_features(self, x):
        for i in range(4):
            x = self.downsample_layers[i](x)
            x = self.stages[i](x)
        return self.norm(x.mean([-2, -1])) # global average pooling, (N, C, H, W) -> (N, C)

    def forward(self, x):
        x = self.forward_features(x)
        x = self.head(x)
        return x

class LayerNorm(nn.Module):
    r""" LayerNorm that supports two data formats: channels_last (default) or channels_first. 
    The ordering of the dimensions in the inputs. channels_last corresponds to inputs with 
    shape (batch_size, height, width, channels) while channels_first corresponds to inputs 
    with shape (batch_size, channels, height, width).
    """
    def __init__(self, normalized_shape, eps=1e-6, data_format="channels_last"):
        super().__init__()
        self.weight = nn.Parameter(torch.ones(normalized_shape))
        self.bias = nn.Parameter(torch.zeros(normalized_shape))
        self.eps = eps
        self.data_format = data_format
        if self.data_format not in ["channels_last", "channels_first"]:
            raise NotImplementedError 
        self.normalized_shape = (normalized_shape, )
    
    def forward(self, x):
        if self.data_format == "channels_last":
            return F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        elif self.data_format == "channels_first":
            u = x.mean(1, keepdim=True)
            s = (x - u).pow(2).mean(1, keepdim=True)
            x = (x - u) / torch.sqrt(s + self.eps)
            x = self.weight[:, None, None] * x + self.bias[:, None, None]
            return x


model_urls = {
    "convnext_tiny_1k": "https://dl.fbaipublicfiles.com/convnext/convnext_tiny_1k_224_ema.pth",
    "convnext_small_1k": "https://dl.fbaipublicfiles.com/convnext/convnext_small_1k_224_ema.pth",
    "convnext_base_1k": "https://dl.fbaipublicfiles.com/convnext/convnext_base_1k_224_ema.pth",
    "convnext_large_1k": "https://dl.fbaipublicfiles.com/convnext/convnext_large_1k_224_ema.pth",
    "convnext_tiny_22k": "https://dl.fbaipublicfiles.com/convnext/convnext_tiny_22k_224.pth",
    "convnext_small_22k": "https://dl.fbaipublicfiles.com/convnext/convnext_small_22k_224.pth",
    "convnext_base_22k": "https://dl.fbaipublicfiles.com/convnext/convnext_base_22k_224.pth",
    "convnext_large_22k": "https://dl.fbaipublicfiles.com/convnext/convnext_large_22k_224.pth",
    "convnext_xlarge_22k": "https://dl.fbaipublicfiles.com/convnext/convnext_xlarge_22k_224.pth",
    "convnext_tiny_22k_384": "https://dl.fbaipublicfiles.com/convnext/convnext_tiny_22k_1k_384.pth",
    "convnext_small_22k_384": "https://dl.fbaipublicfiles.com/convnext/convnext_small_22k_1k_384.pth",
    "convnext_base_22k_384": "https://dl.fbaipublicfiles.com/convnext/convnext_base_22k_1k_384.pth",
    "convnext_large_22k_384": "https://dl.fbaipublicfiles.com/convnext/convnext_large_22k_1k_384.pth",
    "convnext_xlarge_22k_384": "https://dl.fbaipublicfiles.com/convnext/convnext_xlarge_22k_1k_384_ema.pth",
}

@register_model
def convnext_tiny(pretrained=False, in_22k=False, classes=1000, resolution=None, **kwargs):
    #1k is 1000, 22k is 21841
    model = ConvNeXt(num_classes=classes, depths=[3, 3, 9, 3], dims=[96, 192, 384, 768], **kwargs)
    # for (name, module) in model.named_modules():
    #     print(name)
    resolution = "_"+str(resolution) if resolution==384 else None
    if pretrained:        
        url = model_urls['convnext_tiny_22k'+resolution] if in_22k else model_urls['convnext_tiny_1k']
        checkpoint = torch.hub.load_state_dict_from_url(url=url, map_location="cpu", check_hash=True)
        if classes != 1000 and classes != 21841:    
            random_state_dict = model.state_dict()
            checkpoint['model']['head.weight'] = random_state_dict['head.weight']
            checkpoint['model']['head.bias'] = random_state_dict['head.bias']
        model.load_state_dict(checkpoint["model"])
    return model

@register_model
def convnext_small(pretrained=False, in_22k=False, classes=1000, resolution=None, **kwargs):
    model = ConvNeXt(num_classes=classes, depths=[3, 3, 27, 3], dims=[96, 192, 384, 768], **kwargs)
    resolution = "_"+str(resolution) if resolution==384 else None
    if pretrained:
        url = model_urls['convnext_small_22k'+resolution] if in_22k else model_urls['convnext_small_1k']
        checkpoint = torch.hub.load_state_dict_from_url(url=url, map_location="cpu")
        if classes != 1000 and classes != 21841:    
            random_state_dict = model.state_dict()
            checkpoint['model']['head.weight'] = random_state_dict['head.weight']
            checkpoint['model']['head.bias'] = random_state_dict['head.bias']
        model.load_state_dict(checkpoint["model"])
    return model

@register_model
def convnext_base(pretrained=False, in_22k=False, classes=1000, resolution=None, **kwargs):
    model = ConvNeXt(num_classes=classes, depths=[3, 3, 27, 3], dims=[128, 256, 512, 1024], **kwargs)
    resolution = "_"+str(resolution) if resolution==384 else None
    if pretrained:
        url = model_urls['convnext_base_22k'+resolution] if in_22k else model_urls['convnext_base_1k']
        checkpoint = torch.hub.load_state_dict_from_url(url=url, map_location="cpu")
        if classes != 1000 and classes != 21841:    
            random_state_dict = model.state_dict()
            checkpoint['model']['head.weight'] = random_state_dict['head.weight']
            checkpoint['model']['head.bias'] = random_state_dict['head.bias']
        model.load_state_dict(checkpoint["model"])
    return model

@register_model
def convnext_large(pretrained=False, in_22k=False, classes=1000, resolution=None, **kwargs):
    model = ConvNeXt(num_classes=classes, depths=[3, 3, 27, 3], dims=[192, 384, 768, 1536], **kwargs)
    resolution = "_"+str(resolution) if resolution==384 else None
    if pretrained:
        url = model_urls['convnext_large_22k'+resolution] if in_22k else model_urls['convnext_large_1k']
        checkpoint = torch.hub.load_state_dict_from_url(url=url, map_location="cpu")
        if classes != 1000 and classes != 21841:    
            random_state_dict = model.state_dict()
            checkpoint['model']['head.weight'] = random_state_dict['head.weight']
            checkpoint['model']['head.bias'] = random_state_dict['head.bias']
        model.load_state_dict(checkpoint["model"])
    return model

@register_model
def convnext_xlarge(pretrained=False, in_22k=False, classes=1000, resolution=None, **kwargs):
    model = ConvNeXt(num_classes=classes, depths=[3, 3, 27, 3], dims=[256, 512, 1024, 2048], **kwargs)
    resolution = "_"+str(resolution) if resolution==384 else None
    if pretrained:
        assert in_22k, "only ImageNet-22K pre-trained ConvNeXt-XL is available; please set in_22k=True"
        url = model_urls['convnext_xlarge_22k'+resolution]
        checkpoint = torch.hub.load_state_dict_from_url(url=url, map_location="cpu")
        if classes != 1000 and classes != 21841:    
            random_state_dict = model.state_dict()
            checkpoint['model']['head.weight'] = random_state_dict['head.weight']
            checkpoint['model']['head.bias'] = random_state_dict['head.bias']
        model.load_state_dict(checkpoint["model"])
    return model

@register_model
def get_convnext(pretrained=False, backbone_scale='tiny', in_22k=True, classes=1000, resolution=224, **kwargs):
    if backbone_scale == "tiny":
        depths, dims= [3, 3, 9, 3], [96, 192, 384, 768]
    if backbone_scale == "small":
        depths, dims= [3, 3, 27, 3], [96, 192, 384, 768]
    if backbone_scale == "base":
        depths, dims= [3, 3, 27, 3], [128, 256, 512, 1024]
    if backbone_scale == "large":
        depths, dims= [3, 3, 27, 3], [192, 384, 768, 1536]
    if backbone_scale == "xlarge":
        depths, dims= [3, 3, 27, 3], [256, 512, 1024, 2048]
    else:
        model = ConvNeXt(num_classes=classes, depths=depths, dims=dims, **kwargs)
        if pretrained: 
            clsNums = "22k" if in_22k else "1k"
            url = model_urls["convnext_"+backbone_scale+"_"+clsNums+"_"+str(resolution)]
            checkpoint = torch.hub.load_state_dict_from_url(url=url, map_location="cpu")
            if classes != 1000 and classes != 21841:   
                random_state_dict = model.state_dict()
                checkpoint['model']['head.weight'] = random_state_dict['head.weight']
                checkpoint['model']['head.bias'] = random_state_dict['head.bias']   
            model.load_state_dict(checkpoint["model"])    
    return model

class CDNeXt(nn.Module):
    def __init__(self, encoder, backbone_scale="tiny", out_channels=2, isTemporalAttention=[1,2,3,4], isCBAM=[0,0,0,0], isNonlocal=[0,0,0,0]):
        super().__init__()
        self.encoder = encoder
        self.isFeatureFusion = True

        self.CAon = False
        self.SAon = False
        self.isCBAMconcat = False

        self.isNonlocalConcat = False
        self.AttentionModule = DANetModule#NonLocal2D    DANetModule

        self.isTemporalAttention = isTemporalAttention
        self.SpatiotemporalAttentionModule = SpatiotemporalAttentionFull #SpatiotemporalAttentionBase  SpatiotemporalAttentionFull  SpatiotemporalAttentionFullNotWeightShared
        self.isCBAM = isCBAM
        self.isNonlocal = isNonlocal
        self.encoderName = backbone_scale
        if "resnet" in self.encoderName:
            self.encoderNameScale = 2
        else:
            self.encoderNameScale = 4
        self.AdvSupResult = []
        self.downScale = [16, 8, 4, 0]
        self.stageNumber = 4
        self.backbone_depth = {
                                'tiny': [3, 3, 9, 3], 
                                'small': [3, 3, 27, 3],
                                'base': [3, 3, 27, 3], 
                                'large': [3, 3, 27, 3],
                                'xlarge': [3, 3, 27, 3],
                                "resnet18": [2, 2, 2, 2]
                            }
        self.backbone_dims = {
                                'tiny': [96, 192, 384, 768], 
                                'small': [96, 192, 384, 768],
                                'base': [128, 256, 512, 1024], 
                                'large': [192, 384, 768, 1536],
                                'xlarge': [256, 512, 1024, 2048],
                                "resnet18": [64, 128, 256, 512]
                            }
        self.size_dict = {
                            'tiny': [24, 96, 192, 384, 768], 
                            'small': [24, 96, 192, 384, 768],
                            'base': [32, 128, 256, 512, 1024], 
                            'large': [48, 192, 384, 768, 1536],
                            'xlarge': [64, 256, 512, 1024, 2048],
                            "resnet18": [32, 64, 128, 256, 512]
                        }
        # source constructure
        #init attention module
        self.CBAMs = []
        self.TemporalAttentions = []
        self.Nonlocals = []
        self.ChangeSqueezeConv = []
        # module sequence,  F.interpolate、TemporalAttention、AdversialSupervised、concat feature、conv、

        for index in range(self.stageNumber):
            if index == 0:

                if self.isCBAM[index] > 0:
                    if self.isCBAMconcat:
                        self.CBAMs.append(CBAM(self.n_channels*2, CAon=self.CAon, SAon=self.SAon))
                    else:
                        self.CBAMs.append(CBAM(self.n_channels, CAon=self.CAon, SAon=self.SAon))
                if self.isTemporalAttention[index] > 0:
                    self.TemporalAttentions.append(self.SpatiotemporalAttentionModule(self.size_change[index],))
                if self.isNonlocal[index] > 0:
                    if self.isNonlocalConcat:
                        self.Nonlocals.append(self.AttentionModule(self.n_channels*2))
                    else:
                        self.Nonlocals.append(self.AttentionModule(self.n_channels))
                self.ChangeSqueezeConv.append(SqueezeDoubleConvOld(self.n_channels*2, self.n_channels))
            else:
                if self.isCBAM[index] > 0:
                    if self.isCBAMconcat:
                        self.CBAMs.append(CBAM(self.size_change[index]*2, CAon=self.CAon, SAon=self.SAon))
                    else:
                        self.CBAMs.append(CBAM(self.size_change[index], CAon=self.CAon, SAon=self.SAon))
                if self.isTemporalAttention[index] > 0:
                    self.TemporalAttentions.append(self.SpatiotemporalAttentionModule(self.size_change[index],))
                if self.isNonlocal[index] > 0:
                    if self.isNonlocalConcat:
                        self.Nonlocals.append(self.AttentionModule(self.size_change[index]*2))
                    else:
                        self.Nonlocals.append(self.AttentionModule(self.size_change[index]))
                self.ChangeSqueezeConv.append(SqueezeDoubleConvOld(self.size_change[index]*4, self.size_change[index]))

        self.CBAMs = nn.ModuleList(self.CBAMs)
        self.TemporalAttentions = nn.ModuleList(self.TemporalAttentions)
        self.Nonlocals = nn.ModuleList(self.Nonlocals)
        self.ChangeSqueezeConv = nn.ModuleList(self.ChangeSqueezeConv)
        if self.isFeatureFusion == True:
            self.ChangeFinalSqueezeConv = SqueezeDoubleConvOld(sum(self.size_change[:-1]), self.size_change[-1]*self.encoderNameScale)
            self.ChangeFinalConv = nn.Sequential(SqueezeDoubleConvOld(self.size_change[-1]*self.encoderNameScale, self.size_change[-1]), 
                                                nn.Conv2d(self.size_change[-1], out_channels, kernel_size=1))
        else:
            self.ChangeFinalSqueezeConv = SqueezeDoubleConvOld(self.size_change[-2], self.size_change[-1]*self.encoderNameScale)
            # self.ChangeFinalSqueezeConv = SqueezeDoubleConvOld(self.size_change[-2], self.size_change[-1])
            self.ChangeFinalConv = nn.Sequential(SqueezeDoubleConvOld(self.size_change[-1]*self.encoderNameScale, self.size_change[-1]), 
                                                nn.Conv2d(self.size_change[-1], out_channels, kernel_size=1))
        # self.softmax = nn.Softmax(dim=1)
        self.register_hook(self.encoder)
        self.backboneFeatures = []

    def register_hook(self, backbone):
        if "resnet" in self.encoderName:
            def hook(module, input, output):
                self.backboneFeatures.append(output)
            depth = self.backbone_depth[self.encoderName]
            for index, depth_num in enumerate(depth):
                getattr(backbone, "layer"+str(index+1)).register_forward_hook(hook)
        else:
            def hook(module, input, output):
                self.backboneFeatures.append(output)
            depth = self.backbone_depth[self.encoderName]
            for index, depth_num in enumerate(depth):
                backbone.stages[index][depth_num-1].register_forward_hook(hook)

    @property
    def n_channels(self):
        return self.backbone_dims[self.encoderName][-1]

    @property
    def size_change(self):
        size_dict =  copy.deepcopy(self.size_dict)
        size_dict = size_dict[self.encoderName][::-1]
        return size_dict

    def forward(self, inputs, device):

        outputs = {}
        x1 = inputs[0].to(device).to(torch.float32)
        x2 = inputs[2].to(device).to(torch.float32).squeeze(1)
        x2 = F.interpolate(x2, scale_factor=8, mode='bilinear', align_corners=True)

        _ = self.encoder(x1)
        _ = self.encoder(x2)
        # print(self.backboneFeatures)
        blocks1 = self.backboneFeatures[0:self.stageNumber]
        blocks2 = self.backboneFeatures[self.stageNumber:]
        self.backboneFeatures = []
        FusionFeatures = []
        change = None
        for stage in range(self.stageNumber):
            moduleIdx = stage
            eff_last_1 = blocks1.pop()#.permute(0, 3, 1, 2) 
            eff_last_2 = blocks2.pop()#.permute(0, 3, 1, 2)

            if self.isTemporalAttention[stage] > 0:
                moduleRealIdx = self.isTemporalAttention[stage] - 1
                eff_last_1, eff_last_2 = self.TemporalAttentions[moduleRealIdx](eff_last_1, eff_last_2)

            if self.isNonlocal[stage] > 0:
                moduleIdx = self.isNonlocal[stage] - 1
                if self.isNonlocalConcat:
                    eff_last = self.Nonlocals[moduleIdx](torch.cat([eff_last_1, eff_last_2], dim=1))
                    sliceNum = eff_last.shape[1]//2
                    eff_last_1, eff_last_2 = eff_last[:,0:sliceNum], eff_last[:,sliceNum:]
                else:
                    eff_last_1, eff_last_2 = self.Nonlocals[moduleIdx](eff_last_1), self.Nonlocals[moduleIdx](eff_last_2)

            if self.isCBAM[stage] > 0:
                moduleIdx = self.isCBAM[stage] - 1
                if self.isCBAMconcat:
                    eff_last = self.CBAMs[moduleIdx](torch.cat([eff_last_1, eff_last_2], dim=1))
                    sliceNum = eff_last.shape[1]//2
                    eff_last_1, eff_last_2 = eff_last[:,0:sliceNum], eff_last[:,sliceNum:]
                else:
                    eff_last_1, eff_last_2 = self.CBAMs[moduleIdx](eff_last_1), self.CBAMs[moduleIdx](eff_last_2)
            
            if stage == 0:
                change = torch.cat([eff_last_1, eff_last_2], dim=1)
            else:
                change = torch.cat([eff_last_1, eff_last_2, change], dim=1)

            if stage == self.stageNumber-1:
                change = self.ChangeSqueezeConv[stage](change)    
                FusionFeatures.append(change)
            else:
                change = self.ChangeSqueezeConv[stage](change)    
                FusionFeatures.append(change)
                change = F.interpolate(change, scale_factor=2., mode='bilinear', align_corners=True)
            
        if self.isFeatureFusion == True:
            for index, down in enumerate(self.downScale):
                FusionFeatures[index] = F.interpolate(FusionFeatures[index], scale_factor=2**(self.stageNumber-index-1), 
                                                        mode='bilinear', align_corners=True)
            fusion = torch.cat(FusionFeatures, dim=1)
        else:
            fusion = change

        change = self.ChangeFinalSqueezeConv(fusion)
        change = F.interpolate(change, scale_factor=self.encoderNameScale, mode='bilinear', align_corners=True)
        change = self.ChangeFinalConv(change)
        print(change.shape)

        return change

def get_cdnext(out_channels=2, backbone_scale="tiny", pretrained=True, in_22k=True, resolution=384, backbone_trainable=False, **kwargs):
    print("is pretrained: ", pretrained)
    encoder = get_convnext(pretrained=pretrained, backbone_scale=backbone_scale, classes=out_channels, in_22k=in_22k, resolution=resolution, **kwargs)
    model = CDNeXt(encoder, backbone_scale=backbone_scale, out_channels=out_channels, **kwargs)    

    if "resnet" in backbone_scale:
        pass
    else: 
        if backbone_trainable == False:
            for name, value in model.named_parameters():
                if "encoder" in name:
                    value.requires_grad = False

    return model


if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '3'
    deep_model = get_cdnext(out_channels=2, backbone_scale="tiny", pretrained=True).cuda()
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384).cuda()
    low_images1 = torch.rand(bs, 16, 17, 48, 48).cuda()
    low_images2 = torch.rand(bs, 1, 16, 48, 48).cuda()
    # low_images3 = torch.rand(bs, 1, 8, 48, 48).cuda()
    # low_images4 = torch.rand(bs, 1, 8, 48, 48).cuda()
    pre_label = torch.rand(bs, 384, 384).cuda()
    post_label = torch.rand(bs, 384, 384).cuda()
    inputs = [hight_image, low_images1, low_images2, pre_label, post_label]
    device = torch.device("cuda")

    outputss = deep_model(inputs, device)


    # flops, params = profile(deep_model, inputs=(inputs, device, ))
    # print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
