import os
from thop import profile
import torch
import torch.nn as nn
import timm
import numpy as np
import torch.nn.functional as F
from timm.models.layers import DropPath, to_2tuple, trunc_normal_
from timm.models.registry import register_model
from timm.models.vision_transformer import _cfg
from timm.models.registry import register_model
import math
import warnings
warnings.filterwarnings('ignore')

from einops import rearrange
from dcn_v2 import DCN
from model.UANet import *
from model.PVT import *
from model.PVT import Mlp


class PVTv2_Sentinel(nn.Module):
    def __init__(self, backbone="pvt_v5_b2", in_channels=17, de_channel=64, num_classes=2):
        super().__init__()
        # 1 construct the pvt backbone for sentinel data
        self.backbone = eval(backbone)()
        path = "/mnt/d2/zxq/BDS12/model/PTH/pvt_v2_b5.pth"
        save_model = torch.load(path)
        model_dict = self.backbone.state_dict()
        new_state_dict = {k:v if v.size()==model_dict[k].size()  else  model_dict[k] for k,v in zip(model_dict.keys(), save_model.values())}
        self.backbone.load_state_dict(new_state_dict, strict=False)



        # 3 construct the decoder for sentinel fetaure
        self.conv_2 = nn.Sequential(MBDC(64,de_channel))
        self.conv_3 = nn.Sequential(MBDC(128,de_channel))
        self.conv_4 = nn.Sequential(MBDC(320,de_channel))
        self.conv_5 = nn.Sequential(MBDC(512,de_channel))

        # 2 modify the stride of steam layer of PVT
        self.backbone.patch_embed1.proj = nn.Conv2d(in_channels, 64, kernel_size=7, stride=1, padding=3)
        # print(self.backbone.patch_embed1)

    def _repeat_reshape(self, predict, frame):
        predict = predict.unsqueeze(1).repeat(1, frame, 1, 1, 1)
        predict = rearrange(predict, "b t c h w -> (b t) c h w")
        return predict

    def forward(self, images):
        B, frame, C, h, w = images.shape
        images = images.view(B*frame, C, h, w)
        layer2, layer3, layer4, layer5 = self.backbone(images)
        layer5 = self.conv_5(layer5).view(B, frame, -1, h//8, w//8)
        layer4 = self.conv_4(layer4).view(B, frame, -1, h//4, w//4)
        layer3 = self.conv_3(layer3).view(B, frame, -1, h//2, w//2)
        layer2 = self.conv_2(layer2).view(B, frame, -1, h, w)
        return [layer2, layer3, layer4, layer5]


class DCN_Guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.dcn = DCN(decoder_channel*2, decoder_channel, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
        self.bn = nn.BatchNorm2d(decoder_channel)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, time_serise_feature):
        B, frame, C, H, W = time_serise_feature.shape
        current_frames = time_serise_feature[:, -1]
        referent_frames = time_serise_feature
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)
        assert current_frames.shape == referent_frames.shape, print(f"{current_frames.shape} vs. {referent_frames.shape}")

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")
        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)

        fusion_frames = self.dcn(fusion_frames)
        fusion_frames = self.bn(fusion_frames)
        fusion_frames = self.relu(fusion_frames)

        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames

class time_series_fusion(nn.Module):
    def __init__(self, decoder_channel, frame):
        super(time_series_fusion, self).__init__()
        self.dcn_guide = DCN_Guide(decoder_channel)

        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=int(decoder_channel*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.BatchNorm2d(decoder_channel*frame),
            nn.ReLU(inplace=True),
        )

        self.up = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel*frame, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.UpsamplingBilinear2d(scale_factor=2)
        )
 
    def forward(self, time_serise_feature):
        B, frame, C, H, W = time_serise_feature.shape
        time_serise_feature = self.dcn_guide(time_serise_feature)

        time_serise_feature = time_serise_feature.view(B, frame, C, H*W).permute(0, 2, 3, 1)
        time_serise_feature = self.conv1(time_serise_feature)
        time_serise_feature = time_serise_feature.view(B, frame*C, H*W, 1).squeeze(-1).view(B, frame*C, H, W)
        fusion_feature = self.up(time_serise_feature)

        return fusion_feature

class Cross_Attention(nn.Module):
    def __init__(self, dim, num_heads=8, qkv_bias=False, qk_scale=None, attn_drop=0., proj_drop=0., sr_ratio=1):
        super().__init__()
        assert dim % num_heads == 0, f"dim {dim} should be divided by num_heads {num_heads}."

        self.dim = dim
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = qk_scale or head_dim ** -0.5

        self.q = nn.Linear(dim, dim, bias=qkv_bias)
        self.kv = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        self.sr_ratio = sr_ratio
        if sr_ratio > 1:
            self.sr = nn.Conv2d(dim, dim, kernel_size=sr_ratio, stride=sr_ratio)
            self.norm = nn.LayerNorm(dim)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x_low, x_high, H, W):
        B, N, C = x_low.shape
        q = self.q(x_low).reshape(B, N, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)

        if self.sr_ratio > 1:
            x_ = x_high.permute(0, 2, 1).reshape(B, C, H, W)
            x_ = self.sr(x_).reshape(B, C, -1).permute(0, 2, 1)
            x_ = self.norm(x_)
            kv = self.kv(x_).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        else:
            kv = self.kv(x_high).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        k, v = kv[0], kv[1]

        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)

        return x

class Cross_Block(nn.Module):
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, qk_scale=None, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm, sr_ratio=1):
        super().__init__()
        self.norm1 = norm_layer(dim)
        self.norm2 = norm_layer(dim)
        self.attn = Cross_Attention(
            dim,
            num_heads=num_heads, qkv_bias=qkv_bias, qk_scale=qk_scale,
            attn_drop=attn_drop, proj_drop=drop, sr_ratio=sr_ratio)
        # NOTE: drop path for stochastic depth, we shall see if this is better than dropout here
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm3 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x_low, x_high, H, W):
        x = x_low + self.drop_path(self.attn(self.norm1(x_low), self.norm2(x_high), H, W))
        x = x + self.drop_path(self.mlp(self.norm3(x), H, W))
        return x

class Cross_Scale(nn.Module):
    def __init__(self, decoder_channel=64, num_heads=1, mlp_ratio=8, qkv_bias=True, qk_scale=None, attn_drop=0., sr_ratio=8):
        super().__init__()
        # F_h^1=PVT(F_h,F_l,F_l), F_l^1=PVT(F_l,F_h,F_h)
        # F_o=UANet(F_h^1,F_l^1)
        self.low_cross_block = Cross_Block(dim=decoder_channel, num_heads=num_heads, mlp_ratio=mlp_ratio, qkv_bias=True, qk_scale=None, attn_drop=attn_drop, sr_ratio=sr_ratio)
        self.high_cross_block = Cross_Block(dim=decoder_channel, num_heads=num_heads, mlp_ratio=mlp_ratio,qkv_bias=True, qk_scale=None, attn_drop=attn_drop, sr_ratio=sr_ratio)

    def forward(self, low_feature, high_feature):
        B, C, H, W = high_feature.shape
        low_feature = F.interpolate(low_feature, size=(H, W), mode='bilinear', align_corners=True)
        low_feature = rearrange(low_feature, "b c h w -> b (h w) c")
        high_feature = rearrange(high_feature, "b c h w -> b (h w) c")
        low_cross_scale = self.low_cross_block(low_feature, high_feature, H, W).view(B, H, W, C).permute(0, 3, 1, 2)
        high_cross_scale = self.high_cross_block(high_feature, low_feature, H, W).view(B, H, W, C).permute(0, 3, 1, 2)
        return low_cross_scale + high_cross_scale


class Building_Decoder(nn.Module):
    def __init__(self, decoder_channel, frame, num_classes):
        super().__init__()
        self.time_fusion5 = time_series_fusion(decoder_channel, frame)
        self.time_fusion4 = time_series_fusion(decoder_channel, frame)
        self.time_fusion3 = time_series_fusion(decoder_channel, frame)
        self.time_fusion2 = time_series_fusion(decoder_channel, frame)

        self.cross_scale5 = Cross_Scale(decoder_channel, sr_ratio=1)
        self.cross_scale4 = Cross_Scale(decoder_channel, sr_ratio=2)
        self.cross_scale3 = Cross_Scale(decoder_channel, sr_ratio=4)
        self.cross_scale2 = Cross_Scale(decoder_channel, sr_ratio=8)

        self.cgm = CGM()
        self.psm = PSM()

        self.ufm_layer4 = Uncertainty_Aware_Fusion_Module(high_channel=decoder_channel, low_channel=decoder_channel, out_channel=decoder_channel, num_classes=num_classes)
        self.ufm_layer3 = Uncertainty_Aware_Fusion_Module(high_channel=decoder_channel, low_channel=decoder_channel, out_channel=decoder_channel, num_classes=num_classes)
        self.ufm_layer2 = Uncertainty_Aware_Fusion_Module(high_channel=decoder_channel, low_channel=decoder_channel, out_channel=decoder_channel, num_classes=num_classes)
        self.ufm_layer1 = Uncertainty_Aware_Fusion_Module(high_channel=decoder_channel, low_channel=decoder_channel, out_channel=decoder_channel, num_classes=num_classes)


    def _feature_constraint(sefl, low_feature, high_feature, outputs):
        low_feature = F.normalize(low_feature, dim=1)
        high_feature = F.normalize(high_feature.detach(), dim=1)
        # print(low_feature.shape, high_feature.shape)
        outputs["loss"] += F.mse_loss(low_feature, high_feature)
        return outputs

    def forward(self, low_encoder_features_lists, outputs):
        low_layer5 = low_encoder_features_lists[-1][:, :-1]
        low_layer4 = low_encoder_features_lists[-2][:, :-1]
        low_layer3 = low_encoder_features_lists[-3][:, :-1]
        low_layer2 = low_encoder_features_lists[-4][:, :-1]

        low_layer5 = self.time_fusion5(low_layer5)
        low_layer4 = self.time_fusion4(low_layer4)
        low_layer3 = self.time_fusion3(low_layer3)
        low_layer2 = self.time_fusion2(low_layer2)
        outputs = self._feature_constraint(low_layer5, outputs["pre_high_features5"], outputs)
        outputs = self._feature_constraint(low_layer4, outputs["pre_high_features4"], outputs)
        outputs = self._feature_constraint(low_layer3, outputs["pre_high_features3"], outputs)
        outputs = self._feature_constraint(low_layer2, outputs["pre_high_features2"], outputs)



        low_layer5 = self.cross_scale5(low_layer5, outputs["pre_high_features5"])
        predict_5_down = F.interpolate(outputs["pre_high_building5"], low_layer5.size()[2:], mode='bilinear', align_corners=True)
        low_layer5 = self.psm(low_layer5,predict_5_down)
        low_layer5 = self.cgm(low_layer5,predict_5_down)


        low_layer4 = self.cross_scale4(low_layer4, outputs["pre_high_features4"])
        low_layer4, predict4 = self.ufm_layer4(low_layer4, low_layer5, predict_5_down)

        low_layer3 = self.cross_scale3(low_layer3, outputs["pre_high_features3"])
        low_layer3, predict3 = self.ufm_layer3(low_layer3, low_layer4, predict4)

        low_layer2 = self.cross_scale2(low_layer2, outputs["pre_high_features2"])
        low_layer2, predict2 = self.ufm_layer2(low_layer2, low_layer3, predict3)

        outputs["pre_low_building"] = F.interpolate(predict2, scale_factor=4, mode='bilinear', align_corners=True)
        outputs["pre_low_building3"] = F.interpolate(predict3, scale_factor=8, mode='bilinear', align_corners=True)
        outputs["pre_low_building4"] = F.interpolate(predict4, scale_factor=16, mode='bilinear', align_corners=True)
        # print(f"outputs[pre_low_building]: {predict2.shape}")

        outputs["pre_low_feature2"] = low_layer2
        outputs["pre_low_feature3"] = low_layer3
        outputs["pre_low_feature4"] = low_layer4
        outputs["pre_low_feature5"] = low_layer5


        return outputs
    

class Damage_Decoder(nn.Module):
    def __init__(self, decoder_channel, frame, damage_classes):
        super().__init__()
        # 需要添加 diff 的模块
        # self.cosine_different = Cross_Different()

        self.time_fusion5 = time_series_fusion(decoder_channel, frame)
        self.time_fusion4 = time_series_fusion(decoder_channel, frame)
        self.time_fusion3 = time_series_fusion(decoder_channel, frame)
        self.time_fusion2 = time_series_fusion(decoder_channel, frame)

        self.cross_scale5 = Cross_Scale(decoder_channel, sr_ratio=1)
        self.cross_scale4 = Cross_Scale(decoder_channel, sr_ratio=2)
        self.cross_scale3 = Cross_Scale(decoder_channel, sr_ratio=4)
        self.cross_scale2 = Cross_Scale(decoder_channel, sr_ratio=8)

        self.neck = FPN(in_channels=[decoder_channel, decoder_channel, decoder_channel, decoder_channel], out_channels=decoder_channel)
        self.decoder = SemanticFPNDecoder(channel = decoder_channel, feature_strides=[4, 8, 16, 32],num_classes=damage_classes)
        self.cgm = CGM()
        self.psm = PSM()

        self.ufm_layer4 = Uncertainty_Aware_Fusion_Module(high_channel=decoder_channel, low_channel=decoder_channel, out_channel=decoder_channel, num_classes=damage_classes)
        self.ufm_layer3 = Uncertainty_Aware_Fusion_Module(high_channel=decoder_channel, low_channel=decoder_channel, out_channel=decoder_channel, num_classes=damage_classes)
        self.ufm_layer2 = Uncertainty_Aware_Fusion_Module(high_channel=decoder_channel, low_channel=decoder_channel, out_channel=decoder_channel, num_classes=damage_classes)
        self.ufm_layer1 = Uncertainty_Aware_Fusion_Module(high_channel=decoder_channel, low_channel=decoder_channel, out_channel=decoder_channel, num_classes=damage_classes)


    def _feature_constraint(sefl, low_feature, high_feature, outputs):
        low_feature = F.normalize(low_feature, dim=1)
        high_feature = F.normalize(high_feature.detach(), dim=1)
        # print(low_feature.shape, high_feature.shape)
        outputs["loss"] += F.mse_loss(low_feature, high_feature)
        return outputs

    def forward(self, low_encoder_features_lists, outputs):
        low_layer5 = low_encoder_features_lists[-1][:, :-1] - low_encoder_features_lists[-1][:, -1:]
        low_layer4 = low_encoder_features_lists[-2][:, :-1] - low_encoder_features_lists[-2][:, -1:]
        low_layer3 = low_encoder_features_lists[-3][:, :-1] - low_encoder_features_lists[-3][:, -1:]
        low_layer2 = low_encoder_features_lists[-4][:, :-1] - low_encoder_features_lists[-4][:, -1:]

        low_layer5 = self.time_fusion5(low_layer5)
        low_layer4 = self.time_fusion4(low_layer4)
        low_layer3 = self.time_fusion3(low_layer3)
        low_layer2 = self.time_fusion2(low_layer2)

        predict5 = self.decoder(self.neck([low_layer2,low_layer3,low_layer4,low_layer5]))
        predict5_down = F.interpolate(predict5, low_layer5.size()[2:], mode='bilinear', align_corners=True)

        # outputs = self._feature_constraint(low_layer5, outputs["pre_high_features5"], outputs)
        low_layer5 = self.cross_scale5(low_layer5, outputs["pre_low_feature5"])

        low_layer5 = self.psm(low_layer5,predict5_down)
        low_layer5 = self.cgm(low_layer5,predict5_down)

        # outputs = self._feature_constraint(low_layer4, outputs["pre_high_features4"], outputs)
        low_layer4 = self.cross_scale4(low_layer4, outputs["pre_low_feature4"])
        low_layer4, predict4 = self.ufm_layer4(low_layer4, low_layer5, predict5_down)

        # outputs = self._feature_constraint(low_layer3, outputs["pre_high_features3"], outputs)
        low_layer3 = self.cross_scale3(low_layer3, outputs["pre_low_feature3"])
        low_layer3, predict3 = self.ufm_layer3(low_layer3, low_layer4, predict4)

        # outputs = self._feature_constraint(low_layer2, outputs["pre_high_features2"], outputs)
        low_layer2 = self.cross_scale2(low_layer2, outputs["pre_low_feature2"])
        low_layer2, predict2 = self.ufm_layer2(low_layer2, low_layer3, predict3)

        outputs["post_low_damage"] = F.interpolate(predict2, scale_factor=4, mode='bilinear', align_corners=True)
        outputs["post_low_damage3"] = F.interpolate(predict3, scale_factor=8, mode='bilinear', align_corners=True)
        outputs["post_low_damage4"] = F.interpolate(predict4, scale_factor=16, mode='bilinear', align_corners=True)
        outputs["post_low_damage5"] = F.interpolate(predict5, scale_factor=4, mode='bilinear', align_corners=True)

        # outputs[f"pre_low_feature2"] = low_layer2
        # outputs[f"pre_low_feature3"] = low_layer3
        # outputs[f"pre_low_feature4"] = low_layer4
        # outputs[f"pre_low_feature5"] = low_layer5


        return outputs
    


class LBDv19_S2(nn.Module):
    def __init__(self, backbone="pvt_v2_b2_s2", s2_inchannel=17, frame=16, num_classes=2, damage_classes=2, post_input="S2"):
        super().__init__()
        # 1 High RGB Net
        decoder_channel = 64
        self.high_net = UANet_pvt(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/36.pt"
        self.high_net = self._load_weight(self.high_net, pre_train_model_path)

        # 2 low-sentinel-2 Net
        self.low2_net = PVTv2_Sentinel(backbone=backbone, in_channels=s2_inchannel, de_channel=decoder_channel, num_classes=num_classes)
        self.low2_net = self._load_weight(self.low2_net, pre_train_model_path)

        # 3 time-serise data transfer to unity the color space
        self.fcn5 = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])
        self.fcn4 = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])
        self.fcn3 = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])
        self.fcn2 = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])

        # 4 pre-sentinel-2 to pre-buildings
        self.pre_low_building_decoder = Building_Decoder(decoder_channel, frame, num_classes)

        # 5 pre-post to buildings damage mapping
        self.post_low_damage_decoder = Damage_Decoder(decoder_channel, frame, damage_classes)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
    

    def _feature_transfer(self, time_serise_feature_lists):
        aligned_features = []
        for layer_index in range(len(time_serise_feature_lists)):
            B, total_frame, C, H, W = time_serise_feature_lists[layer_index].shape
            aligned_feature = []
            T15 = time_serise_feature_lists[layer_index][:, -2]
            for frame_index in range(total_frame):
                curr_feat = time_serise_feature_lists[layer_index][:, frame_index]  # [B, C, H, W]
                feat_pair = torch.cat([curr_feat, T15], dim=1)
                A = eval(f"self.fcn{layer_index+2}")[frame_index](feat_pair)
                aligned_feat = torch.mul(curr_feat, A).squeeze(1)
                aligned_feature.append(aligned_feat)
            aligned_feature = torch.stack(aligned_feature, dim=1) # [B, 17, C, H, W]
            aligned_features.append(aligned_feature)
        return aligned_features
    
    def _encoder_feature_constraint(self, inputs, device, outputs, low_encoder_features_lists):
        pre_label = inputs[3].unsqueeze(1).to(device).to(torch.float32)
        post_label = inputs[4].unsqueeze(1).to(device).to(torch.float32)
        B, _, H, W = pre_label.shape
        outputs["loss"] = 0
        for layer_index in range(len(low_encoder_features_lists)):

            B, frame, _, h, w = low_encoder_features_lists[layer_index].shape

            high_features = F.interpolate(outputs[f"pre_high_features{layer_index+2}"], size=(h, w), mode='bilinear', align_corners=True)
            outputs["loss"] += F.mse_loss(F.normalize(low_encoder_features_lists[layer_index][:, -2], dim=1), F.normalize(high_features.detach(), dim=1))

            T15 = F.interpolate(low_encoder_features_lists[layer_index][:, -2], size=(H, W), mode='bilinear')
            T16 = F.interpolate(low_encoder_features_lists[layer_index][:, -1], size=(H, W), mode='bilinear')

            pre_label = torch.where(pre_label == 1, 1, 0)
            post_label = torch.where(post_label == 1, 0, 1)

            # 计算损毁区域
            outputs["loss"] += F.mse_loss(F.normalize(T16 * post_label), F.normalize(T15.detach() * post_label), reduction='mean')

            for frame_id in range(frame - 2):
                frame_feature = F.normalize(low_encoder_features_lists[layer_index][:, frame_id])
                outputs["loss"] += F.mse_loss(frame_feature, F.normalize(low_encoder_features_lists[layer_index][:, -2].detach()), reduction='mean')

        return outputs
    

    def forward(self, inputs, device):
        outputs = {}
        B, _, H, W = inputs[0].to(device).to(torch.float32).shape
        outputs = self.high_net(inputs[0].to(device).to(torch.float32), device, mode="pre", outputs=outputs)
        # high_features_down4 = F.interpolate(outputs["pre_high_features"], size=(H//4, W//4), mode='bilinear', align_corners=True)
        # predict_lists = [outputs["pre_high_building3"], outputs["pre_high_building4"], outputs["pre_high_building5"]]

        s2_images = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
        B, frame, _, h, w = s2_images.shape
        low_encoder_features_lists = self.low2_net(images=s2_images)
        # print(low_encoder_features_lists[0].shape, low_encoder_features_lists[1].shape, low_encoder_features_lists[2].shape, low_encoder_features_lists[3].shape)

        low_encoder_features_lists = self._feature_transfer(low_encoder_features_lists)
        # print(low_encoder_features_lists[0].shape, low_encoder_features_lists[1].shape, low_encoder_features_lists[2].shape, low_encoder_features_lists[3].shape)
        # if self.training:
        outputs = self._encoder_feature_constraint(inputs, device, outputs, low_encoder_features_lists)


        outputs = self.pre_low_building_decoder(low_encoder_features_lists, outputs)
        outputs = self.post_low_damage_decoder(low_encoder_features_lists, outputs)

        return outputs

if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '3'
    # deep_model = UANet_Sentinel().cuda()

    deep_model = LBDv19_S2(backbone="pvt_v2_b2_s2", s2_inchannel=17, frame=16, num_classes=2, damage_classes=2, post_input="S2").cuda().train()
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384).cuda()
    low_images1 = torch.rand(bs, 16, 17, 48, 48).cuda()
    low_images2 = torch.rand(bs, 1, 17, 48, 48).cuda()
    pre_label = torch.rand(bs, 384, 384).cuda()
    post_label = torch.rand(bs, 384, 384).cuda()
    inputs = [hight_image, low_images1, low_images2, pre_label, post_label]
    device = torch.device("cuda")

    outputss = deep_model(inputs, device)

    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
