from thop import profile
import torch
import torch.nn as nn
import timm
import numpy as np
import torch.nn.functional as F
# from HRNet2 import HRNet_S2
from HRNet import HRNet_single





class residual_up_decoder(nn.Module):
    def __init__(self, decoder_channnel):
        super(residual_up_decoder, self).__init__()
        self.conv_path = nn.Sequential(
            nn.BatchNorm2d(decoder_channnel),
            nn.ReLU(),
            nn.Conv2d(decoder_channnel, decoder_channnel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channnel),
            nn.ReLU(),
            nn.Conv2d(decoder_channnel, decoder_channnel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channnel),
            nn.ReLU(),
        )

        self.res = nn.Conv2d(decoder_channnel, decoder_channnel, kernel_size=1)
        self.deconv = nn.Sequential(
            nn.ConvTranspose2d(decoder_channnel, decoder_channnel, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
            )

    def forward(self, x):
        x = self.conv_path(x) + self.res(x)
        x = self.deconv(x)
        return x

class HRNet(nn.Module):
    def __init__(self, pretrained=False, s2_inchannel=18, num_classes=3):
        super(HRNet, self).__init__()
        self.num_classes = num_classes
        self.rgb_backbone = timm.create_model('hrnet_w48', pretrained=pretrained, features_only=True, out_indices=(1, 2, 3, 4))
        self.pre_train_model_path = "./net/hrnetv2_w48_imagenet_pretrained.pth"
        self.rgb_backbone = self._load_weight(self.rgb_backbone)
        last_inp_channels = np.int32(np.sum(self.rgb_backbone.feature_info.channels()))
        decoder_channnel = self.rgb_backbone.feature_info.channels()[0]

        self.rgb_decoder_layer = nn.Sequential(
            nn.Conv2d(in_channels=last_inp_channels, out_channels=decoder_channnel, kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
        )
        self.class_layer1 = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channnel, out_channels=decoder_channnel, kernel_size=3, stride=1, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=decoder_channnel, out_channels=self.num_classes, kernel_size=1, stride=1, padding=0),
        )

    def _load_weight(self, net):
        if self.pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.pre_train_model_path)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
    
    def decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x

    def forward(self, images, device, mode="pre", output={}):
        B, C, H, W = images.shape
        high_features = self.rgb_backbone(images.to(device).to(torch.float32))
        high_features = self.decoder(high_features, self.rgb_decoder_layer)
        high_outs = self.class_layer1(high_features)
        high_outs = F.interpolate(high_outs, size=(H, W), mode='bilinear', align_corners=True)
        output[f"{mode}_high_building"] = high_outs[:, :self.num_classes-1]
        output[f"{mode}_high_gray"] = high_outs[:, self.num_classes-1:]
        output[f"{mode}_high_features"] = high_features
        return output


class cross_fusion(nn.Module):
    def __init__(self, in_channels, frame):
        super(cross_fusion, self).__init__()
        # print(in_channels)
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=in_channels, out_channels=int(in_channels*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.BatchNorm2d(in_channels*frame),
            nn.ReLU(inplace=True),
        )
        self.conv2 = nn.Conv2d(in_channels=in_channels, out_channels=in_channels, kernel_size=1, stride=1)
            
    def forward(self, s2_time_serise_feature):
        B, frame, C, H, W = s2_time_serise_feature.shape
        s2_time_serise_feature1 = s2_time_serise_feature.view(B, frame, C, H*W).permute(0, 2, 3, 1)
        s2_time_serise_feature1 = self.conv1(s2_time_serise_feature1) # B C*FRAM H*W 1 
        s2_time_serise_feature1 = s2_time_serise_feature1.view(B, frame*C, H*W, 1).squeeze(-1).view(B, frame, C, H, W).view(B*frame, C, H, W)
        s2_time_serise_feature1 = self.conv2(s2_time_serise_feature1)
        s2_time_serise_feature2 = s2_time_serise_feature + s2_time_serise_feature1.view(B, frame, C, H, W)
        return s2_time_serise_feature2

class HRNet_S2(nn.Module):
    def __init__(self, pretrained, s2_inchannel=17, frame=16, num_classes=3, damage_classes=2):
        super(HRNet_S2, self).__init__()
        self.num_classes = num_classes

        self.high_hrnet = HRNet(num_classes=num_classes)
        # self.pre_train_model_path = "./result/xBD_512/S2_HRNet2/70.pt"
        self.pre_train_model_path = "./result/Gaza/S2_HRNet2/1_high_extract/26.pt"

        # self.high_hrnet = HRNet_single(num_classes=num_classes)
        # self.pre_train_model_path = "./result/Gaza/S2_HRNet2/1_high_single_extract/12.pt"

        self.high_hrnet = self._load_weight(self.high_hrnet)
        self.setup()

        self.low_hrnet = HRNet(num_classes=num_classes)
        # self.pre_train_model_path = "./result/xBD_512/S2_HRNet2/70.pt"
        self.pre_train_model_path = "./result/Gaza/S2_HRNet2/1_high_extract/26.pt"

        # self.low_hrnet = HRNet_single(num_classes=num_classes)
        # self.pre_train_model_path = "./result/Gaza/S2_HRNet2/1_high_single_extract/12.pt"        
        self.low_hrnet = self._load_weight(self.low_hrnet)
        self.s2_backbone = self.low_hrnet.rgb_backbone
        self.s2_backbone.conv1.stride = 1
        self.s2_backbone.conv2.stride = 1
        conv1_weights = self.s2_backbone.conv1.weight
        # print(conv1_weights_mean.shape)
        conv1_weights_mean = conv1_weights.mean(dim=1, keepdim=True)
        new_conv1_weights = conv1_weights_mean.repeat(1, s2_inchannel, 1, 1)
        self.s2_backbone.conv1.in_inchannel = s2_inchannel
        self.s2_backbone.conv1.weight.data = new_conv1_weights

        decoder_channnel = self.s2_backbone.feature_info.channels()[0]

        self.s2_decoder_layer = self.low_hrnet.rgb_decoder_layer
        self.cross_fusion = cross_fusion(decoder_channnel, frame=frame)
        self.s2_class_layer = self.low_hrnet.class_layer1
        self.up3 = residual_up_decoder(decoder_channnel)
        self.up2 = residual_up_decoder(decoder_channnel)
        self.up1 = residual_up_decoder(decoder_channnel)

        self.dcross_fusion = cross_fusion(decoder_channnel, frame=frame+1)
        self.s2_demage_layer = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channnel*2, out_channels=decoder_channnel, kernel_size=3, stride=1, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=decoder_channnel, out_channels=damage_classes, kernel_size=3, stride=1, padding=1),
        )
        self.dup3 = residual_up_decoder(decoder_channnel)
        self.dup2 = residual_up_decoder(decoder_channnel)
        self.dup1 = residual_up_decoder(decoder_channnel)
        self.s2_demage_layer1 = nn.Conv2d(in_channels=2, out_channels=damage_classes, kernel_size=3, stride=1, padding=1)

    def setup(self):
        self.high_hrnet.train()
        for name, param in self.high_hrnet.named_parameters():
            param.requires_grad = False

    def _load_weight(self, net):
        if self.pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net

    def decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x

    def a_loss(self, T_feature, S_feature):
        l = nn.SmoothL1Loss(reduction="mean")
        loss = l(T_feature, S_feature)
        return loss

    def forward(self, inputs, device):
        output = {}
        with torch.no_grad():
            output = self.high_hrnet(inputs[0].to(device).to(torch.float32), device, mode="pre")
            # output = self.high_hrnet(inputs[1].to(device).to(torch.float32), device, mode="post", output=output)

        s2_images = torch.cat((inputs[2].to(device).to(torch.float32), inputs[3].to(device).to(torch.float32)), dim=1)
        B, frame, C, H, W = s2_images.shape
        s2_images = s2_images.view(B*frame, C, H, W)
        low_features = self.s2_backbone(s2_images)
        low_features0 = self.decoder(low_features, self.s2_decoder_layer).view(B, frame, -1, H, W)

        low_features = self.cross_fusion(low_features0[:, :-1, :, :, :])
        low_features = torch.mean(low_features, dim=1, keepdim=False)
        low_features = self.up3(low_features)
        low_features = self.up2(low_features)
        low_features = self.up1(low_features)
        low_outs = self.s2_class_layer(low_features)
        low_outs = F.interpolate(low_outs, size=(384, 384), mode='bilinear', align_corners=True)
        output["pre_low_building"] = low_outs[:, :self.num_classes-1]
        output["pre_low_gray"] = low_outs[:, self.num_classes-1:]
        output["pre_low_features"] = low_features
        output["loss"] = self.a_loss(F.interpolate(output["pre_high_features"], size=(384, 384), mode='bilinear', align_corners=True), low_features)
        return output

        # 第四种，引入高分辨率灾前的特征和分割结果
        low_features1 = self.dcross_fusion(low_features0)[:, -1, :, :, :]
        low_features = self.dup3(low_features1)
        low_features = self.dup2(low_features)
        low_features = self.dup1(low_features)
        high_probility = inputs[-2].to(device).to(torch.float32).unsqueeze(1)
        low_outs = self.s2_demage_layer(torch.cat((low_features, F.interpolate(output["pre_high_features"], size=(384, 384), mode='bilinear', align_corners=True)*high_probility), dim=1))
        
        # high_probility = torch.softmax(output["pre_high_building"], dim=1)[:, 1:, :, :]#.unsqueeze(dim=1)
        # low_outs = self.s2_demage_layer(torch.cat((low_features, F.interpolate(output["pre_high_features"], size=(384, 384), mode='bilinear', align_corners=True)*high_probility), dim=1))
        low_outs = F.interpolate(low_outs, size=(384, 384), mode='bilinear', align_corners=True)
        # torch.unsqueeze
        low_outs = self.s2_demage_layer1(low_outs*high_probility)
        output["post_low_damage"] = low_outs
        output["post_low_features"] = low_features
        return output

if __name__ == "__main__":
    deep_model = HRNet_S2(pretrained=False, s2_inchannel=17, num_classes=3).cuda()
    
    hight_image = torch.rand(1, 3, 384, 384).cuda()
    low_images1 = torch.rand(1, 16, 17, 48, 48).cuda()
    low_images2 = torch.rand(1, 1, 17, 48, 48).cuda()
    inputs = [hight_image, hight_image, low_images1, low_images2,]
    device = torch.device("cuda")

    outputs = deep_model(inputs, device)
    # for output in outputs:
    #     print(output.shape)

    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
