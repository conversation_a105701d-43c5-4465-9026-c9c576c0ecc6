import os
from glob import glob
from osgeo import gdal
import numpy as np
import shutil 
# 指定目录路径
file_paths = glob("/mnt/d2/zxq/BDS12/dataset/palisades/**/S2_Python/S2_post/**.tif")

# 遍历目录下的所有文件
for file_path in file_paths:
    os.rename(file_path, file_path.replace("_pre_", "_post_"))
    print(file_path, file_path.replace("_pre_", "_post_"))
    # x = input()

# def remove_filnal_s1():
#     file_paths = glob("/mnt/d2/zxq/BDS12/dataset/palisades/**/images/*pre*.tif")
#     for file_path in file_paths:
#         s1_file_paths = glob(file_path.replace("images", "S1").replace(".tif", "**.tif"))
#         s1_file_paths.sort()
#         # print(s1_file_paths)
#         srt = s1_file_paths[-1]
#         dst = srt.replace("/S1/", "/S1_post/")
#         os.makedirs(os.path.dirname(dst), exist_ok=True)
#         print(srt, dst)
#         shutil.move(srt, dst)

# remove_filnal_s1()

def save_to_tif(pred_result, save_path, test_image_path):
    """
    pred_result: 需要保存的图像
    save_path: 保存路径
    test_image_path: 空间参考系
    """
    img = pred_result
    im_geotrans, im_prof = [], []
    tif = gdal.Open(os.path.join(test_image_path))
    im_geotrans.append(tif.GetGeoTransform())
    im_prof.append(tif.GetProjection())
    if 'uint8' in img.dtype.name:
        datatype = gdal.GDT_Byte
    elif 'uint16' in img.dtype.name:
        datatype = gdal.GDT_UInt16
    else:
        datatype = gdal.GDT_Float32
    # print(img.dtype.name)
    #判读数组维数
    if len(img.shape) == 3:
        im_bands, im_height, im_width = img.shape
    else:
        im_bands, (im_height, im_width) = 1, img.shape

    #创建文件
    driver  = gdal.GetDriverByName("GTiff")                #数据类型必须有，因为要计算需要多大内存空间
    dataset = driver.Create(save_path, im_width, im_height, im_bands, datatype)
    dataset.SetGeoTransform(im_geotrans[-1])              #写入仿射变换参数
    dataset.SetProjection(im_prof[-1])                    #写入投影
    if im_bands == 1:
        dataset.GetRasterBand(1).WriteArray(img)  #写入数组数据
    else:
        for i in range(im_bands):
            dataset.GetRasterBand(i+1).WriteArray(img[i])
    del dataset
    # print('保存成功')


# 对 gaza 数据集 添加灾前标签
# small_tif_paths = glob("/mnt/d2/zxq/BDS12/dataset/gaza/**/labels/**.tif")
# for small_tif_path in small_tif_paths:
#     save_path = small_tif_path.replace("_post_", "_pre_")
#     os.makedirs(os.path.dirname(save_path), exist_ok=True)
#     # print(small_tif_path, save_path)
#     dataset = gdal.Open(small_tif_path)
#     geotransform = dataset.GetGeoTransform()
#     projection = dataset.GetProjection()
#     image_array = dataset.ReadAsArray()
#     image_array = np.array(image_array).astype(np.float32)
#     image_array = np.where(image_array >= 1, 1, 0)
#     save_to_tif(image_array, save_path, small_tif_path)
    # print(save_path)
    # x=input()