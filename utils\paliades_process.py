import math
from osgeo import gdal, osr
import numpy as np
import os
from glob import glob
import matplotlib.pyplot as plt
from multiprocessing import Pool
import shutil
import random
from tqdm import tqdm
from PIL import Image
from shapely.geometry import Polygon, box
import geopandas as gpd
import rasterio
from rasterio.windows import from_bounds
import os
def get_event_shp(dataset_paths, output_shp_path, epsg=4326):
    # 定义SHP文件的schema
    polygons = []
    filenames = []

    for dataset_path in dataset_paths:
        # 打开TIFF文件
        dataset = gdal.Open(dataset_path)
        if not dataset:
            print(f"无法打开文件: {dataset_path}")
            continue
        
        # 获取地理变换和边界范围
        geotransform = dataset.GetGeoTransform()
        width = dataset.RasterXSize
        height = dataset.RasterYSize

        # 左上、右上、右下、左下的坐标
        min_x = geotransform[0]
        max_y = geotransform[3]
        max_x = min_x + width * geotransform[1]
        min_y = max_y + height * geotransform[5]

        # 构建边界多边形
        bbox = Polygon([
            (min_x, min_y),
            (max_x, min_y),
            (max_x, max_y),
            (min_x, max_y),
            (min_x, min_y)
        ])

        # 存储多边形和文件名
        polygons.append(bbox)
        filenames.append(os.path.basename(dataset_path))
        dataset = None  # 关闭GDAL数据集

    # 将数据转换为GeoDataFrame
    gdf = gpd.GeoDataFrame({'geometry': polygons, 'filename': filenames})

    # 设置坐标系为EPSG:4326
    gdf.set_crs(f"EPSG:{epsg}", allow_override=True, inplace=True)

    # 转换为WGS84坐标系
    gdf = gdf.to_crs(epsg=epsg)

    # 保存为Shapefile，确保所有相关文件都生成
    gdf.to_file(output_shp_path)

    print(f"边界 Shapefile 已保存: {output_shp_path}")

def tif_resample(input_file, output_file, xRes=0.5, yRes=0.5):
    gdal.Warp(output_file, input_file, xRes=xRes,  yRes=yRes, resampleAlg=gdal.GRA_NearestNeighbour, dstSRS="EPSG:32611")

def get_damage_path(high_resample_tif_paths, region_id):
    damage_path = []
    nodamage_path = []
    # print(high_resample_tif_paths)
    for id, high_resample_tif_path in enumerate(tqdm(high_resample_tif_paths, desc=f"get {region_id} damage path")):
        lab_dst_path = high_resample_tif_path.replace(f"High/resample2/turkey-earthquake{region_id}", "High/target").replace("_pre_disaster", "_building_damage")
        # print(lab_dst_path)
        lab = Image.open(lab_dst_path)
        lab = np.array(lab).astype(np.uint8)
        lab = np.where(lab > 1, 1, 0)
        if np.sum(np.array(lab)) == 0:
            nodamage_path.append(high_resample_tif_path)
        else:
            damage_path.append(high_resample_tif_path)

    return damage_path, nodamage_path

def split_train_test(high_resample_tif_paths):
    random.seed(0)
    random.shuffle(high_resample_tif_paths)
    train_num = int(len(high_resample_tif_paths) * 0.8)
    for id, high_resample_tif_path in enumerate(high_resample_tif_paths):
        if id <= train_num:
            mode = "train"
        else:
            mode = "test"
        img_dst_path = high_resample_tif_path.replace(f"High/Patch", f"dataset/{mode}/images")
        os.makedirs(os.path.dirname(img_dst_path), exist_ok=True)
        # print(high_resample_tif_path, img_dst_path)
        shutil.copyfile(high_resample_tif_path, img_dst_path)
    return

def clip_high_tif_2_patch(high_tif_path):
    # 读取 SHP
    shp = gpd.read_file("/mnt/E/Dataset/LA2025/palisades-fire/paliades_extent.shp")
    shp_union = shp.geometry.unary_union  # 合并所有多边形

    dataset = gdal.Open(high_tif_path)
    geotransform = dataset.GetGeoTransform()
    projection = dataset.GetProjection()

    image_array = dataset.ReadAsArray()
    image_array = np.array(image_array).astype(np.float32)
    _, w, h = image_array.shape
    print(h, w)
    split_withd = 384
    r           = 0
    row         = int(np.ceil((h-r*2)/(split_withd-r*2)))
    col         = int(np.ceil((w-r*2)/(split_withd-r*2)))
    # print(h, w, split_withd, r, row, col)
    args = []
    for i in range(row):
        for j in range(col):
            # if i < (row-1) and j < (col-1):
            x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
            y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd         
        
            # #-------------------------------------------------#
            # #   裁剪的图像在最后一行
            # elif i == row-1 and j < (col-1):
            #     x1, x2 = h-split_withd, h
            #     y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd
            
            # #-------------------------------------------------#
            # #   裁剪的图像在最后一列
            # elif i < row-1 and j == (col-1):
            #     x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
            #     y1, y2 = w-split_withd, w

            # #-------------------------------------------------#
            # #   裁剪的图像在右下角
            # elif i == row-1 and j == (col-1):
            #     x1, x2 = h-split_withd, h
            #     y1, y2 = w-split_withd, w

            min_x = geotransform[0] + x1 * geotransform[1]
            max_y = geotransform[3] + y1 * geotransform[5]
            max_x = min_x + split_withd * geotransform[1]
            min_y = max_y + split_withd * geotransform[5]          

            # 创建 patch 的边界框 Polygon
            patch_poly = box(min_x, min_y, max_x, max_y)

            # **只保留完全在 shp 内的 patch**
            if not shp_union.contains(patch_poly):
                continue  # 丢弃不完全在 shp 内的 patch

            patch_path = os.path.join(os.path.dirname(high_tif_path).replace("High/TIF", "High/Patch"), os.path.basename(high_tif_path).replace(".tif", f"_{i}_{j}.tif"))
            os.makedirs(os.path.dirname(patch_path), exist_ok=True)
            # args.append([patch_path, high_tif_path, [min_x, min_y, max_x, max_y]])
            # print(patch_path, high_tif_path, [min_x, min_y, max_x, max_y])
            # x = input()

            gdal.Warp(patch_path, high_tif_path, outputBounds=(min_x, min_y, max_x, max_y), dstSRS=projection)



def get_tif_bounds(tif_path):
    """获取TIF文件的地理范围 (min_x, min_y, max_x, max_y)"""
    dataset = gdal.Open(tif_path)
    if not dataset:
        raise ValueError(f"无法打开TIF文件: {tif_path}")
    
    gt = dataset.GetGeoTransform()
    width = dataset.RasterXSize
    height = dataset.RasterYSize

    min_x = gt[0]
    max_y = gt[3]
    max_x = min_x + width * gt[1]
    min_y = max_y + height * gt[5]
    
    # gdal以像素中心为基准，所以要确保坐标顺序正确
    if max_x < min_x: min_x, max_x = max_x, min_x
    if max_y < min_y: min_y, max_y = max_y, min_y
    
    return (min_x, min_y, max_x, max_y)

def is_small_in_big(small_bounds, big_bounds):
    """判断small tif的边界是否完全在big tif的边界内"""
    s_min_x, s_min_y, s_max_x, s_max_y = small_bounds
    b_min_x, b_min_y, b_max_x, b_max_y = big_bounds

    return (s_min_x >= b_min_x and  s_max_x <= b_max_x and  s_min_y >= b_min_y and  s_max_y <= b_max_y)


def clip_big_tif_by_small(small_tif_path, big_tif_path, save_path):
    small_bounds = get_tif_bounds(small_tif_path)
    s_min_x, s_min_y, s_max_x, s_max_y = small_bounds
    
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    # options = gdal.WarpOptions(format="GTiff", outputBounds=(s_min_x, s_min_y, s_max_x, s_max_y), cutlineBlend=0, dstNodata=255, outputType=gdal.GDT_Float32, options=["PHOTOMETRIC=MINISBLACK"])
    options = gdal.WarpOptions(
        format="GTiff",
        outputBounds=small_bounds,
        xRes=8, yRes=8,  # 明确指定输出分辨率
        resampleAlg='near',  # 最近邻，保持原始值（比如分类或整数波段）
        dstNodata=None,
        outputType=gdal.GDT_Float32
    )
    gdal.Warp(destNameOrDestDS=save_path,  srcDSOrSrcDSTab=big_tif_path,  options=options)

def clip_big_tif_by_small_margin(small_tif_path, big_tif_path, save_path):
    small_bounds = get_tif_bounds(small_tif_path)
    s_min_x, s_min_y, s_max_x, s_max_y = small_bounds
    aligned_left = math.floor(s_min_x / 10) * 10
    aligned_top  = math.ceil(s_max_y  / 10) * 10
    # 计算右下角坐标
    aligned_right = aligned_left + 22*10
    aligned_bottom = aligned_top - 22*10

    output_bounds = (aligned_left, aligned_bottom, aligned_right, aligned_top)
    contains = (aligned_left <= s_min_x and
                aligned_right >= s_max_x and
                aligned_top >= s_max_y and
                aligned_bottom <= s_min_y)    
    if not contains:
        raise ValueError(
            f"[裁剪范围错误] 输出裁剪区域未能完全包含高分图像范围。\n"
            f"输出裁剪 bounds: {output_bounds}\n"
            f"高分影像 bounds: {(s_min_x, s_min_y, s_max_x, s_max_y)} \n"
            f"[保存路径]{save_path}"
        )
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    # options = gdal.WarpOptions(format="GTiff", outputBounds=(s_min_x, s_min_y, s_max_x, s_max_y), cutlineBlend=0, dstNodata=255, outputType=gdal.GDT_Float32, options=["PHOTOMETRIC=MINISBLACK"])
    options = gdal.WarpOptions(
        format="GTiff",
        outputBounds=output_bounds,
        xRes=10, yRes=10,  # 明确指定输出分辨率
        resampleAlg='near',  # 最近邻，保持原始值（比如分类或整数波段）
        dstNodata=None,
        outputType=gdal.GDT_Float32
    )
    gdal.Warp(destNameOrDestDS=save_path,  srcDSOrSrcDSTab=big_tif_path,  options=options)


def clip_big_tif_by_small_byrasterio(small_tif_path, big_tif_path, save_path):
    # 打开小图，获取其边界坐标和仿射变换
    with rasterio.open(small_tif_path) as small_src:
        bounds = small_src.bounds
        dst_transform = small_src.transform
        dst_crs = small_src.crs
        width = small_src.width
        height = small_src.height

    # 打开大图，获取窗口
    with rasterio.open(big_tif_path) as big_src:
        # 坐标系检查
        if big_src.crs != dst_crs:
            raise ValueError("CRS 不一致，请先重投影小图或大图")

        # 获取裁剪窗口（窗口坐标）
        window = from_bounds(*bounds, transform=big_src.transform)
        window = window.round_offsets().round_lengths()

        # 读取窗口数据
        data = big_src.read(window=window)

        # 获取新变换
        transform = rasterio.windows.transform(window, big_src.transform)

        # 保存裁剪结果
        profile = big_src.profile.copy()
        profile.update({
            'height': data.shape[1],
            'width': data.shape[2],
            'transform': transform
        })

        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with rasterio.open(save_path, 'w', **profile) as dst:
            dst.write(data)

    print(f"裁剪完成，保存至：{save_path}")


def get_espg(path):
    d = gdal.Open(path)
    proj = osr.SpatialReference(wkt=d.GetProjection())
    # print(proj)
    space = proj.GetAttrValue('AUTHORITY',1)
    # print(space)
    return space

if __name__ == "__main__":
    event_name = "palisades-fire"
    event_base_path = f"/mnt/E/Dataset/LA2025/{event_name}/"


    # 第1步：将灾前高分辨率图像裁剪成 384x384 的小图像
    # high_tif_paths = glob(os.path.join(event_base_path, "High/TIF/*pre*.tif"))
    # high_tif_paths.sort()
    # for high_tif_path in high_tif_paths:
    #     clip_high_tif_2_patch(high_tif_path)

    # 第2步: 检查裁剪数据
    # get_event_shp(dataset_paths=glob(os.path.join(event_base_path, "dataset/test/images/*pre*.tif")), output_shp_path="/mnt/E/Dataset/LA2025/palisades-fire/paliades_extent_clip_test.shp", epsg=32611)

    # 第3步：随机划分训练集和测试集
    # split_train_test(high_resample_tif_paths=glob(os.path.join(event_base_path, "High/Patch/*pre*.tif")))

    # 第4步：裁剪灾后影像和标签
    # small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images/*pre*.tif"))
    # big_tif_path = glob(os.path.join(event_base_path, "High/TIF/*post*.tif"))[0]
    # args = []
    # for small_tif_path in small_tif_paths:
    #     save_name = os.path.basename(small_tif_path).replace("_pre_", "_post_")
    #     save_path = os.path.dirname(small_tif_path)
    #     os.makedirs(save_path, exist_ok=True)
    #     # print(small_tif_path, big_tif_path, os.path.join(save_path, save_name))
    #     args.append((small_tif_path, big_tif_path, os.path.join(save_path, save_name)))
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(clip_big_tif_by_small, args), total=len(args), desc="clip S2 tif"))

    # small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images/*pre*.tif"))
    # big_tif_path = glob(os.path.join(event_base_path, "labels_modify/*post*.tif"))[0]
    # args = []
    # for small_tif_path in small_tif_paths:
    #     save_name = os.path.basename(small_tif_path).replace("_pre_", "_post_")
    #     save_path = os.path.dirname(small_tif_path).replace("/images", "/labels")
    #     os.makedirs(save_path, exist_ok=True)
    #     # print(small_tif_path, big_tif_path, os.path.join(save_path, save_name))
    #     args.append((small_tif_path, big_tif_path, os.path.join(save_path, save_name)))
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(clip_big_tif_by_small, args), total=len(args), desc="clip pre label tif"))

    # 第5步：S2数据重采样
    # s2_tif_paths = glob(os.path.join(event_base_path, "S2_Python_L2A/**.tif"))
    # args = []
    # for s2_tif_path in s2_tif_paths:
    #     resample_path = s2_tif_path.replace("/S2_Python_L2A/", "/S2_Python_L2A_resample/")
    #     os.makedirs(os.path.dirname(resample_path), exist_ok=True)
    #     args.append((s2_tif_path, resample_path, 8, 8))
    #     tif_resample(s2_tif_path, resample_path, 8, 8)
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(tif_resample, args), total=len(args), desc="tif resample"))

    # 第6步：将S2数据裁剪成 384x384 的小图像
    small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images/*pre*.tif"))
    big_tif_paths = glob(os.path.join(event_base_path, "S2_Python/**.tif"))
    small_tif_paths.sort()
    big_tif_paths.sort()
    args = []
    print(len(small_tif_paths), len(big_tif_paths))
    small_count = 0
    for small_tif_path in tqdm(small_tif_paths):
        small_bounds = get_tif_bounds(small_tif_path)
        big_count = 0
        small_count += 1
        for big_id, big_tif_path in enumerate(big_tif_paths):
            big_bounds = get_tif_bounds(big_tif_path)
            # if is_small_in_big(small_bounds, big_bounds):
            if len(big_tif_paths)-1 == big_id:
                save_path = os.path.dirname(small_tif_path.replace("/images/", "/S2_Python_Margin/S2_post/"))
                # save_name = os.path.basename(small_tif_path).split(".")[0] + "_S2_" +os.path.basename(big_tif_path).split("_S2_")[-1]
                save_name = os.path.basename(small_tif_path).split(".")[0].replace("_pre_", "_post_") + "_S2_" + os.path.basename(big_tif_path).split("_S2_")[-1]

            else:
                save_path = os.path.dirname(small_tif_path.replace("/images/", "/S2_Python_Margin/S2/"))
                save_name = os.path.basename(small_tif_path).split(".")[0] + "_S2_" +os.path.basename(big_tif_path).split("_S2_")[-1]

            os.makedirs(save_path, exist_ok=True)
            big_count += 1
            # print(small_tif_path, big_tif_path, os.path.join(save_path, save_name))
            args.append((small_tif_path, big_tif_path, os.path.join(save_path, save_name)))
        # if len(args) > 100:
        #     break
    print(f"Total: {len(args)}")
    num_workers = os.cpu_count()
    with Pool(num_workers) as pool:
        list(tqdm(pool.starmap(clip_big_tif_by_small_margin, args), total=len(args), desc="clip S2 tif"))

    # 第5步：S1相关数据重采样
    # s1_tif_paths = glob(os.path.join(event_base_path, "S1**/**.tif"))
    # args = []
    # for s1_tif_path in s1_tif_paths:
    #     resample_path = s1_tif_path.replace("/S1", "/S1_resample")
    #     os.makedirs(os.path.dirname(resample_path), exist_ok=True)
    #     args.append((s1_tif_path, resample_path, 4, 4))
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(tif_resample, args), total=len(args), desc="tif resample"))

    # 第8步：将S1数据裁剪成 384x384 的小图像
    # small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images/*pre*.tif"))
    # big_tif_paths = glob(os.path.join(event_base_path, "S1_resample_ConIfg/**.tif"))
    # small_tif_paths.sort()
    # big_tif_paths.sort()
    # args = []
    # print(len(small_tif_paths), len(big_tif_paths))
    # small_count = 0
    # for small_tif_path in tqdm(small_tif_paths):
    #     small_bounds = get_tif_bounds(small_tif_path)
    #     big_count = 0
    #     small_count += 1
    #     for big_id, big_tif_path in enumerate(big_tif_paths):

    #         save_path = os.path.dirname(small_tif_path.replace("/images/", "/S1_ConIfg/"))
    #         save_name = os.path.basename(small_tif_path).split(".")[0] + "_S1_ConIfg_" +os.path.basename(big_tif_path).split("_S1_ConIfg")[0] + ".tif"

    #         # os.makedirs(save_path, exist_ok=True)
    #         big_count += 1
    #         print(small_tif_path, big_tif_path, os.path.join(save_path, save_name))
    #         args.append((small_tif_path, big_tif_path, os.path.join(save_path, save_name)))
    # print(f"Total: {len(args)}")
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(clip_big_tif_by_small, args), total=len(args), desc="clip S2 tif"))