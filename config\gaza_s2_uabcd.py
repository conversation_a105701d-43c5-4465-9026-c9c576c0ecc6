import torch
from torch import nn
import os
from glob import glob
import albumentations as albu
import argparse
from pathlib import Path
import numpy as np
from model.uabcd import UABCD
from loss.loss import OhemCELoss
from dataset.Gaza_S2 import Gaza_S2, Gaza_S1, dataset_collate

# =============================训练参数============================ #
batch_size = 12
lr = 1e-4
size = 384
epochs = 50
output_building = 2
output_damage = 2
data_name = "gaza"
model_name = "uabcd"
save_path = os.path.join("./result", data_name, model_name, "2_S2")
os.makedirs(save_path, exist_ok=True)

model = UABCD(backbone="pvt_v2_b2_s2", in_chans=17, latent_dim=8, num_classes=output_damage)
pre_train_model_path = None

criterion1 = OhemCELoss(thresh=0.7, n_min=batch_size*size*size//16, ignore_index=255)
criterion2 = nn.L1Loss()
criterion3 = nn.KLDivLoss(reduction="mean")

# 数据集加载 + 多GPU训练
device = 'cuda'
train_dataset = Gaza_S2('train')
val_dataset = Gaza_S2('test')
collate_fn = dataset_collate
# =============================训练参数============================ #


# =============================测试参数============================ #
data_name = "gaza"
model_name = "uabcd"
save_name = "2_S2"
test_base_path = f"./dataset/{data_name}/test/"
test_base_save_path = os.path.join("./result", data_name)
model_path = os.path.join("./result/", data_name, model_name, save_name, "35.pt")

def make_path(file_path):
    os.makedirs(file_path, exist_ok=True)
    return file_path

pre_high_image_save_path  = make_path(os.path.join(test_base_save_path, "pre_high_image"))
post_high_image_save_path  = make_path(os.path.join(test_base_save_path, "post_high_image"))
pre_s2_image_save_path = make_path(os.path.join(test_base_save_path, "pre_s2_image"))
post_s2_image_post_save_path = make_path(os.path.join(test_base_save_path, "post_s2_image"))
pre_label_save_path = make_path(os.path.join(test_base_save_path, "pre_label"))
post_label_save_path = make_path(os.path.join(test_base_save_path, "post_label"))
pre_s1_image_save_path = make_path(os.path.join(test_base_save_path, "pre_s1_image"))
post_s1_image_post_save_path = make_path(os.path.join(test_base_save_path, "post_s1_image"))

pre_high_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "pre_high_result"))
post_high_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "post_high_result"))
pre_s2_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "pre_s2_result"))
post_s2_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "post_s2_result"))
pre_s1_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "pre_s1_result"))
post_s1_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "post_s1_result"))
plot_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "plot"))
plot_feature_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "plot_feature"))


pre_high_image_paths = []
post_high_image_paths = []
pre_s2_image_paths = []
post_s2_image_paths = []
pre_label_paths = sorted(glob(os.path.join(test_base_path, "labels", "*gaza*post**.tif")))
post_label_paths = []
pre_s1_image_paths = []
post_s1_image_paths = []

for pre_label_path in pre_label_paths:
    image_path = pre_label_path.split(".tif")[0]
    pre_high_image_paths.append(image_path.replace("/labels/", "/images/").replace("_post_", "_pre_")+".tif")
    post_high_image_paths.append(image_path.replace("/labels/", "/images/").replace("_post_", "_pre_")+".tif")
    pre_s2_image_paths.append(image_path.replace("/labels/", "/S2/").replace("_post_", "_pre_"))
    post_s2_image_paths.append(image_path.replace("/labels/", "/S2_post/").replace("_post_", "_post_"))
    pre_s1_image_paths.append(image_path.replace("/labels/", "/S1/").replace("_post_", "_pre_"))
    post_s1_image_paths.append(image_path.replace("/labels/", "/S1_post/").replace("_post_", "_post_"))
    post_label_paths.append(pre_label_path.replace("_post_", "_post_"))


s2_mean_std = np.loadtxt("./dataset/gaza/gaza_S2_mean_std.txt")
s2_mean, s2_std = s2_mean_std[0,:], s2_mean_std[1,:]


label_keys = [
    [  0,   0,   0], # 0 非建筑物区域
    [255, 255, 255], # 1 未损毁
    [  0,   0, 255], # 2 Moderate
    [  0, 255,   0], # 3 Severe
    [255,   0,   0], # 4 Destroyed
]

# =============================测试参数============================ #
