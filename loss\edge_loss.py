import torch
import torch.nn.functional as F
from torch import nn

class EdgeFeatureConstraint(nn.Module):
    def __init__(self):
        super().__init__()
        # Sobel filters for edge detection
        sobel_x = torch.tensor([[[-1, 0, 1],
                                 [-2, 0, 2],
                                 [-1, 0, 1]]], dtype=torch.float32)
        sobel_y = torch.tensor([[[-1, -2, -1],
                                 [ 0,  0,  0],
                                 [ 1,  2,  1]]], dtype=torch.float32)

        self.register_buffer('sobel_x', sobel_x.unsqueeze(0))  # shape: [1, 1, 3, 3]
        self.register_buffer('sobel_y', sobel_y.unsqueeze(0))

    def _edge_map(self, x):
        if x.shape[1] > 1:
            x = x.mean(dim=1, keepdim=True)  # Convert to 1 channel if needed
        grad_x = F.conv2d(x, self.sobel_x, padding=1)
        grad_y = F.conv2d(x, self.sobel_y, padding=1)
        edge = torch.sqrt(grad_x ** 2 + grad_y ** 2 + 1e-6)

        # Normalize to [0, 1]
        edge = edge - edge.amin(dim=(2, 3), keepdim=True)
        edge = edge / (edge.amax(dim=(2, 3), keepdim=True) + 1e-6)
        return edge

    def _dice_loss(self, input, target, eps=1e-6):
        input = input.view(input.size(0), -1)
        target = target.view(target.size(0), -1)

        intersection = (input * target).sum(dim=1)
        union = input.sum(dim=1) + target.sum(dim=1)

        dice = (2.0 * intersection + eps) / (union + eps)
        dice = torch.clamp(dice, min=0, max=1)  # 防止溢出
        return 1.0 - dice.mean()

    def forward(self, high_features, low_features):
        # Average over temporal or channel dims
        high_features = torch.mean(high_features, dim=1, keepdim=True)
        low_features = torch.mean(low_features, dim=1, keepdim=True)

        # Compute edge maps
        high_edge = self._edge_map(high_features)
        low_edge = self._edge_map(low_features)

        # Compute Dice loss
        loss = self._dice_loss(low_edge, high_edge.detach())  # 不反向传播 high_edge
        return loss
