import torch
import torch.nn as nn
from diffusers import AutoencoderKL, UNet2DConditionModel

class HighResFeatureDenoiser(nn.Module):
    def __init__(self, vae_model_name="stabilityai/sd-vae-ft-mse", unet_model_name="google/ddpm-uncond"):
        super().__init__()
        # VAE: 负责高分影像编码和解码（这里你也可以换成你自己的VAE）
        # self.vae = AutoencoderKL.from_pretrained(vae_model_name)
        
        # UNet: 去噪模型，输入是带噪声的潜特征，输出去噪后的潜特征
        self.unet = UNet2DConditionModel.from_pretrained(unet_model_name)
        
        # beta schedule 可以自定义或用diffusers自带的
        self.num_timesteps = 1000

    def add_noise(self, latents, noise, timesteps):
        """
        根据扩散过程中的时间步t给潜特征加噪声
        latents: [B,C,H,W] 高分特征
        noise: 与latents同尺寸的噪声
        timesteps: [B] 每个样本的扩散步数（int）
        """
        # 这里用简单的线性beta schedule示例，你也可以用预定义的beta schedule
        betas = torch.linspace(0.0001, 0.02, self.num_timesteps).to(latents.device)
        alphas = 1 - betas
        alphas_cumprod = torch.cumprod(alphas, dim=0)

        sqrt_alphas_cumprod = torch.sqrt(alphas_cumprod[timesteps])[:, None, None, None]  # [B,1,1,1]
        sqrt_one_minus_alphas_cumprod = torch.sqrt(1 - alphas_cumprod[timesteps])[:, None, None, None]

        noisy_latents = sqrt_alphas_cumprod * latents + sqrt_one_minus_alphas_cumprod * noise
        return noisy_latents

    def forward(self, highres_imgs, timesteps):
        """
        highres_imgs: [B, 3, H, W] 高分RGB图像
        timesteps: [B] 采样的扩散步数
        """
        B, _, H, W = highres_imgs.shape
        # 编码成潜特征
        # latents = self.vae.encode(highres_imgs).latent_dist.sample()  # [B,C,H_latent,W_latent]

        C = 4  # VAE 通常输出 latent 通道数为 4
        H_lat, W_lat = H // 8, W // 8  # 模拟 VAE 的下采样效果
        latents = torch.randn(B, C, H_lat, W_lat, device=highres_imgs.device)

        # 采样噪声
        noise = torch.randn_like(latents)

        # 给潜特征加噪
        noisy_latents = self.add_noise(latents, noise, timesteps)

        # UNet去噪：输入 noisy_latents 和 timestep，输出噪声预测
        noise_pred = self.unet(noisy_latents, timesteps).sample

        # 训练目标：预测噪声 noise_pred ≈ noise

        return noise_pred, noise, noisy_latents, latents


if __name__ == "__main__":
    # 简单测试
    model = HighResFeatureDenoiser()
    dummy_imgs = torch.randn(2, 3, 256, 256)  # batch=2，256×256 RGB图
    timesteps = torch.randint(0, 1000, (2,))  # 随机采样时间步
    
    noise_pred, noise, noisy_latents, latents = model(dummy_imgs, timesteps)
    print("noise_pred.shape:", noise_pred.shape)
