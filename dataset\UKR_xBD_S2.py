import os
import re
import cv2
import imageio.v2 as imageio
import numpy as np
import random
from PIL import Image, ImageDraw
from matplotlib import pyplot as plt
from datetime import datetime, timedelta
import albumentations as albu

import torch
from torch.utils.data import Dataset
from torch.utils.data import DataLoader
import torch.nn.functional as F
import torchvision.transforms as transforms
from glob import glob
from osgeo import gdal, osr
from pyproj import Proj, Transformer
from functools import partial
from einops import rearrange

class UKR_xBD_S2(Dataset):
    def __init__(self, mode):
        self.mode = mode

        file = {"train": "train", "test": "test"}
        # pre_label_paths = glob(os.path.join("./dataset/UKRS2", file[self.mode], "labels", "**pre**.tif"))
        # pre_label_paths = glob(os.path.join("./dataset/xBDS2", file[self.mode], "labels", "*santa*pre**.tif"))
        # print(len(label_paths))
        pre_label_paths = glob(os.path.join("./dataset/palu", file[self.mode], "labels", "*palu*pre**.tif"))
        if self.mode == "train":
            pre_label_paths += glob(os.path.join("./dataset/palu", "hold", "labels", "*palu*pre**.tif"))
            # pre_label_paths += glob(os.path.join("./dataset/xBDS2", file[self.mode], "labels", "*santa*pre**.tif"))

        # self.s2_mean_std = np.loadtxt("./dataset/xBDS2/mean_std.txt")
        # self.s2_mean_std = np.loadtxt("./dataset/xBDS2/xbd_santa_harvey_S2_mean_std.txt")
        # self.s2_mean_std = np.loadtxt("./dataset/xBDS2/xbd_santa_harvey_S2_mean_std.txt")
        self.s2_mean_std = np.loadtxt("./dataset/palu/palu_s2_mean_std.txt")
        self.s2_mean, self.s2_std = self.s2_mean_std[0,:], self.s2_mean_std[1,:]

        self.pre_high_image_paths = []
        self.post_high_image_paths = []
        self.pre_s2_image_paths = []
        self.post_s2_image_paths = []
        self.pre_label_paths = []
        self.post_label_paths = []

        for item, pre_label_path in enumerate(pre_label_paths):
            image_name = pre_label_path.split(".tif")[0]
            # print(image_name.split("/labels/")[1].split("_disaster")[0])
            # palu_cloud_names = [f"palu-tsunami_{str(id).zfill(8)}_pre" for id in [3, 4, 6, 7, 8, 16, 18, 21, 32, 143]]
            # palu_cloud_names = [f"palu-tsunami_{str(id).zfill(8)}_pre" for id in [3, 4, 6, 7, 8, 12, 16, 18, 21, 32, 41, 96, 117, 120, 143, 192, 194]]
            palu_cloud_names = [f"palu-tsunami_{str(id).zfill(8)}_pre" for id in [192, 194]] # 1211
            # print(palu_cloud_names)
            if image_name.split("/labels/")[1].split("_disaster")[0] in palu_cloud_names:
                # print(image_name, image_name.split("/labels/")[1].split("_disaster")[0])
                continue
            self.pre_high_image_paths.append(image_name.replace("labels", "images"))
            self.post_high_image_paths.append(image_name.replace("labels", "images").replace("pre", "post"))
            self.pre_s2_image_paths.append(image_name.replace("pre", "pre").replace("labels", "S2"))
            self.post_s2_image_paths.append(image_name.replace("labels", "S2_post")).replace("pre", "post")
            self.pre_label_paths.append(pre_label_path)
            self.post_label_paths.append(pre_label_path.replace("pre", "post"))
        
        self.pre_high_image_paths = np.array(self.pre_high_image_paths)
        self.post_high_image_paths = np.array(self.post_high_image_paths)
        self.pre_s2_image_paths = np.array(self.pre_s2_image_paths)
        self.post_s2_image_paths = np.array(self.post_s2_image_paths)
        self.pre_label_paths = np.array(self.pre_label_paths)
        self.post_label_paths = np.array(self.post_label_paths)


    def __len__(self):
        return len(self.pre_label_paths)        

    def _load_high_image(self, image_path):
        high_image_paths = glob(os.path.join(image_path + ".tif"))
        high_image_paths.sort()
        # print(high_image_paths)
        if ".tif" in high_image_paths[-1]:
            dataset = gdal.Open(high_image_paths[-1])
            high_image = dataset.ReadAsArray()
        else:
            high_image = Image.open(high_image_paths[-1])
            high_image = np.transpose(np.array(high_image), (2, 0, 1))

        high_image = torch.from_numpy(np.array(high_image).astype(dtype=np.float32)/255.)
        return high_image, high_image_paths[-1]

    def _add_lon_lat_band(self, gdal_dataset, x_size, y_size):
        image_array = gdal_dataset.ReadAsArray()
        # 获取UTM的投影信息
        source_srs = osr.SpatialReference()
        source_srs.ImportFromWkt(gdal_dataset.GetProjectionRef())

        # 创建WGS84的目标投影信息
        target_srs = osr.SpatialReference()
        target_srs.ImportFromEPSG(4326)  # WGS84坐标系

        # 增加经纬度
        geotransform = gdal_dataset.GetGeoTransform()
        # print(geotransform)
        # 创建坐标转换对象
        transformer = osr.CoordinateTransformation(source_srs, target_srs)

        lon = np.linspace(geotransform[0], geotransform[0] + geotransform[1] * x_size, x_size)
        lat = np.linspace(geotransform[3] + geotransform[5] * y_size, geotransform[3], y_size)
        # 创建坐标点的二维数组
        lon_lat_points = np.array([[l, la] for la in lat for l in lon])

        # 进行坐标转换
        transformed_points = transformer.TransformPoints(lon_lat_points)

        # 分离转换后的经纬度
        lon = np.array([point[0] for point in transformed_points]).reshape(1, x_size, y_size)
        lat = np.array([point[1] for point in transformed_points]).reshape(1, x_size, y_size)
        # print(lon.shape, lat.shape)
        # x = input()
        image_array = np.concatenate((image_array, lon, lat), axis=0)
        return image_array
    
    def _add_time_band(self, image_array, x_size, y_size, high_time_str, low_time_str):
        high_time = datetime.strptime(high_time_str, "%Y%m%d")
        low_time = datetime.strptime(low_time_str, "%Y%m%d")
        time_difference = (low_time - high_time).days
        normalized_difference = time_difference / (2*365)
        expanded_array = np.full((1, x_size, y_size), normalized_difference)
        image_array = np.concatenate((image_array, expanded_array), axis=0)
        # print(f"high_time:{high_time}, low_time:{low_time}, time_difference{time_difference}, normalized_difference:{normalized_difference}")
        # x = input()   
        return image_array

    def _load_S2_image(self, image_path, high_path):
        # print(image_path)
        if "pre_disaster" in image_path:
            high_time_str = "20170617"
            s2_image_paths = glob(os.path.join(image_path +  "_S2**.tif"))
        else:
            s2_image_paths = glob(os.path.join(image_path +  "_S2**.tif"))
            # high_time_str = re.search(r'_(\d{8})\.(png|tif)$', os.path.basename(high_path)).group(1)
        s2_image_paths.sort()
        # print(image_path, s2_image_paths, len(s2_image_paths))
        # x = input()
        s2_image_paths = s2_image_paths[-16:]
        if "S2_post" in image_path:
            s2_image_paths = s2_image_paths[1:]
            # print(image_path, s2_image_paths)
        s2_time_series_images = []
        for s2_image_path in s2_image_paths:
            dataset = gdal.Open(s2_image_path)
            image_array = dataset.ReadAsArray()
            x_size = dataset.RasterXSize
            y_size = dataset.RasterYSize

            # # 增加经纬度波段
            # image_array = self._add_lon_lat_band(dataset, x_size, y_size)

            # # 增加时间波段
            # low_time_str = re.search(r'_(\d{8})\.tif$', os.path.basename(s2_image_path)).group(1)
            # image_array = self._add_time_band(image_array, x_size, y_size, high_time_str, low_time_str)

            # 增加角度波段
            # print(s2_image_path.replace("_second", "_angle"))
            # angle_band = gdal.Open(s2_image_path.replace("_second", "_angle"))
            # angle_band = angle_band.ReadAsArray()
            # print(angle_band.shape)
            # image_array[-4:] = angle_band

            s2_time_series_images.append(image_array)
        s2_time_series_images = np.array(s2_time_series_images).reshape((len(s2_image_paths), -1, x_size, y_size))
        s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)

        # 数据归一化
        s2_time_series_images = torch.from_numpy(np.array(s2_time_series_images, dtype=np.float32))
        s2_time_series_images = torch.where(torch.isnan(s2_time_series_images), torch.tensor(0.), s2_time_series_images)
        s2_time_series_images = (s2_time_series_images - self.s2_mean[None, :, None, None]) / self.s2_std[None, :, None, None]
        return s2_time_series_images


    def _load_label(self, label_path):
        # print(label_path)
        label = gdal.Open(label_path)
        label = label.ReadAsArray()
        label = torch.from_numpy(np.array(label, dtype=np.float32))
        label = torch.where(torch.isnan(label), torch.tensor(0.), label)
        return label
    
    def _data_augmentation(self, pre_high_image, post_high_image, pre_s2_images, post_s2_images, pre_label, post_label):
        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[2])
            post_high_image = post_high_image.flip(dims=[2])
            pre_s2_images = pre_s2_images.flip(dims=[3])
            post_s2_images = post_s2_images.flip(dims=[3])
            pre_label = pre_label.flip(dims=[1])
            post_label = post_label.flip(dims=[1])

        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[1])
            post_high_image = post_high_image.flip(dims=[1])
            pre_s2_images = pre_s2_images.flip(dims=[2])
            post_s2_images = post_s2_images.flip(dims=[2])
            pre_label = pre_label.flip(dims=[0])
            post_label = post_label.flip(dims=[0])

        return pre_high_image, post_high_image, pre_s2_images, post_s2_images, pre_label, post_label
    def _resample(self, S2_time_series_data):
        S2_time_series_data = F.interpolate(S2_time_series_data, size=(48, 48), mode='bilinear', align_corners=True)
        return S2_time_series_data

    def __getitem__(self, item):
        pre_high_image, pre_high_time = self._load_high_image(self.pre_high_image_paths[item])
        post_high_image, post_high_time = self._load_high_image(self.post_high_image_paths[item])
        pre_s2_images = self._load_S2_image(self.pre_s2_image_paths[item], pre_high_time)
        post_s2_images = self._load_S2_image(self.post_s2_image_paths[item], post_high_time)
        # pre_s2_images = self._resample(pre_s2_images)
        # post_s2_images = self._resample(post_s2_images)
        pre_label = self._load_label(self.pre_label_paths[item])
        post_label = self._load_label(self.post_label_paths[item])

        pre_label = torch.where(pre_label>=0.5, 1, 0)
        post_label = torch.where(post_label>4, 1, post_label)
        post_label = torch.where(post_label>=3, 1, 0)

        if self.mode == "train":
            pre_high_image, post_high_image, pre_s2_images, post_s2_images, pre_label, post_label = self._data_augmentation(pre_high_image, post_high_image, pre_s2_images, post_s2_images, pre_label, post_label)
        return pre_high_image, post_high_image, pre_s2_images, post_s2_images, pre_label, post_label

def dataset_collate(batch):
    pre_high_image = torch.stack([item[0] for item in batch])
    post_high_image = torch.stack([item[1] for item in batch])
    pre_s2_images = torch.stack([item[2] for item in batch])
    post_s2_images = torch.stack([item[3] for item in batch])
    pre_label = torch.stack([item[4] for item in batch])
    post_label = torch.stack([item[5] for item in batch])
    return pre_high_image, post_high_image, pre_s2_images, post_s2_images, pre_label, post_label

if __name__ == "__main__":
    train_dataset = UKR_xBD_S2('train')
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=0, drop_last=False, collate_fn=dataset_collate)
    print(len(train_loader.dataset))
    for batch_id, inputs in enumerate(train_loader):
        print(batch_id, inputs[0].shape, inputs[1].shape, inputs[2].shape, inputs[3].shape, inputs[4].shape, inputs[5].shape)
        # print(torch.max(inputs[0]), torch.min(inputs[0]), torch.max(inputs[1]), torch.min(inputs[1]))
        print(torch.max(inputs[2]), torch.min(inputs[2]), torch.max(inputs[3]), torch.min(inputs[3]))
        print(torch.max(inputs[4]), torch.min(inputs[4]), torch.max(inputs[5]), torch.min(inputs[5]))
        x = input()

