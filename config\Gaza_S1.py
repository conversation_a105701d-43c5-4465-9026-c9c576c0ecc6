# 在5的基础上，引入因果干预的对比学习

import torch
from torch import nn
import os
from glob import glob
import albumentations as albu
import argparse
from pathlib import Path
import numpy as np
from net.HRNet3_plusv4 import HRNet_S2_DeConv
from loss.loss import <PERSON>emCELoss, MaskLoss, BCEFocalLoss, one_hot_CrossEntropy, myOhemCELoss, BCE_OhemCELoss, WBCE_OhemCELoss
from dataset.Gaza_S2 import Gaza_S1_S2, s1_s2_dataset_collate

# =============================训练参数============================ #
batch_size = 8
lr = 1e-4
size = 384
mosaic_ratio = 0.5
epochs = 100
output_building = 2
output_damage = 2
frame = 1
data_name = "Gaza"
model_name = "HRNet3_plusv4"
save_path = os.path.join("./result", data_name, model_name, f"sentinel_1_{frame}frame_osm")
os.makedirs(save_path, exist_ok=True)

model = HRNet_S2_DeConv(pretrained=False, s2_inchannel=8, frame=frame)
pre_train_model_path = f"./result/Gaza/HRNet3_plusv4/sentinel_1_{frame}frame_osm/24.pt" #

criterion1 = OhemCELoss(thresh=0.7, n_min=batch_size*size*size//16, ignore_index=255)
criterion2 = nn.MSELoss()
criterion3 = nn.KLDivLoss(reduction="mean")

# 数据集加载 + 多GPU训练
device = 'cuda'
train_dataset = Gaza_S1_S2('train')
val_dataset   = Gaza_S1_S2('test')
collate_fn = s1_s2_dataset_collate
# =============================训练参数============================ #


# =============================测试参数============================ #
data_name = "Gaza"
save_name = f"sentinel_1_{frame}frame_osm"
test_base_path = os.path.join("./dataset/Gaza", "test") # xBDS2 UKRS2
test_base_save_path = os.path.join("./result", data_name)
model_path = os.path.join("./result/", data_name, model_name, save_name, "4.pt") # 14
time_series_path = os.path.join(test_base_path, "images")
s2_pre_path = os.path.join(test_base_path, "S2")
s2_post_path = os.path.join(test_base_path, "S2_post")

pre_high_image_save_path  = os.path.join(test_base_save_path, "pre_high_image")
post_high_image_save_path  = os.path.join(test_base_save_path, "post_high_image")
pre_s2_image_save_path = os.path.join(test_base_save_path, "pre_s2_image")
post_s2_image_post_save_path = os.path.join(test_base_save_path, "post_s2_image")
pre_label_save_path = os.path.join(test_base_save_path, "pre_label")
post_label_save_path = os.path.join(test_base_save_path, "post_label")
pre_s1_image_save_path = os.path.join(test_base_save_path, "pre_s1_image")
post_s1_image_post_save_path = os.path.join(test_base_save_path, "post_s1_image")

pre_high_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "pre_high_label")
post_high_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "post_high_label")
pre_s2_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "pre_s2_label")
post_s2_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "post_s2_label")
high_gray_save_path = os.path.join(test_base_save_path, model_name, save_name, "high_gray")
s2_gray_save_path = os.path.join(test_base_save_path, model_name, save_name, "s2_gray")
plot_save_path = os.path.join(test_base_save_path, model_name, save_name, "plot")
pre_s1_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "pre_s1_label")
post_s1_label_save_path = os.path.join(test_base_save_path, model_name, save_name, "post_s1_label")

os.makedirs(pre_high_image_save_path, exist_ok=True)
os.makedirs(post_high_image_save_path, exist_ok=True)

os.makedirs(pre_s2_image_save_path, exist_ok=True)
os.makedirs(post_s2_image_post_save_path, exist_ok=True)

os.makedirs(pre_s1_image_save_path, exist_ok=True)
os.makedirs(post_label_save_path, exist_ok=True)

os.makedirs(pre_label_save_path, exist_ok=True)
os.makedirs(post_s1_image_post_save_path, exist_ok=True)


os.makedirs(pre_high_label_save_path, exist_ok=True)
os.makedirs(post_high_label_save_path, exist_ok=True)

os.makedirs(pre_s2_label_save_path, exist_ok=True)
os.makedirs(post_s2_label_save_path, exist_ok=True)

os.makedirs(high_gray_save_path, exist_ok=True)
os.makedirs(s2_gray_save_path, exist_ok=True)

os.makedirs(pre_s1_label_save_path, exist_ok=True)
os.makedirs(post_s1_label_save_path, exist_ok=True)

os.makedirs(plot_save_path, exist_ok=True)

def get_pre_image_name(image_name):
    pre_images = glob(os.path.join(time_series_path, image_name + ".tif"))
    pre_images.sort()
    # print(pre_images)
    return pre_images[-1]

# print(names)
pre_high_image_paths = []
post_high_image_paths = []
pre_s2_image_paths = []
post_s2_image_paths = []
pre_label_paths = []
post_label_paths = sorted(glob(os.path.join(test_base_path, "labels", "**post**.tif")))
pre_s1_image_paths = []
post_s1_image_paths = []

for post_label_path in post_label_paths:
    image_name = post_label_path.split(".tif")[0]
    pre_high_image_paths.append(image_name.replace("post", "pre").replace("labels", "images") + ".tif")
    post_high_image_paths.append(image_name.replace("post", "pre").replace("labels", "images")+ ".tif")
    pre_s2_image_paths.append(image_name.replace("post", "pre").replace("labels", "S2"))
    post_s2_image_paths.append(image_name.replace("post", "post").replace("labels", "S2_post"))
    pre_label_paths.append(post_label_path)
    pre_s1_image_paths.append(image_name.replace("post", "pre").replace("labels", "S1_buffer_resample_clip/S1"))



s2_mean_std = np.loadtxt("./dataset/Gaza/gaza_S2_mean_std.txt")
s2_mean, s2_std = s2_mean_std[0,:], s2_mean_std[1,:]

s1_mean_std = np.loadtxt("./dataset/Gaza/gaza_S1_mean_std.txt")
s1_mean, s1_std = s1_mean_std[0,:], s1_mean_std[1,:]

label_keys = [
    [  0,   0,   0], # 0 非建筑物区域
    [255, 255, 255], # 1 未损毁
    [255, 255,   0], # 2 possible
    [  0,   0, 255], # 3 Moderate
    [  0, 255,   0], # 4 Severe
    [255,   0,   0], # 5 Destroyed
]

# =============================测试参数============================ #
