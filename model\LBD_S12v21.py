import os
from thop import profile
import torch
import torch.nn as nn
import timm
import numpy as np
import torch.nn.functional as F
from timm.models.layers import DropPath, to_2tuple, trunc_normal_
from timm.models.registry import register_model
from timm.models.vision_transformer import _cfg
from timm.models.registry import register_model
import math
import warnings
warnings.filterwarnings('ignore')

from einops import rearrange
from dcn_v2 import DCN
from model.UANet import *
from model.PVT import *
from model.PVT import Mlp

class Uncertainty_Aware_Fusion_Module2(nn.Module):
    def __init__(self,high_channel,low_channel,out_channel,num_classes):
        super(Uncertainty_Aware_Fusion_Module2, self).__init__()
        self.rank = Uncertainty_Rank_Algorithm()
        self.high_channel = high_channel
        self.low_channel = low_channel
        self.out_channel = out_channel
        self.conv_high = BasicConv2d(2*self.high_channel,self.out_channel,3,1,1)
        self.conv_low = BasicConv2d(2*self.low_channel,self.out_channel,3,1,1)
        self.conv_fusion = nn.Conv2d(2*self.out_channel,self.out_channel,3,1,1)

        self.seg_out = nn.Conv2d(self.out_channel,num_classes,1)


    def forward(self, feature_low, feature_high, map):

        seg_fusion = torch.cat((F.interpolate(feature_high, feature_low.size()[2:], mode='bilinear', align_corners=True), feature_low), dim=1)
        seg_fusion = self.conv_fusion(seg_fusion)

        return seg_fusion

class UANet_Sentinel(nn.Module):
    def __init__(self, backbone="pvt_v5_b2", in_channels=17, de_channel=64, num_classes=2):
        super().__init__()
        # 1 construct the pvt backbone for sentinel data
        self.backbone = eval(backbone)()
        path = "/mnt/d2/zxq/BDS12/model/PTH/pvt_v2_b1.pth"
        path = "/mnt/d2/zxq/BDS12/model/PTH/pvt_v2_b5.pth"
        save_model = torch.load(path)
        model_dict = self.backbone.state_dict()
        new_state_dict = {k:v if v.size()==model_dict[k].size()  else  model_dict[k] for k,v in zip(model_dict.keys(), save_model.values())}
        self.backbone.load_state_dict(new_state_dict, strict=False)

        # 2 modify the stride of steam layer of PVT
        self.backbone.patch_embed1.proj = nn.Conv2d(in_channels, 64, kernel_size=7, stride=1, padding=3)
        # print(self.backbone.patch_embed1)

        # 3 construct the decoder for sentinel fetaure
        self.conv_2 = nn.Sequential(MBDC(64,de_channel))
        self.conv_3 = nn.Sequential(MBDC(128,de_channel))
        self.conv_4 = nn.Sequential(MBDC(320,de_channel))
        self.conv_5 = nn.Sequential(MBDC(512,de_channel))

        self.cgm = CGM()
        self.psm = PSM()

        self.ufm_layer4 = Uncertainty_Aware_Fusion_Module2(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)
        self.ufm_layer3 = Uncertainty_Aware_Fusion_Module2(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)
        self.ufm_layer2 = Uncertainty_Aware_Fusion_Module2(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)
        self.ufm_layer1 = Uncertainty_Aware_Fusion_Module2(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)

    def _repeat_reshape(self, predict, frame):
        predict = predict.unsqueeze(1).repeat(1, frame, 1, 1, 1)
        predict = rearrange(predict, "b t c h w -> (b t) c h w")
        return predict
    
    def forward(self, images, predict_lists):
        B, frame, C, h, w = images.shape
        images = images.view(B*frame, C, h, w)
        # images = F.interpolate(images, (384, 384), mode='bilinear', align_corners=True)
        layer2, layer3, layer4, layer5 = self.backbone(images)
        layer5 = self.conv_5(layer5)
        layer4 = self.conv_4(layer4)
        layer3 = self.conv_3(layer3)
        layer2 = self.conv_2(layer2)

        [predict_3, predict_4, predict_5]=predict_lists
        predict_5 = F.interpolate(predict_5, layer5.size()[2:], mode='bilinear', align_corners=True)
        predict_5 = self._repeat_reshape(predict_5, frame)
        # print(predict_5.shape, layer5.shape)
        # layer5 = self.psm(layer5,predict_5)
        # layer5 = self.cgm(layer5,predict_5)

        fusion = self.ufm_layer4(layer4,layer5,predict_5)

        predict_4 = F.interpolate(predict_4, fusion.size()[2:], mode='bilinear', align_corners=True)
        predict_4 = self._repeat_reshape(predict_4, frame)
        fusion = self.ufm_layer3(layer3,fusion,predict_4)

        predict_3 = F.interpolate(predict_3, fusion.size()[2:], mode='bilinear', align_corners=True)
        predict_3 = self._repeat_reshape(predict_3, frame)
        fusion = self.ufm_layer2(layer2,fusion,predict_3)

        return fusion
 
class residual_up_decoder(nn.Module):
    def __init__(self, decoder_channel):
        super(residual_up_decoder, self).__init__()
        self.conv_path = nn.Sequential(
            nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channel),
            nn.ReLU(),
            nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channel),
            nn.ReLU(),
        )

        self.res = nn.Conv2d(decoder_channel, decoder_channel, kernel_size=1)
        self.deconv = nn.Sequential(
            nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channel, momentum= 0.1),
            nn.ReLU(inplace=True),
            )

    def forward(self, x):
        x = self.conv_path(x) + self.res(x)
        x = self.deconv(x)
        return x

class DCN_guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.dcn = DCN(decoder_channel*2, decoder_channel, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
        self.bn = nn.BatchNorm2d(decoder_channel)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, current_frames, referent_frames):
        B, frame, C, H, W = referent_frames.shape
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

        assert current_frames.shape == referent_frames.shape

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")
        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)

        fusion_frames = self.dcn(fusion_frames)
        fusion_frames = self.bn(fusion_frames)
        fusion_frames = self.relu(fusion_frames)

        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames
    
class cross_fusion(nn.Module):
    def __init__(self, in_channels, decoder_channel, frame):
        super(cross_fusion, self).__init__()

        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=in_channels, out_channels=int(in_channels*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.BatchNorm2d(in_channels*frame),
            nn.ReLU(inplace=True),
        )

        self.up = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel*frame, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.UpsamplingBilinear2d(scale_factor=2)
        )
 
    def forward(self, time_serise_feature):
        B, frame, C, H, W = time_serise_feature.shape

        time_serise_feature = time_serise_feature.view(B, frame, C, H*W).permute(0, 2, 3, 1)
        time_serise_feature = self.conv1(time_serise_feature)
        time_serise_feature = time_serise_feature.view(B, frame*C, H*W, 1).squeeze(-1).view(B, frame*C, H, W)
        fusion_feature = self.up(time_serise_feature)

        return fusion_feature


class Cross_Attention(nn.Module):
    def __init__(self, dim, num_heads=8, qkv_bias=False, qk_scale=None, attn_drop=0., proj_drop=0., sr_ratio=1):
        super().__init__()
        assert dim % num_heads == 0, f"dim {dim} should be divided by num_heads {num_heads}."

        self.dim = dim
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = qk_scale or head_dim ** -0.5

        self.q = nn.Linear(dim, dim, bias=qkv_bias)
        self.kv = nn.Linear(dim, dim * 2, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

        self.sr_ratio = sr_ratio
        if sr_ratio > 1:
            self.sr = nn.Conv2d(dim, dim, kernel_size=sr_ratio, stride=sr_ratio)
            self.norm = nn.LayerNorm(dim)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x_low, x_high, H, W):
        B, N, C = x_low.shape
        q = self.q(x_low).reshape(B, N, self.num_heads, C // self.num_heads).permute(0, 2, 1, 3)

        if self.sr_ratio > 1:
            x_ = x_high.permute(0, 2, 1).reshape(B, C, H, W)
            x_ = self.sr(x_).reshape(B, C, -1).permute(0, 2, 1)
            x_ = self.norm(x_)
            kv = self.kv(x_).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        else:
            kv = self.kv(x_high).reshape(B, -1, 2, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        k, v = kv[0], kv[1]

        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)

        return x


class Cross_Block(nn.Module):
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, qk_scale=None, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm, sr_ratio=1):
        super().__init__()
        self.norm1 = norm_layer(dim)
        self.norm2 = norm_layer(dim)
        self.attn = Cross_Attention(
            dim,
            num_heads=num_heads, qkv_bias=qkv_bias, qk_scale=qk_scale,
            attn_drop=attn_drop, proj_drop=drop, sr_ratio=sr_ratio)
        # NOTE: drop path for stochastic depth, we shall see if this is better than dropout here
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm3 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x_low, x_high, H, W):
        x = x_low + self.drop_path(self.attn(self.norm1(x_low), self.norm2(x_high), H, W))
        x = x + self.drop_path(self.mlp(self.norm3(x), H, W))
        return x

class cross_scale(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        # F_h^1=PVT(F_h,F_l,F_l), F_l^1=PVT(F_l,F_h,F_h)
        # F_o=UANet(F_h^1,F_l^1)
        self.low_cross_block = Cross_Block(dim=decoder_channel, num_heads=1, mlp_ratio=8, qkv_bias=True, qk_scale=None, attn_drop=0., sr_ratio=8)
        self.high_cross_block = Cross_Block(dim=decoder_channel, num_heads=1, mlp_ratio=8,qkv_bias=True, qk_scale=None, attn_drop=0., sr_ratio=8)

    def forward(self, low_feature, high_feature):
        B, C, H, W = low_feature.shape
        low_feature = rearrange(low_feature, "b c h w -> b (h w) c")
        high_feature = rearrange(high_feature, "b c h w -> b (h w) c")
        low_cross_scale = self.low_cross_block(low_feature, high_feature, H, W).view(B, H, W, C).permute(0, 3, 1, 2)
        high_cross_scale = self.high_cross_block(high_feature, low_feature, H, W).view(B, H, W, C).permute(0, 3, 1, 2)
        return low_cross_scale, high_cross_scale


class time_serise_damage_feature(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.mlp = nn.Sequential(
            nn.Linear(decoder_channel, decoder_channel),
            nn.ReLU(),
            nn.Linear(decoder_channel, decoder_channel)
        )

        self.layernorm = nn.LayerNorm(decoder_channel)
        self.scale = decoder_channel ** -0.5

    def forward(self, time_series_feature):
        B, T, C, H, W = time_series_feature.shape

        # 获取最后一帧的特征（Q16）
        Q_seq = time_series_feature[:, :-1]  # 灾前特征序列
        Q16 = Q_seq[:, -1]  # 取最后一帧 Q16
        Q17 = time_series_feature[:, -1]  # 灾后特征，最后一帧

        T = T - 1
        Qi = Q_seq.reshape(B * T, C, -1)  # [B*T, C, H*W]
        Q16_expand = Q16.unsqueeze(1).repeat(1, T, 1, 1, 1).reshape(B * T, C, -1)
        Q17_expand = Q17.unsqueeze(1).repeat(1, T, 1, 1, 1).reshape(B * T, C, -1)

        diff_Q = Qi - Q17_expand
        
        attn = (Q16_expand.transpose(-2, -1) @ diff_Q) * self.scale
        attn = attn.softmax(dim=-1)

        x = (attn @ diff_Q.transpose(-2, -1))
        x = self.mlp(x)

        F_new = (x + diff_Q.transpose(-2, -1)).transpose(-2, -1).reshape(B, T, C, H, W)
        # F_new = x.transpose(-2, -1).reshape(B, T, C, H, W)

        return F_new
    

class Uncertainty_Aware_Fusion_Module3(nn.Module):
    def __init__(self,high_channel,low_channel,out_channel,num_classes):
        super(Uncertainty_Aware_Fusion_Module3, self).__init__()
        self.rank = Uncertainty_Rank_Algorithm()
        self.high_channel = high_channel
        self.low_channel = low_channel
        self.out_channel = out_channel
        self.conv_high = BasicConv2d(2*self.high_channel,self.out_channel,3,1,1)
        self.conv_low = BasicConv2d(2*self.low_channel,self.out_channel,3,1,1)
        self.conv_fusion = nn.Conv2d(2*self.out_channel,self.out_channel,3,1,1)

        self.seg_out = nn.Conv2d(self.out_channel,num_classes,1)


    def forward(self, feature_low, feature_high, map):
        map = map[:,1,:,:].unsqueeze(1)
        uncertainty_fore_map_high, uncertainty_back_map_high = self.rank(map)
        uncertainty_feature_high = torch.cat((uncertainty_fore_map_high * feature_high, uncertainty_back_map_high * feature_high),dim=1)
        uncertainty_high_up = F.interpolate(self.conv_high(uncertainty_feature_high), feature_low.size()[2:], mode='bilinear', align_corners=True)

        low_map = F.interpolate(map, feature_low.size()[2:], mode='bilinear', align_corners=True)
        uncertainty_fore_map_low, uncertainty_back_map_low = self.rank(low_map)
        uncertainty_feature_low = torch.cat((uncertainty_fore_map_low * feature_low, uncertainty_back_map_low * feature_low),dim=1)
        uncertainty_low = self.conv_low(uncertainty_feature_low)

        seg_fusion = torch.cat((uncertainty_high_up, uncertainty_low), dim=1)

        seg_fusion = self.conv_fusion(seg_fusion)
        seg_out = self.seg_out(seg_fusion)

        return seg_fusion, seg_out



class LBDv21_S2(nn.Module):
    def __init__(self, backbone="pvt_v2_b2_s2", s2_inchannel=17, frame=16, num_classes=2, damage_classes=2, post_input="S2"):
        super().__init__()
        # 1 High RGB Net
        decoder_channel = 64
        self.high_net = UANet_pvt(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/36.pt"
        self.high_net = self._load_weight(self.high_net, pre_train_model_path)
        # for param in self.high_net.parameters():
        #     param.requires_grad = False
        # 2 low-sentinel-2 Net
        self.low2_net = UANet_Sentinel(backbone=backbone, in_channels=s2_inchannel, de_channel=decoder_channel, num_classes=num_classes)
        self.low2_net = self._load_weight(self.low2_net, pre_train_model_path)

        # 3 time-serise data transfer to unity the color space
        self.fcn = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])

        # 4 pre-sentinel-2 to pre-buildings
        self.dcn_guide = DCN_guide(decoder_channel)
        self.cross_fusion = cross_fusion(in_channels=decoder_channel, decoder_channel=decoder_channel, frame=frame)
        self.pre_cross_scale = cross_scale(decoder_channel=decoder_channel)
        self.pre_ufm_layer = Uncertainty_Aware_Fusion_Module3(decoder_channel, decoder_channel, decoder_channel, num_classes=num_classes)
        self.building_layer = nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)

        # 5 pre-post to buildings damage mapping
        self.cosine_different = time_serise_damage_feature(decoder_channel)
        self.damage_dcn_guide = DCN_guide(decoder_channel)
        self.dcross_fusion = cross_fusion(in_channels=decoder_channel, decoder_channel=decoder_channel, frame=frame)
        self.dres = residual_up_decoder(decoder_channel)
        self.damage_layer = nn.Conv2d(in_channels=decoder_channel, out_channels=damage_classes, kernel_size=1, stride=1, padding=0)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
 

    def _feature_transfer(self, time_serise_feature):
        B, total_frame, C, H, W = time_serise_feature.shape
        aligned_features = []
        T15 = time_serise_feature[:, -2]
        
        for t in range(total_frame):  # 对于T0到T14
            curr_feat = time_serise_feature[:, t]  # [B, C, H, W]
            feat_pair = torch.cat([curr_feat, T15], dim=1)
            A = self.fcn[t](feat_pair)
            aligned_feat = torch.mul(curr_feat, A).squeeze(1)
            aligned_features.append(aligned_feat)
        aligned_features = torch.stack(aligned_features, dim=1) # [B, 17, C, H, W]
        return aligned_features
 
    def _encoder_feature_constraint(self, inputs, device, high_features, low_decoder_features, outputs):
        pre_label = inputs[3].unsqueeze(1).to(device).to(torch.float32)
        post_label = inputs[4].unsqueeze(1).to(device).to(torch.float32)
        B, _, H, W = pre_label.shape
        B, frame, _, h, w = low_decoder_features.shape

        high_features = F.interpolate(high_features, size=(h, w), mode='bilinear', align_corners=True)
        outputs["loss"] = F.mse_loss(F.normalize(low_decoder_features[:, -2], dim=1), F.normalize(high_features.detach(), dim=1))

        T15 = F.interpolate(low_decoder_features[:, -2], size=(H, W), mode='bilinear')
        T16 = F.interpolate(low_decoder_features[:, -1], size=(H, W), mode='bilinear')

        pre_label = torch.where(pre_label == 1, 1, 0)
        post_label = torch.where(post_label == 1, 0, 1)

        # 计算损毁区域
        outputs["loss"] += F.mse_loss(F.normalize(T16 * post_label), F.normalize(T15.detach() * post_label), reduction='mean')

        for frame_id in range(frame - 2):
            frame_feature = F.normalize(low_decoder_features[:, frame_id])
            outputs["loss"] += F.mse_loss(frame_feature, F.normalize(low_decoder_features[:, -2].detach()), reduction='mean')

        return outputs
    

    def forward(self, inputs, device):
        outputs = {}
        B, _, H, W = inputs[0].to(device).to(torch.float32).shape
        # with torch.no_grad():
        outputs = self.high_net(inputs[0].to(device).to(torch.float32), device, mode="pre", outputs=outputs)
        high_features_down4 = F.interpolate(outputs["pre_high_features"], size=(H//4, W//4), mode='bilinear', align_corners=True)
        predict_lists = [outputs["pre_high_building3"], outputs["pre_high_building4"], outputs["pre_high_building5"]]

        s2_images = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
        B, frame, _, h, w = s2_images.shape
        low_encoder_features = self.low2_net(images=s2_images, predict_lists=predict_lists).view(B, frame, -1, h, w)
        # print(low_encoder_features.shape)

        low_encoder_features = self._feature_transfer(low_encoder_features)
        if self.training:
            outputs = self._encoder_feature_constraint(inputs, device, outputs["pre_high_features"], low_encoder_features, outputs)


        low_pre_features = self.dcn_guide(current_frames=low_encoder_features[:, -2], referent_frames=low_encoder_features[:, :-1])
        low_pre_features = self.cross_fusion(low_pre_features)
        outputs["pre_low_features"] = low_pre_features
        outputs["pre_high_features"] = high_features_down4

        low_pre_features2, high_features_down4 = self.pre_cross_scale(low_pre_features, high_features_down4)
        # low_outs = self.building_layer(low_pre_features2)
        # outputs["pre_low_building2"] = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)
        pre_building_map = F.interpolate(outputs["pre_high_building"], size=(H//4, W//4), mode='bilinear', align_corners=True)
        

        low_pre_features2, low_outs = self.pre_ufm_layer(low_pre_features2, high_features_down4, pre_building_map)
        outputs["pre_low_building"] = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)
        pre_building_map = F.interpolate(torch.sigmoid(outputs["pre_high_building"]), size=(H//4, W//4), mode='bilinear', align_corners=True)[:, 1:]

        # low_post_features = low_encoder_features[:, :-1] - low_encoder_features[:, -1:]
        low_post_features = self.cosine_different(low_encoder_features)
        low_post_features = self.damage_dcn_guide(current_frames=low_post_features[:, -1], referent_frames=low_post_features)
        low_post_features = self.dcross_fusion(low_post_features)
        outputs["post_low_features"] = low_post_features
        outputs["post_high_features"] = low_pre_features

        low_post_features2 = self.dres(low_post_features*pre_building_map)
        low_outs = self.damage_layer(low_post_features2)
        outputs["post_low_damage"] = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)

        return outputs


class LBDv21_S1(nn.Module):
    def __init__(self, backbone="pvt_v2_b2_s2", s1_inchannel=16, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2, post_input="S2"):
        super().__init__()
        # 1 High RGB Net
        decoder_channel = 64
        self.high_net = UANet_pvt(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/36.pt"
        self.high_net = self._load_weight(self.high_net, pre_train_model_path)

        # 2 low-sentinel-2 Net
        self.low2_net = UANet_Sentinel(backbone=backbone, in_channels=s2_inchannel, de_channel=decoder_channel, num_classes=num_classes)
        self.low2_net = self._load_weight(self.low2_net, pre_train_model_path)

        self.low1_net = UANet_Sentinel(backbone=backbone, in_channels=s1_inchannel, de_channel=decoder_channel, num_classes=num_classes)
        self.low1_net = self._load_weight(self.low1_net, pre_train_model_path)

        # 3 time-serise data transfer to unity the color space
        self.fcn = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])

        # 4 pre-sentinel-2 to pre-buildings
        self.dcn_guide = DCN_guide(decoder_channel)
        self.cross_fusion = cross_fusion(in_channels=decoder_channel, decoder_channel=decoder_channel, frame=frame)
        self.pre_cross_scale = cross_scale(decoder_channel=decoder_channel)
        self.pre_ufm_layer = Uncertainty_Aware_Fusion_Module3(decoder_channel, decoder_channel, decoder_channel, num_classes=num_classes)
        self.building_layer = nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)

        # 5 pre-post to buildings damage mapping
        self.cosine_different = time_serise_damage_feature(decoder_channel)
        self.damage_dcn_guide = DCN_guide(decoder_channel)
        self.dcross_fusion = cross_fusion(in_channels=decoder_channel, decoder_channel=decoder_channel, frame=frame)
        self.dres = residual_up_decoder(decoder_channel)
        self.damage_layer = nn.Conv2d(in_channels=decoder_channel, out_channels=damage_classes, kernel_size=1, stride=1, padding=0)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
 

    def _feature_transfer(self, time_serise_feature):
        B, total_frame, C, H, W = time_serise_feature.shape
        aligned_features = []
        T15 = time_serise_feature[:, -2]
        
        for t in range(total_frame):  # 对于T0到T14
            curr_feat = time_serise_feature[:, t]  # [B, C, H, W]
            feat_pair = torch.cat([curr_feat, T15], dim=1)
            A = self.fcn[t](feat_pair)
            aligned_feat = torch.mul(curr_feat, A).squeeze(1)
            aligned_features.append(aligned_feat)
        aligned_features = torch.stack(aligned_features, dim=1) # [B, 17, C, H, W]
        return aligned_features
 
    def _encoder_feature_constraint(self, inputs, device, high_features, low_decoder_features, outputs):
        pre_label = inputs[3].unsqueeze(1).to(device).to(torch.float32)
        post_label = inputs[4].unsqueeze(1).to(device).to(torch.float32)
        B, _, H, W = pre_label.shape
        B, frame, _, h, w = low_decoder_features.shape

        high_features = F.interpolate(high_features, size=(h, w), mode='bilinear', align_corners=True)
        outputs["loss"] = F.mse_loss(F.normalize(low_decoder_features[:, -2], dim=1), F.normalize(high_features.detach(), dim=1))

        T15 = F.interpolate(low_decoder_features[:, -2], size=(H, W), mode='bilinear')
        T16 = F.interpolate(low_decoder_features[:, -1], size=(H, W), mode='bilinear')

        pre_label = torch.where(pre_label == 1, 1, 0)
        post_label = torch.where(post_label == 1, 0, 1)

        # 计算损毁区域
        outputs["loss"] += F.mse_loss(F.normalize(T16 * post_label), F.normalize(T15.detach() * post_label), reduction='mean')

        for frame_id in range(frame - 2):
            frame_feature = F.normalize(low_decoder_features[:, frame_id])
            outputs["loss"] += F.mse_loss(frame_feature, F.normalize(low_decoder_features[:, -2].detach()), reduction='mean')

        return outputs
    

    def forward(self, inputs, device):
        outputs = {}
        B, _, H, W = inputs[0].to(device).to(torch.float32).shape
        # with torch.no_grad():
        outputs = self.high_net(inputs[0].to(device).to(torch.float32), device, mode="pre", outputs=outputs)
        high_features_down4 = F.interpolate(outputs["pre_high_features"], size=(H//4, W//4), mode='bilinear', align_corners=True)
        predict_lists = [outputs["pre_high_building3"], outputs["pre_high_building4"], outputs["pre_high_building5"]]

        s2_images = inputs[1].to(device).to(torch.float32)
        B, frame, _, h, w = s2_images.shape
        low_encoder_features_s2 = self.low2_net(images=s2_images, predict_lists=predict_lists).view(B, frame, -1, h, w)

        s1_images = inputs[2].to(device).to(torch.float32)
        B, frame, _, h, w = s1_images.shape
        low_encoder_features_s1 = self.low1_net(images=s1_images, predict_lists=predict_lists).view(B, frame, -1, h, w)

        low_encoder_features = torch.cat((low_encoder_features_s2, low_encoder_features_s1), dim=1)
        B, frame, _, h, w = low_encoder_features.shape

        # print(low_encoder_features.shape)

        low_encoder_features = self._feature_transfer(low_encoder_features)
        if self.training:
            outputs = self._encoder_feature_constraint(inputs, device, outputs["pre_high_features"], low_encoder_features, outputs)


        low_pre_features = self.dcn_guide(current_frames=low_encoder_features[:, -2], referent_frames=low_encoder_features[:, :-1])
        low_pre_features = self.cross_fusion(low_pre_features)
        outputs["pre_low_features"] = low_pre_features
        outputs["pre_high_features"] = high_features_down4

        low_pre_features2, high_features_down4 = self.pre_cross_scale(low_pre_features, high_features_down4)
        pre_building_map = F.interpolate(outputs["pre_high_building"], size=(H//4, W//4), mode='bilinear', align_corners=True)

        low_pre_features2, low_outs = self.pre_ufm_layer(low_pre_features2, high_features_down4, pre_building_map)
        outputs["pre_low_building"] = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)
        pre_building_map = F.interpolate(torch.sigmoid(outputs["pre_high_building"]), size=(H//4, W//4), mode='bilinear', align_corners=True)[:, 1:]

        low_post_features = self.cosine_different(low_encoder_features)
        low_post_features = self.damage_dcn_guide(current_frames=low_post_features[:, -1], referent_frames=low_post_features)
        low_post_features = self.dcross_fusion(low_post_features)
        outputs["post_low_features"] = low_post_features
        outputs["post_high_features"] = low_pre_features

        low_post_features2 = self.dres(low_post_features*pre_building_map)
        low_outs = self.damage_layer(low_post_features2)
        outputs["post_low_damage"] = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)

        return outputs

if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '3'
    # deep_model = UANet_Sentinel().cuda()

    deep_model = LBDv21_S1(backbone="pvt_v2_b2_s2", s1_inchannel=16, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2, post_input="S2").cuda()
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384).cuda()
    low_images1 = torch.rand(bs, 16, 17, 48, 48).cuda()
    low_images2 = torch.rand(bs, 1, 16, 48, 48).cuda()
    pre_label = torch.rand(bs, 384, 384).cuda()
    post_label = torch.rand(bs, 384, 384).cuda()
    inputs = [hight_image, low_images1, low_images2, pre_label, post_label]
    device = torch.device("cuda")

    outputss = deep_model(inputs, device)

    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
