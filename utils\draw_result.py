# -*- coding: utf-8 -*-
# @Author: zx<PERSON><PERSON><PERSON>
# @Date:   2022-03-28 19:18:24
# @Last Modified by:   Your name
# @Last Modified time: 2024-06-06 19:29:26
from operator import index
import os
import numpy as np
from PIL import Image
from tqdm import tqdm
import cv2
from matplotlib import pyplot as plt
import multiprocessing
from glob import glob

TP = [  0, 255,   0] # 绿色
FP = [255,   0,   0] # 红色
FN = [0,     0, 255] # 蓝色
TN = [255, 255, 255] # 白色

def draw(test_name, model_name, name):
    name = name.replace("_post.png", "_pre.png")
    save_draw1_path = "./result/" + test_name + "/" + model_name + "/draw1/"
    save_draw2_path = "./result/" + test_name + "/" + model_name + "/draw2/"
    os.makedirs(save_draw1_path, exist_ok=True)
    os.makedirs(save_draw2_path, exist_ok=True)
            
    label_pre_path = "./result/" + test_name + "/label_pre/" + name
    result_pre_path = "./result/" + test_name + "/" + model_name + "/label_pre/" + name

    image_pre_path = label_pre_path.replace("/label_pre/", "/image_pre/")
    image_post_path = label_pre_path.replace("/label_pre/", "/image_post/").replace("_pre.png", "_post.png")

    label_post_path = label_pre_path.replace("/label_pre/", "/label_post/").replace("_pre.png", "_post.png")
    result_post_path = result_pre_path.replace("/label_pre/", "/label_post/").replace("_pre.png", "_post.png")
    # label_post_path = label_pre_path.replace("/label_pre/", "/label_post/").replace("_pre.png", "_post.png")
    # result_post_path = result_pre_path.replace("/label_pre/", "/label_process/").replace("_pre.png", "_post.png")
    
    # print(post_result_path)
    image_pre = Image.open(image_pre_path)#.resize((256, 256))
    image_post = Image.open(image_post_path)#.resize((256, 256))

    label_pre   = np.array(Image.open(label_pre_path)).astype(np.float32)
    result_pre  = np.array(Image.open(result_pre_path)).astype(np.float32)
    
    label_post  = np.array(Image.open(label_post_path).convert("L")).astype(np.float32)
    result_post = np.array(Image.open(result_post_path).convert("L")).astype(np.float32)
    h, w        = label_pre.shape

    def calculate(pre_label, pre_result):
        drwa_result = np.zeros((h, w, 3))
        # ---------------------- #
        # 有四种值（情况）：
        # 预测为建筑物、标签为建筑物TP 1 1  青色
        # 预测为建筑物、标签为非建筑物FP 1 0 深红 错误提取
        # 预测为非建筑物、标签为非建筑物TN 0 0 白色 
        # 预测为非建筑物、标签为建筑物FN 0 1 蓝色 遗漏提取
        drwa_result[(pre_label+pre_result)==(255*2),:] = TP
        drwa_result[(pre_label-pre_result)<0,:]        = FP
        drwa_result[(pre_result-pre_label)<0,:]        = FN
        drwa_result[(pre_result+pre_label)==0,:]       = TN

        # 计算F1FENS
        return drwa_result
    drwa_result = calculate(label_pre, result_pre)
    img1 = Image.blend(Image.fromarray(np.uint8(drwa_result)), image_pre, 0.5)
    # img2 = Image.blend(Image.fromarray(np.uint8(drwa_result)), image_post, 0.5)
    img1.save(os.path.join(save_draw1_path, os.path.basename(image_pre_path)))
    # img2.save(os.path.join(save_draw1_path, os.path.basename(image_post_path)))


    drwa_result = calculate(label_post, result_post)
    # # img1 = Image.blend(Image.fromarray(np.uint8(drwa_result)), image_pre, 0.5)
    img2 = Image.blend(Image.fromarray(np.uint8(drwa_result)), image_post, 0.5)
    # # img1.save(os.path.join(save_draw2_path, os.path.basename(image_pre_path)))
    img2.save(os.path.join(save_draw2_path, os.path.basename(image_post_path)))


if __name__ == "__main__":
    pool = multiprocessing.Pool(multiprocessing.cpu_count()) # 全部cpu执行
    test_name="Ukraine30"
    model_name="DBNetV4"
    names = glob(os.path.join("./result", test_name, "label_post", "**post.png"))
    # names = glob(os.path.join("./result", test_name, "post_new", "**post.png"))
    # print(names)
    for name in tqdm(names):
        name = os.path.basename(name)
        draw(test_name, model_name, name)
        # pool.apply_async(func=draw, args=(test_name, model_name, name))
    pool.close()
    pool.join()
