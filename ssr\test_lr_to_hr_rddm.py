#!/usr/bin/env python3
"""
测试LR到HR特征恢复RDDM模型
"""

import torch
import torch.nn.functional as F
import numpy as np
from lr_to_hr_rddm import LRtoHRRDDM, create_lr_to_hr_model

def create_feature_data(batch_size=2, channels=2, height=64, width=64):
    """创建模拟的LR和HR特征数据"""
    
    # 创建HR特征：具有丰富的细节和清晰的边界
    hr_features = torch.randn(batch_size, channels, height, width)
    
    # 添加一些结构化的特征模式
    for b in range(batch_size):
        for c in range(channels):
            # 添加一些高频细节
            hr_features[b, c, 10:20, 10:20] += 1.0
            hr_features[b, c, 30:40, 30:40] += -0.5
            hr_features[b, c, 45:55, 15:25] += 0.8
    
    # 创建LR特征：通过下采样和模糊来模拟低分辨率特征
    # 方法1：高斯模糊
    kernel_size = 5
    sigma = 1.5
    kernel = torch.exp(-torch.arange(-(kernel_size//2), kernel_size//2 + 1)**2 / (2*sigma**2))
    kernel = kernel / kernel.sum()
    kernel_2d = kernel.unsqueeze(0) * kernel.unsqueeze(1)
    kernel_2d = kernel_2d.unsqueeze(0).unsqueeze(0).repeat(channels, 1, 1, 1)
    
    lr_features = F.conv2d(hr_features, kernel_2d, padding=kernel_size//2, groups=channels)
    
    # 方法2：添加噪声
    lr_features += torch.randn_like(lr_features) * 0.1
    
    # 方法3：降低动态范围
    lr_features = lr_features * 0.8
    
    return hr_features, lr_features

def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    # 创建模型
    model = create_lr_to_hr_model(
        dim=32,
        dim_mults=(1, 2, 4),
        channels=2,
        timesteps=100,
        sampling_timesteps=20,
        alpha_scale=0.1,
        beta_scale=0.001
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建测试数据
    hr_features, lr_features = create_feature_data(batch_size=2, height=64, width=64)
    
    print(f"HR特征形状: {hr_features.shape}")
    print(f"LR特征形状: {lr_features.shape}")
    print(f"HR特征范围: [{hr_features.min():.3f}, {hr_features.max():.3f}]")
    print(f"LR特征范围: [{lr_features.min():.3f}, {lr_features.max():.3f}]")
    
    # 测试训练前向传播
    model.train()
    loss, pred_hr = model(hr_features, lr_features)
    
    print(f"训练损失: {loss.item():.6f}")
    print(f"预测HR特征形状: {pred_hr.shape}")
    print(f"预测HR特征范围: [{pred_hr.min():.3f}, {pred_hr.max():.3f}]")
    
    # 计算重建误差
    reconstruction_error = F.mse_loss(pred_hr, hr_features)
    print(f"重建误差 (MSE): {reconstruction_error.item():.6f}")
    
    return True

def test_feature_enhancement():
    """测试特征增强效果"""
    print("\n=== 测试特征增强效果 ===")
    
    model = create_lr_to_hr_model(
        dim=32,
        dim_mults=(1, 2),
        channels=2,
        timesteps=50,
        sampling_timesteps=10,
        alpha_scale=0.15,
        beta_scale=0.0005
    )
    
    # 创建测试数据
    hr_features, lr_features = create_feature_data(batch_size=1, height=64, width=64)
    
    model.eval()
    with torch.no_grad():
        # 特征增强
        enhanced_features = model.enhance_features(lr_features, num_steps=10)
        
        print(f"原始LR特征范围: [{lr_features.min():.3f}, {lr_features.max():.3f}]")
        print(f"增强后特征范围: [{enhanced_features.min():.3f}, {enhanced_features.max():.3f}]")
        print(f"目标HR特征范围: [{hr_features.min():.3f}, {hr_features.max():.3f}]")
        
        # 计算改进效果
        original_error = F.mse_loss(lr_features, hr_features)
        enhanced_error = F.mse_loss(enhanced_features, hr_features)
        improvement = original_error - enhanced_error
        
        print(f"原始误差: {original_error.item():.6f}")
        print(f"增强后误差: {enhanced_error.item():.6f}")
        print(f"改进程度: {improvement.item():.6f}")
        
        # 计算特征相似度
        def compute_feature_similarity(f1, f2):
            f1_flat = f1.flatten()
            f2_flat = f2.flatten()
            correlation = torch.corrcoef(torch.stack([f1_flat, f2_flat]))[0, 1]
            return correlation.item()
        
        lr_hr_similarity = compute_feature_similarity(lr_features, hr_features)
        enhanced_hr_similarity = compute_feature_similarity(enhanced_features, hr_features)
        
        print(f"LR-HR相似度: {lr_hr_similarity:.4f}")
        print(f"增强-HR相似度: {enhanced_hr_similarity:.4f}")
        
        if improvement > 0:
            print("✓ 成功改进特征质量")
            success = True
        else:
            print("✗ 未能改进特征质量")
            success = False
    
    return success

def test_different_scales():
    """测试不同尺度的特征"""
    print("\n=== 测试不同尺度特征 ===")
    
    scales = [
        (32, 32, "小尺度"),
        (64, 64, "中尺度"),
        (128, 128, "大尺度")
    ]
    
    for height, width, scale_name in scales:
        print(f"\n--- {scale_name} ({height}x{width}) ---")
        
        try:
            model = create_lr_to_hr_model(
                dim=16,  # 减小模型以适应不同尺度
                dim_mults=(1, 2),
                channels=2,
                timesteps=30,
                sampling_timesteps=5,
                alpha_scale=0.1,
                beta_scale=0.001
            )
            
            # 创建对应尺度的数据
            hr_features, lr_features = create_feature_data(
                batch_size=1, 
                height=height, 
                width=width
            )
            
            # 测试训练
            model.train()
            loss, pred_hr = model(hr_features, lr_features)
            
            print(f"  训练损失: {loss.item():.6f}")
            
            # 测试推理
            model.eval()
            with torch.no_grad():
                enhanced = model.enhance_features(lr_features, num_steps=3)
                
                original_error = F.mse_loss(lr_features, hr_features)
                enhanced_error = F.mse_loss(enhanced, hr_features)
                improvement = original_error - enhanced_error
                
                print(f"  改进程度: {improvement.item():.6f}")
                print(f"  状态: {'✓' if improvement > 0 else '✗'}")
                
        except Exception as e:
            print(f"  ✗ 失败: {str(e)}")

def test_feature_statistics():
    """测试特征统计特性"""
    print("\n=== 测试特征统计特性 ===")
    
    model = create_lr_to_hr_model(
        dim=32,
        dim_mults=(1, 2, 4),
        channels=2,
        timesteps=50,
        sampling_timesteps=10
    )
    
    # 创建测试数据
    hr_features, lr_features = create_feature_data(batch_size=1, height=64, width=64)
    
    model.eval()
    with torch.no_grad():
        enhanced = model.enhance_features(lr_features, num_steps=10)
        
        def compute_statistics(features, name):
            mean = features.mean().item()
            std = features.std().item()
            min_val = features.min().item()
            max_val = features.max().item()
            
            print(f"{name}:")
            print(f"  均值: {mean:.4f}, 标准差: {std:.4f}")
            print(f"  范围: [{min_val:.4f}, {max_val:.4f}]")
            
            return mean, std, min_val, max_val
        
        print("特征统计:")
        hr_stats = compute_statistics(hr_features, "HR特征")
        lr_stats = compute_statistics(lr_features, "LR特征")
        enhanced_stats = compute_statistics(enhanced, "增强特征")
        
        # 检查增强特征是否更接近HR特征的统计特性
        hr_mean, hr_std = hr_stats[0], hr_stats[1]
        lr_mean_diff = abs(lr_stats[0] - hr_mean)
        enhanced_mean_diff = abs(enhanced_stats[0] - hr_mean)
        
        lr_std_diff = abs(lr_stats[1] - hr_std)
        enhanced_std_diff = abs(enhanced_stats[1] - hr_std)
        
        print(f"\n统计特性改进:")
        print(f"均值差异 - LR: {lr_mean_diff:.4f}, 增强: {enhanced_mean_diff:.4f}")
        print(f"标准差差异 - LR: {lr_std_diff:.4f}, 增强: {enhanced_std_diff:.4f}")
        
        mean_improved = enhanced_mean_diff < lr_mean_diff
        std_improved = enhanced_std_diff < lr_std_diff
        
        print(f"均值改进: {'✓' if mean_improved else '✗'}")
        print(f"标准差改进: {'✓' if std_improved else '✗'}")
        
        return mean_improved and std_improved

def test_batch_processing():
    """测试批处理"""
    print("\n=== 测试批处理 ===")
    
    batch_sizes = [1, 2, 4, 8]
    
    model = create_lr_to_hr_model(
        dim=16,
        dim_mults=(1, 2),
        channels=2,
        timesteps=20,
        sampling_timesteps=5
    )
    
    for batch_size in batch_sizes:
        print(f"\n--- 批大小: {batch_size} ---")
        
        try:
            hr_features, lr_features = create_feature_data(
                batch_size=batch_size, 
                height=32, 
                width=32
            )
            
            # 测试训练
            model.train()
            loss, pred_hr = model(hr_features, lr_features)
            
            print(f"  训练损失: {loss.item():.6f}")
            print(f"  输出形状: {pred_hr.shape}")
            
            # 测试推理
            model.eval()
            with torch.no_grad():
                enhanced = model.enhance_features(lr_features, num_steps=3)
                print(f"  增强结果形状: {enhanced.shape}")
                print(f"  ✓ 批处理成功")
                
        except Exception as e:
            print(f"  ✗ 批处理失败: {str(e)}")

if __name__ == "__main__":
    print("开始测试LR到HR特征恢复RDDM模型...")
    
    success1 = test_basic_functionality()
    success2 = test_feature_enhancement()
    test_different_scales()
    success3 = test_feature_statistics()
    test_batch_processing()
    
    print(f"\n=== 测试总结 ===")
    print(f"基本功能: {'✓' if success1 else '✗'}")
    print(f"特征增强: {'✓' if success2 else '✗'}")
    print(f"统计特性: {'✓' if success3 else '✗'}")
    
    if all([success1, success2, success3]):
        print("✓ LR到HR特征恢复RDDM模型测试全部通过！")
        print("\n推荐使用参数:")
        print("- dim=32-64 (根据计算资源)")
        print("- dim_mults=(1, 2, 4)")
        print("- alpha_scale=0.1-0.15")
        print("- beta_scale=0.001")
        print("- sampling_timesteps=10-20")
    else:
        print("✗ 部分测试失败，需要进一步调优")
