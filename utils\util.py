# -*- coding: utf-8 -*-
# @Author: z<PERSON><PERSON><PERSON><PERSON>
# @Date:   2022-02-03 17:41:10
# @Last Modified by:   Your name
# @Last Modified time: 2023-04-25 19:49:58
import os
import numpy as np

def save_list_to_txt(line_data, save_path):
    """
    将列表数据存入txt文件
    :param line_data:
    :return:
    """
    with open(save_path, "w") as f:
        for data in line_data:
            f.write(str(data))
            f.write("\n")
    f.close()
    print("the data was successfully saved in {}".format(save_path))
