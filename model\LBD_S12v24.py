import os
from thop import profile
import torch
import torch.nn as nn
import torch.nn.functional as F
from timm.models.layers import DropPath, to_2tuple, trunc_normal_
from timm.models.registry import register_model
from timm.models.vision_transformer import _cfg
from timm.models.registry import register_model
import math
import warnings
warnings.filterwarnings('ignore')

from einops import rearrange
from dcn_v2 import DCN
from model.UANet import *
from model.PVT import *

class SpatialAttention(nn.Module):
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        self.spatial = nn.Sequential(
            nn.Conv2d(2, 1, kernel_size=7, stride=1, padding=(7-1)//2, dilation=1,groups=1, bias=False),
            nn.BatchNorm2d(1, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True),
        )
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        s_w = torch.cat((torch.max(x, 1)[0].unsqueeze(1), torch.mean(x, 1).unsqueeze(1)), dim=1)
        s_w = self.sigmoid(self.spatial(s_w))
        return s_w


class Feature_Fusion(nn.Module):
    def __init__(self,high_channel,low_channel,out_channel, num_classes):
        super(Feature_Fusion, self).__init__()
        self.rank = Uncertainty_Rank_Algorithm()
        self.high_channel = high_channel
        self.low_channel = low_channel
        self.out_channel = out_channel
        self.conv_fusion = nn.Sequential(
            BasicConv2d(2*self.out_channel, self.out_channel,3,1,1),
            BasicConv2d(self.out_channel, self.out_channel,3,1,1)
        )

    def forward(self, feature_low, feature_high):
        fusion = torch.cat((F.interpolate(feature_high, feature_low.size()[2:], mode='bilinear', align_corners=True), feature_low), dim=1)
        fusion = self.conv_fusion(fusion)
        return fusion

class UANet_Sentinel(nn.Module):
    def __init__(self, backbone="pvt_v2_b2_s2", in_channels=17, de_channel=64, num_classes=2):
        super().__init__()
        # 1 construct the pvt backbone for sentinel data
        self.backbone = eval(backbone)()
        path = "/mnt/d2/zxq/BDS12/model/PTH/pvt_v2_b2.pth"
        path = "/mnt/d2/zxq/BDS12/model/PTH/pvt_v2_b5.pth"
        save_model = torch.load(path)
        model_dict = self.backbone.state_dict()
        new_state_dict = {k:v if v.size()==model_dict[k].size()  else  model_dict[k] for k,v in zip(model_dict.keys(), save_model.values())}
        self.backbone.load_state_dict(new_state_dict, strict=False)

        # 2 modify the stride of steam layer of PVT
        self.backbone.patch_embed1.proj = nn.Conv2d(in_channels, 64, kernel_size=7, stride=1, padding=3, bias=False)
        # print(self.backbone.patch_embed1)

        # 3 construct the decoder for sentinel fetaure
        self.conv_2 = nn.Sequential(MBDC_low(64,de_channel))
        self.conv_3 = nn.Sequential(MBDC_low(128,de_channel))
        self.conv_4 = nn.Sequential(MBDC_low(320,de_channel))
        self.conv_5 = nn.Sequential(MBDC_low(512,de_channel))

        self.ufm_layer4 = Feature_Fusion(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)
        self.ufm_layer3 = Feature_Fusion(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)
        self.ufm_layer2 = Feature_Fusion(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)

    def _repeat_reshape(self, predict, frame):
        predict = predict.unsqueeze(1).repeat(1, frame, 1, 1, 1)
        predict = rearrange(predict, "b t c h w -> (b t) c h w")
        return predict
    
    def forward(self, images):
        B, frame, C, h, w = images.shape
        images = images.view(B*frame, C, h, w)
        layer2, layer3, layer4, layer5 = self.backbone(images)
        layer5 = self.conv_5(layer5)
        layer4 = self.conv_4(layer4)
        layer3 = self.conv_3(layer3)
        layer2 = self.conv_2(layer2)


        fusion = self.ufm_layer4(layer4,layer5)
        fusion = self.ufm_layer3(layer3,fusion)
        fusion = self.ufm_layer2(layer2,fusion)

        return fusion


class MBDC_low(nn.Module):
    def __init__(self, in_channel, out_channel):
        super(MBDC_low, self).__init__()
        self.relu = nn.ReLU(True)
        out_channel_sum = out_channel * 3

        self.branch0 = nn.Sequential(
            BasicConv2d(in_channel, out_channel, 1, 1, 0, 1)
        )
        self.branch1 = nn.Sequential(
            BasicConv2d(in_channel, out_channel, 1, 1, 0, 1),
            BasicConv2d(out_channel, out_channel, 3, 1, 1, 1),
            BasicConv2d(out_channel, out_channel, 3, 1, 2, 2),
            BasicConv2d(out_channel, out_channel, 3, 1, 4, 4)
        )
        self.branch2 = nn.Sequential(
            BasicConv2d(in_channel, out_channel, 1, 1, 0, 1),
            BasicConv2d(out_channel, out_channel, 3, 1, 1, 1),
            BasicConv2d(out_channel, out_channel, 3, 1, 2, 2),
            BasicConv2d(out_channel, out_channel, 3, 1, 3, 3)
        )
        self.conv_cat = BasicConv2d(out_channel_sum, out_channel, 3, 1, 1, 1)
        self.conv_res = BasicConv2d(in_channel, out_channel, 1, 1, 0, 1)

    def forward(self, x):
        x0 = self.branch0(x)
        x1 = self.branch1(x)
        x2 = self.branch2(x)

        x_cat = self.conv_cat(torch.cat((x0, x1, x2), dim=1))

        x = self.relu(x_cat + self.conv_res(x))
        return x


class UANet_VGG(nn.Module):
    def __init__(self, in_channels=17, de_channel=64, num_classes=2):
        super(UANet_VGG, self).__init__()
        vgg16_bn = models.vgg16_bn(pretrained=True)
        self.inc = vgg16_bn.features[:5]  # 64
        
        self.inc[0] = nn.Conv2d(in_channels, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        # print(self.inc)

        self.down1 = vgg16_bn.features[5:12]  # 64
        self.down2 = vgg16_bn.features[12:22]  # 64
        self.down3 = vgg16_bn.features[22:32]  # 64
        self.down4 = vgg16_bn.features[32:42]  # 64

        self.conv_1 = BasicConv2d(64,de_channel,3,1,1)
        self.conv_2 = nn.Sequential(MBDC_low(128,de_channel))
        self.conv_3 = nn.Sequential(MBDC_low(256,de_channel))
        self.conv_4 = nn.Sequential(MBDC_low(512,de_channel))
        self.conv_5 = nn.Sequential(MBDC_low(512,de_channel))

        self.ufm_layer4 = Feature_Fusion(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)
        self.ufm_layer3 = Feature_Fusion(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)
        self.ufm_layer2 = Feature_Fusion(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)
        self.ufm_layer1 = Feature_Fusion(high_channel= de_channel,low_channel=de_channel, out_channel=de_channel, num_classes=num_classes)


    def forward(self, images):
        B, frame, C, h, w = images.shape
        x = images.view(B*frame, C, h, w)
        size = x.size()[2:]

        layer1 = self.inc(x)
        layer2 = self.down1(layer1)
        layer3 = self.down2(layer2)
        layer4 = self.down3(layer3)
        layer5 = self.down4(layer4)


        layer5 = self.conv_5(layer5)
        layer4 = self.conv_4(layer4)
        layer3 = self.conv_3(layer3)
        layer2 = self.conv_2(layer2)
        layer1 = self.conv_1(layer1)

        fusion = self.ufm_layer4(layer4,layer5)
        fusion = self.ufm_layer3(layer3,fusion)
        fusion = self.ufm_layer2(layer2,fusion)
        fusion = self.ufm_layer1(layer1,fusion)

        return fusion

class Offset_Guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        frame = 16
        self.conv_offset = nn.Conv2d(decoder_channel*2, 2, kernel_size=3, stride=1, padding=1, bias=True)
        self.conv_spatial_fusion = BasicConv2d(decoder_channel*2, decoder_channel, kernel_size=3, padding=1, stride=1)
        self.conv_time_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=int(decoder_channel*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.BatchNorm2d(decoder_channel*frame),
            nn.ReLU(inplace=True),
        )


    def _make_grid(self, input):
        B, C, H, W = input.size()
        xx = torch.arange(0, W).view(1, -1).repeat(H, 1).cuda()
        yy = torch.arange(0, H).view(-1, 1).repeat(1, W).cuda()
        xx = xx.view(1, 1, H, W).repeat(B, 1, 1, 1)
        yy = yy.view(1, 1, H, W).repeat(B, 1, 1, 1)
        grid = torch.cat((xx, yy), 1).float()
        return grid

    def _warp(self, input, flow, grid, mode="bilinear", padding_mode="zeros"):
        B, C, H, W = input.size()
        vgrid = grid + flow

        vgrid[:, 0, :, :] = 2.0 * vgrid[:, 0, :, :].clone() / max(W - 1, 1) - 1.0
        vgrid[:, 1, :, :] = 2.0 * vgrid[:, 1, :, :].clone() / max(H - 1, 1) - 1.0
        vgrid = vgrid.permute(0, 2, 3, 1)
        output = torch.nn.functional.grid_sample(input, vgrid, padding_mode=padding_mode, mode=mode, align_corners=True)

        return output

    def forward(self, current_frames, referent_frames):
        B, frame, C, H, W = referent_frames.shape
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

        assert current_frames.shape == referent_frames.shape

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")
        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)

        offset = self.conv_offset(fusion_frames)
        grid = self._make_grid(referent_frames1)
        referent_frames1 = self._warp(referent_frames1, offset, grid)
        
        fusion_frames = self.conv_spatial_fusion(torch.cat((current_frames1, referent_frames1), dim=1))
        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b c (h w) t", b=B, t=frame, c=C, h=H, w=W)

        # fusion_frames = rearrange(fusion_frames, "b t c h w -> b c (h w) t")
        fusion_frames = self.conv_time_fusion(fusion_frames)
        fusion_frames = rearrange(fusion_frames.squeeze(-1), "b (t c) (h w) -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames, offset


class TemporalAttention(nn.Module):
    def __init__(self, decoder_channel: int, num_heads: int = 8):
        super(TemporalAttention, self).__init__()
        self.decoder_channel = decoder_channel
        self.num_heads = num_heads
        self.attn = nn.MultiheadAttention(embed_dim=decoder_channel, num_heads=num_heads, batch_first=True)
        self.norm = nn.LayerNorm(decoder_channel)
        self.ffn = nn.Sequential(
            nn.Linear(decoder_channel, decoder_channel * 4),
            nn.ReLU(),
            nn.Linear(decoder_channel * 4, decoder_channel)
        )

    def forward(self, x):
        """
        x: Tensor of shape [B, T, C, H, W]
        Returns:
            Tensor of same shape [B, T, C, H, W] after temporal attention
        """
        B, T, C, H, W = x.shape
        x = x.permute(0, 3, 4, 1, 2)  # [B, H, W, T, C]
        x = x.reshape(B * H * W, T, C)  # [B*H*W, T, C]

        # Attention
        x_norm = self.norm(x)
        attn_output, _ = self.attn(x_norm, x_norm, x_norm)  # Self-attention
        x = x + attn_output  # Residual connection

        # Feed-forward
        x_ffn = self.ffn(self.norm(x))
        x = x + x_ffn  # Residual connection

        # Reshape back
        x = x.reshape(B, H, W, T, C).permute(0, 3, 4, 1, 2)  # [B, T, C, H, W]
        return x

class DCN_guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.dcn = DCN(decoder_channel*2, decoder_channel, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, current_frames, referent_frames):
        B, frame, C, H, W = referent_frames.shape
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

        assert current_frames.shape == referent_frames.shape

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")

        # s2_angle = s2_angle - s2_angle[:, -1:,]
        # s2_angle = rearrange(s2_angle, "b t c h w -> (b t) c h w")

        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)

        fusion_frames, offset = self.dcn(fusion_frames)
        fusion_frames = self.relu(fusion_frames)

        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames, offset

class time_fusion(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        frame = 16
        self.conv_time_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=int(decoder_channel*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.BatchNorm2d(decoder_channel*frame),
            nn.ReLU(inplace=True),
        )
    def forward(self, x):
        B, frame, C, H, W = x.shape
        x = rearrange(x, "b t c h w -> b c (h w) t", b=B, t=frame, c=C, h=H, w=W)
        fusion_frames = self.conv_time_fusion(x).squeeze(-1)
        fusion_frames = rearrange(fusion_frames, "b (t c) (h w) -> b t c h w", b=B, t=frame, c=C, h=H, w=W)
        return fusion_frames


class SuperResolution(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()

        self.ts = time_fusion(decoder_channel)
        self.sr = nn.Sequential(
            nn.PixelShuffle(upscale_factor=2),
            BasicConv2d(decoder_channel*4, decoder_channel, 3, 1, 1, 1),
            # nn.PixelShuffle(upscale_factor=2),
            # BasicConv2d(decoder_channel, decoder_channel, 3, 1, 1, 1)
        )

    def forward(self, x):
        x = self.ts(x)
        x = self.sr(rearrange(x, "b t c h w -> b (t c) h w"))
        return x

class ResBlock(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.conv_path = nn.Sequential(
            BasicConv2d(decoder_channel, decoder_channel, kernel_size=3, stride=1, padding=1),
            BasicConv2d(decoder_channel, decoder_channel, kernel_size=3, stride=1, padding=1)
        )

        self.res = nn.Conv2d(decoder_channel, decoder_channel, kernel_size=1)
        self.fusion = BasicConv2d(decoder_channel, decoder_channel, kernel_size=3, stride=1, padding=1)

    def forward(self, x):
        x = self.conv_path(x) + self.res(x)
        x = self.fusion(x)
        return x

class time_serise_damage_feature(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.pre_normal = nn.LayerNorm(decoder_channel)
        self.pre_qkv_proj = nn.Linear(decoder_channel, decoder_channel * 3)

        self.post_normal = nn.LayerNorm(decoder_channel)
        self.post_qkv_proj = nn.Linear(decoder_channel, decoder_channel * 3)

        self.mlp_proj = nn.Linear(decoder_channel, decoder_channel)
        self.scale = decoder_channel ** -0.5

        self.ffn = nn.Sequential(
            nn.LayerNorm(decoder_channel),
            nn.Linear(decoder_channel, decoder_channel * 4),
            nn.ReLU(),
            nn.Linear(decoder_channel * 4, decoder_channel)
        )

    def forward(self, time_series_feature):
        B, T, C, H, W = time_series_feature.shape

        # 获取最后一帧的特征
        pre_seq = time_series_feature[:, :-1]  # 灾前特征序列
        post_seq = time_series_feature[:, -1:]  # 灾后特征，最后一帧
        return pre_seq - post_seq

        # ===== Pre-disaster (T-1) Feature Flatten =====
        pre_seq_r = pre_seq.reshape(B * (T - 1), C, -1).transpose(1, 2)   # [B*(T-1), N, C]
        pre_qkv = self.pre_qkv_proj(self.pre_normal(pre_seq_r))                             # [B*(T-1), N, 2C]
        pre_q, pre_k, pre_v = pre_qkv.chunk(3, dim=-1)

        # ===== Post-disaster Frame: map first, then repeat =====
        post_seq_r = post_seq.squeeze(1).permute(0, 2, 3, 1).reshape(B, H*W, C)  # [B*H*W, C]
        post_qkv = self.post_qkv_proj(self.post_normal(post_seq_r))                         # [B*H*W, 3C]
        post_q, post_k, post_v = post_qkv.chunk(3, dim=-1)   # Each: [B, C, N]

        # Repeat to match pre-disaster shape: [B*(T-1), N, C]
        post_q = post_q.unsqueeze(1).repeat(1, T - 1, 1, 1).reshape(B * (T - 1), C, -1).transpose(1, 2)
        post_k = post_k.unsqueeze(1).repeat(1, T - 1, 1, 1).reshape(B * (T - 1), C, -1).transpose(1, 2)
        post_v = post_v.unsqueeze(1).repeat(1, T - 1, 1, 1).reshape(B * (T - 1), C, -1).transpose(1, 2)

        # ===== Scaled Dot-Product Attention =====
        pre_attn = F.softmax((pre_q @ pre_k.transpose(-2, -1)) * self.scale, dim=-1)
        post_attn = F.softmax((post_q @ post_k.transpose(-2, -1)) * self.scale, dim=-1)

        # ===== Attention Difference Feature =====
        diff_attn = pre_attn - post_attn
        fused = pre_v + self.mlp_proj(diff_attn @ pre_v) # [B*(T-1), N, C]
        fused = fused.transpose(1, 2).reshape(B, T - 1, C, H, W)

        # fused = (pre_attn - post_attn)@(pre_v - post_v)
        # fused = (pre_seq - post_seq) + self.mlp_proj(fused).transpose(1, 2).reshape(B, T - 1, C, H, W)
        # fused = fused + rearrange(self.ffn(rearrange(fused, "b t c h w -> (b t) (h w) c")), "(b t) (h w) c -> b t c h w", b=B, t=T-1, c=C, h=H, w=W)

        return fused


class S2_Building_damage(nn.Module):
    def __init__(self, backbone, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        decoder_channel = 64

        self.low2_net = UANet_Sentinel(backbone=backbone, in_channels=s2_inchannel, de_channel=decoder_channel, num_classes=num_classes)
        # self.low2_net = UANet_VGG(in_channels=s2_inchannel, de_channel=decoder_channel, num_classes=num_classes)

        self.fcn = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False))
            for _ in range(frame+1)])

        # 4 pre-sentinel-2 to pre-buildings
        self.offset_guide = DCN_guide(decoder_channel)
        self.sr_net = SuperResolution(decoder_channel)
        self.res = ResBlock(decoder_channel)
        self.building_layer = nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)
        self.building_layer_aux = nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)


        self.damage_diff_feature = time_serise_damage_feature(decoder_channel)
        self.post_offset_guide = DCN_guide(decoder_channel)
        self.post_sr_net = SuperResolution(decoder_channel)
        self.post_res = ResBlock(decoder_channel)
        self.damage_layer = nn.Conv2d(in_channels=decoder_channel, out_channels=damage_classes, kernel_size=1, stride=1, padding=0)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net

    def _feature_transfer(self, time_serise_feature):
        B, total_frame, C, H, W = time_serise_feature.shape
        # print("shape is", B, total_frame, C, H, W)
        aligned_features = []
        T15 = time_serise_feature[:, -2]
        
        for t in range(total_frame):  # 对于T1到T17
            curr_feat = time_serise_feature[:, t]  # [B, C, H, W]
            feat_pair = torch.cat([curr_feat, T15], dim=1)
            A = self.fcn[t](feat_pair)
            aligned_feat = torch.mul(curr_feat, A).squeeze(1)
            aligned_features.append(aligned_feat)
        aligned_features = torch.stack(aligned_features, dim=1) # [B, 17, C, H, W]
        return aligned_features
 
    def _encoder_feature_constraint(self, inputs, device, high_features, low_decoder_features, outputs):
        pre_label = inputs[3].unsqueeze(1).to(device).to(torch.float32)
        post_label = inputs[4].unsqueeze(1).to(device).to(torch.float32)
        # pseudo_label = 1 - inputs[5].unsqueeze(1).unsqueeze(1).to(device).to(torch.long)
        B, _, H, W = pre_label.shape
        B, frame, _, h, w = low_decoder_features.shape

        high_features = F.interpolate(high_features, size=(h, w), mode='bilinear', align_corners=True)
        outputs["loss"] = F.mse_loss(F.normalize(low_decoder_features[:, -2], dim=1), F.normalize(high_features.detach(), dim=1))

        T15 = F.interpolate(low_decoder_features[:, -2], size=(H, W), mode='bilinear')
        T16 = F.interpolate(low_decoder_features[:, -1], size=(H, W), mode='bilinear')

        pre_label = torch.where(pre_label == 1, 1, 0)
        post_label = torch.where(post_label == 1, 0, 1)#*pseudo_label

        # 计算损毁区域
        # outputs["loss"] += F.mse_loss(F.normalize(T16 * post_label), F.normalize(T15.detach() * post_label), reduction='mean')

        for frame_id in range(frame - 2):
            frame_feature = F.normalize(low_decoder_features[:, frame_id])
            outputs["loss"] += F.mse_loss(frame_feature, F.normalize(low_decoder_features[:, -2].detach()), reduction='mean')

        return outputs

    def forward(self, inputs, device, outputs, H, W):
        s2_images = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
        B, frame, _, h, w = s2_images.shape
        low_encoder_features = self.low2_net(images=s2_images).view(B, frame, -1, h, w)

        low_encoder_features = self._feature_transfer(low_encoder_features)
        low_pre_features_offset, offset = self.offset_guide(current_frames=low_encoder_features[:, -2], referent_frames=low_encoder_features[:, :-1])
        low_encoder_features = torch.cat((low_pre_features_offset, low_encoder_features[:, -1:]), dim=1)

        if self.training:
            outputs = self._encoder_feature_constraint(inputs, device, outputs["pre_high_features"], low_encoder_features, outputs)

        # outputs["low_feature_lists"] = low_pre_features_offset
        # outputs["pre_offset"] = offset

        low_pre_features = self.sr_net(low_pre_features_offset)
        low_pre_features = self.res(low_pre_features)
        low_outs = self.building_layer(low_pre_features)
        outputs["pre_low_features"] = F.interpolate(low_pre_features, size=(H//4, W//4), mode='bilinear', align_corners=True)
        outputs["pre_low_building"] = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)



        pre_building_map1 = F.interpolate(torch.sigmoid(outputs["pre_high_building"]), size=(H//4, W//4), mode='bilinear', align_corners=True)[:, 1:]
        pre_building_map2 = F.interpolate(torch.sigmoid(outputs["pre_low_building"]), size=(H//4, W//4), mode='bilinear', align_corners=True)[:, 1:]
        pre_building_map = (pre_building_map1 + pre_building_map2)/2
        pre_building_map = F.interpolate(pre_building_map, size=(H//4, W//4), mode='bilinear', align_corners=True)

        low_post_features = self.damage_diff_feature(low_encoder_features)
        low_post_features, offset = self.post_offset_guide(current_frames=low_post_features[:, -1], referent_frames=low_post_features)
        low_post_features = self.post_sr_net(low_post_features)
        low_post_features = self.post_res(low_post_features*pre_building_map)       
        low_outs = self.damage_layer(low_post_features)
        outputs["post_low_features"] = F.interpolate(low_post_features, size=(H//4, W//4), mode='bilinear', align_corners=True)
        outputs["post_high_features"] = outputs["pre_high_features"]
        outputs["post_low_damage"] = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)
        return outputs
    

class LBDv24_S2(nn.Module):
    def __init__(self, backbone="pvt_v2_b5_s2", s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        # 1 High RGB Net
        decoder_channel = 64
        self.high_net = UANet_pvt(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/13.pt"
        self.high_net = self._load_weight(self.high_net, pre_train_model_path)

        self.low_net = S2_Building_damage(backbone=backbone, s2_inchannel=s2_inchannel, frame=frame, num_classes=num_classes, damage_classes=damage_classes)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
 

    def forward(self, inputs, device):
        outputs = {}
        # with torch.no_grad():
        B, _, H, W = inputs[0].to(device).to(torch.float32).shape
        outputs = self.high_net(inputs[0].to(device).to(torch.float32), device, mode="pre", outputs=outputs)
        outputs = self.low_net(inputs, device, outputs, H, W)

        return outputs



if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '3'
    # deep_model = UANet_Sentinel().cuda()

    deep_model = LBDv24_S2(backbone="pvt_v2_b2_s2", s2_inchannel=17, frame=16, num_classes=2, damage_classes=2).cuda()
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384).cuda()
    low_images1 = torch.rand(bs, 16, 17, 48, 48).cuda()
    low_images2 = torch.rand(bs, 1, 17, 48, 48).cuda()
    pre_label = torch.rand(bs, 384, 384).cuda()
    post_label = torch.rand(bs, 384, 384).cuda()
    inputs = [hight_image, low_images1, low_images2, pre_label, post_label]
    device = torch.device("cuda")

    outputss = deep_model(inputs, device)

    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
