# S1_S2数据集训练
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/santa_hrnet_s2.py 
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/santa_hrnet_s1.py 
# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/santa_s2_LBD.py 
# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/santa_s2_LBDv12.py 
# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/santa_s1_LBD.py 


# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/all_s2_LBDv11.py 
# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/santa_s2_LBDv14.py


# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/palisades_s2_hrnet.py 
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/palisades_s1_hrnet.py 
# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/palisades_s2_LBD14.py 


# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/eaton_s2_hrnet.py 
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/eaton_s1_hrnet.py 
# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/eaton_s2_LBD14.py 


# 一次性训练
# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/palisades_s2_LBD14.py;
# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/santa_s2_LBDv15.py;
# CUDA_VISIBLE_DEVICES="1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=3 --master_port=25636 train_S12.py --config_path ./config/eaton_s2_LBD14.py;


# palisades to santa
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12_pseudo.py --config_path ./config/santa_s2_LBDv16.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/gaza_s1_LBDv16.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/gaza_s1_uabcd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/gaza_s2_uabcd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/santa_s2_uabcd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/santa_s1_uabcd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/palu_s2_uabcd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/palu_s1_uabcd.py;

# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/santa_s2_hrsicd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/santa_s1_hrsicd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/gaza_s2_hrsicd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/gaza_s1_hrsicd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/palu_s2_hrsicd.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/palu_s1_hrsicd.py;


# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/palu_s1_LBDv16.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/palu_s2_LBDv16.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/gaza_s2_LBDv16.py;


# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/santa_s2_LBDv22.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/santa_s2_LBDv25.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/palu_s2_LBDv16.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12.py --config_path ./config/santa_s1_LBDv21.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25637 train_S12.py --config_path ./config/santa_s2_ESRGAN.py;

# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12_GAN.py --config_path ./config/santa_s2_LBDv25.py;
CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25637 train_S12_GAN.py --config_path ./config/santa_s2_ESRGAN.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12_GAN.py --config_path ./config/santa_s2_LBDv28.py;


# 在xBD数据集上训练
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_BD.py --config_path ./config/xBD_HRNet.py;
# CUDA_VISIBLE_DEVICES="0, 1, 2, 3" python3 -m torch.distributed.run --nproc_per_node=4 --master_port=25636 train_S12_pseudo.py --config_path ./config/xBD_UANet_S2.py;
