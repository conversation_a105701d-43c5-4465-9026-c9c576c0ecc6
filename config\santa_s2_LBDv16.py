import torch
from torch import nn
import os
from glob import glob
import albumentations as albu
import argparse
from pathlib import Path
import numpy as np
from model.LBD_S12v16 import HRNet_S2
from loss.loss import OhemCELoss
# from dataset.Santa import Santa_S12, Santa_S12_tensor, dataset_collate
from dataset.Santa_pseudolabel import Santa_S12, dataset_collate

# =============================训练参数============================ #
batch_size = 3
lr = 1e-4
size = 384
epochs = 100
output_building = 2
output_damage = 2
data_name = "santa"
model_name = "lbdv16"
# save_path = os.path.join("./result", data_name, model_name, "santa2palu")
save_path = os.path.join("./result", data_name, model_name, "santa2palisades_uanet")
os.makedirs(save_path, exist_ok=True)

model = HRNet_S2(s2_inchannel=17, frame=16, num_classes=output_building, damage_classes=output_damage, post_input="S2")
pre_train_model_path = None
osm = False

criterion1 = OhemCELoss(thresh=0.7, n_min=batch_size*size*size//16, ignore_index=255)
criterion2 = nn.L1Loss()
criterion3 = nn.KLDivLoss(reduction="mean")

# 数据集加载 + 多GPU训练
device = 'cuda'
train_dataset = Santa_S12('train')
val_dataset = Santa_S12('test')
collate_fn = dataset_collate
# =============================训练参数============================ #


# =============================测试参数============================ #
data_name = "santa"
model_name = "lbdv14"
save_name = "2_S2"
test_base_path = f"./dataset/{data_name}/test/"
test_base_save_path = os.path.join("./result", data_name)
model_path = os.path.join("./result/", data_name, model_name, save_name, "54.pt")

def make_path(file_path):
    os.makedirs(file_path, exist_ok=True)
    return file_path

pre_high_image_save_path  = make_path(os.path.join(test_base_save_path, "pre_high_image"))
post_high_image_save_path  = make_path(os.path.join(test_base_save_path, "post_high_image"))
pre_s2_image_save_path = make_path(os.path.join(test_base_save_path, "pre_s2_image"))
post_s2_image_post_save_path = make_path(os.path.join(test_base_save_path, "post_s2_image"))
pre_label_save_path = make_path(os.path.join(test_base_save_path, "pre_label"))
post_label_save_path = make_path(os.path.join(test_base_save_path, "post_label"))
pre_s1_image_save_path = make_path(os.path.join(test_base_save_path, "pre_s1_image"))
post_s1_image_post_save_path = make_path(os.path.join(test_base_save_path, "post_s1_image"))

pre_high_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "pre_high_result"))
post_high_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "post_high_result"))
pre_s2_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "pre_s2_result"))
post_s2_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "post_s2_result"))
pre_s1_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "pre_s1_result"))
post_s1_result_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "post_s1_result"))
plot_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "plot"))
plot_feature_save_path = make_path(os.path.join(test_base_save_path, model_name, save_name, "plot_feature"))


pre_high_image_paths = []
post_high_image_paths = []
pre_s2_image_paths = []
post_s2_image_paths = []
pre_label_paths = sorted(glob(os.path.join(test_base_path, "labels", "*santa*pre**.tif")))
pre_label_paths+= sorted(glob(os.path.join(test_base_path.replace("/test/", "/hold/"), "labels", "*santa*pre**.tif")))
post_label_paths = []
pre_s1_image_paths = []
post_s1_image_paths = []

for pre_label_path in pre_label_paths:
    image_path = pre_label_path.split(".tif")[0]
    pre_high_image_paths.append(image_path.replace("/labels/", "/images/")+".tif")
    post_high_image_paths.append(image_path.replace("/labels/", "/images/").replace("_pre_", "_post_")+".tif")
    pre_s2_image_paths.append(image_path.replace("/labels/", "/S2/"))
    post_s2_image_paths.append(image_path.replace("/labels/", "/S2_post/").replace("_pre_", "_post_"))
    pre_s1_image_paths.append(image_path.replace("/labels/", "/S1/"))
    post_s1_image_paths.append(image_path.replace("/labels/", "/S1_post/").replace("_pre_", "_post_"))
    post_label_paths.append(pre_label_path.replace("_pre_", "_post_"))


s2_mean_std = np.loadtxt("./dataset/santa/santa_s2_mean_std3.txt")
# s2_mean_std = np.loadtxt("./dataset/santa_paliasades_eaton_s2_mean_std.txt")
s2_mean, s2_std = s2_mean_std[0,:], s2_mean_std[1,:]


label_keys = [
    [  0,   0,   0], # 0 非建筑物区域
    [255, 255, 255], # 1 未损毁
    [  0,   0, 255], # 2 Moderate
    [  0, 255,   0], # 3 Severe
    [255,   0,   0], # 4 Destroyed
]

# =============================测试参数============================ #
