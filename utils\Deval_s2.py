import enum
import os
import numpy as np
from PIL import Image
from tqdm import tqdm
import warnings
from glob import glob
warnings.filterwarnings("ignore")

def safe_divide(a, b):
    return a / b if b != 0 else 0

def binary_eval(base_path, model_name, mode, resolution):
    city_name = "" # Rubizhne Mariupol Haiti Nepal Yushu left right santa harvey
    test_names = glob(os.path.join(base_path, model_name, resolution, f"*{city_name}**.tif"))
    test_names.sort()
    count = 0
    TP, FP, FN, TN = 0, 0, 0, 0
    labs, pres = [], []
    for id, test_name in tqdm(enumerate(test_names)):
        name = os.path.basename(test_name)
        lab_path = os.path.join(base_path, mode, name)
        # print(lab_path)
        pre_path = os.path.join(base_path, model_name, resolution, name)
        lab = np.array(Image.open(lab_path)).astype(np.float32)
        if mode=="post_label" and ("/santa/" in test_name or "/palu/" in test_name):
            lab = np.where(lab>=3, 1, 0)

        elif mode=="post_label" and ("/gaza/" in test_name):
            lab = np.where(lab>=2, 1, 0)

        palu_cloud_names = [f"palu-tsunami_{str(id).zfill(8)}_post" for id in [3, 4, 6, 7, 8, 12, 16, 18, 21, 32, 41, 96, 117, 120, 143, 192, 194]]
        if name.split("_disaster")[0] in palu_cloud_names and resolution=="post_s2_result":
            continue

        lab = np.where(lab>=0.5, 1, 0)
        pre = np.array(Image.open(pre_path))
        pre = np.where(pre>=0.5, 1, 0)
        c = 1
        TP += np.logical_and(pre == c, lab == c).sum()
        FN += np.logical_and(pre != c, lab == c).sum()
        FP += np.logical_and(pre == c, lab != c).sum()
        TN += np.logical_and(pre != c, lab != c).sum()
        count += 1

    IOU = TP / (TP + FP + FN)
    Recall = TP / (TP + FN)
    Precision = TP / (TP + FP)
    F1 = 2 * Recall * Precision / (Precision + Recall)
    OA = (TP + TN) / (TP + FP + FN + TN)

    # print(count, F1, IOU, Recall, Precision, OA)
    print(f"{base_path} ---- {resolution} evaluation: F1:{round(F1*100., 2)}%, P:{round(Precision*100., 2)}%")
    print(TP,FP,FN,TN)

if __name__ == "__main__":
    
    # mode_name = "uabcd/2_S2"

    # mode_name = "hrsicd/1_S1"

    # mode_name = "lbdv16/1_S1"


    # 测试santa-S2数据
    # binary_eval(base_path="./result/santa", model_name=f"{mode_name}", mode="post_label", resolution="post_s2_result")


    # binary_eval(base_path="./result/santa", model_name=f"{mode_name}", mode="post_label", resolution="post_s1_result")


   
    # Gaza数据
    mode_name = "lbdv16/2_S2"
    binary_eval(base_path="./result/gaza", model_name=f"{mode_name}", mode="post_label", resolution="post_s2_result")

    mode_name = "lbdv16/1_S1"
    binary_eval(base_path="./result/gaza", model_name=f"{mode_name}", mode="post_label", resolution="post_s1_result")


    # palu
    mode_name = "lbdv16/2_S2"
    binary_eval(base_path="./result/palu", model_name=f"{mode_name}", mode="post_label", resolution="post_s2_result")

    mode_name = "lbdv16/1_S1"
    binary_eval(base_path="./result/palu", model_name=f"{mode_name}", mode="post_label", resolution="post_s1_result")
