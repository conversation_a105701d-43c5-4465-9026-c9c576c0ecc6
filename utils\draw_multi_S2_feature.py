import sys
sys.path.append("/mnt/d2/zxq/BDS12")
import argparse
import os
import re
import torch
import torch.nn as nn
from torchvision import transforms
import torch.nn.functional as F
from tqdm import tqdm
import torch.multiprocessing
import numpy as np
import cv2
from PIL import Image
from matplotlib import pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
from cfg import py2cfg
from pathlib import Path
from glob import glob
from osgeo import gdal, osr
from pyproj import Proj, Transformer
from scipy.ndimage import zoom
from datetime import datetime
torch.multiprocessing.set_sharing_strategy('file_system')

import sys
sys.path.append("/mnt/d2/zxq/pytorch-grad-cam-master")
from pytorch_grad_cam import GradCAM # type: ignore


def visualize_dcn_offset(feature_lists, offset_tensor, point_index=0, downsample=2, row=4, col=4, save_path=""):
    """
    offset_tensor: Tensor of shape [T, 2N, H, W]
    frame_index: index of temporal frame to visualize
    point_index: index of one of 9 sampling points (0~8)
    downsample: stride to reduce visual density
    """
    plt.figure(figsize=(row*40, col*40))
    plt.suptitle(f"Offset Visualiza of point_index: {point_index}", fontsize=24)

    for frame_index in range(offset_tensor.shape[0]):
        plt.subplot(row, col, frame_index+1)
        plt.axis('off')
        feature = feature_lists[frame_index*2]
        offset = offset_tensor[frame_index].detach().cpu().numpy()  # [18, 48, 48]
        H, W = offset.shape[1], offset.shape[2]

        dx = offset[point_index]             # [H, W] -- x方向偏移
        dy = offset[point_index + 9]         # [H, W] -- y方向偏移
        offset = offset.reshape(2, 9, H, W)

        x = np.arange(0, W, downsample)
        y = np.arange(0, H, downsample)
        X, Y = np.meshgrid(x, y)

        U = dx[::downsample, ::downsample]
        V = dy[::downsample, ::downsample]

        plt.imshow(feature, cmap = plt.cm.jet)
        plt.quiver(X, Y, U, V, angles='xy', scale_units='xy', scale=1, color='blue')

    # plt.gca().invert_yaxis()
    # plt.title("Deformable Conv Sampling Points")
    plt.axis('off')
    plt.show()
    plt.savefig(save_path)

def classId2rgb(out, config):
    out = np.array(out).astype(np.float32)
    result = np.zeros((out.shape[0], out.shape[1], 3)).astype(np.int8)
    for i in range(len(config.label_keys)):
        result[out==i, :] = config.label_keys[i]
    # result = np.transpose(result, (2, 0, 1))
    # print(result.shape)
    result = Image.fromarray(result.astype(np.uint8))
    return result

def save_to_tif(pred_result, save_path, test_image_path):
    """
    pred_result: 需要保存的图像
    save_path: 保存路径
    test_image_path: 空间参考系
    """
    img = pred_result
    # print(np.max(img), np.min(img))

    im_geotrans, im_prof = [], []
    tif = gdal.Open(os.path.join(test_image_path))
    im_geotrans.append(tif.GetGeoTransform())
    im_prof.append(tif.GetProjection())
    if 'uint8' in img.dtype.name:
        datatype = gdal.GDT_Byte
    elif 'uint16' in img.dtype.name:
        datatype = gdal.GDT_UInt16
    else:
        datatype = gdal.GDT_Float32
    # print(img.dtype.name)
    #判读数组维数
    if len(img.shape) == 3:
        im_bands, im_height, im_width = img.shape
    else:
        im_bands, (im_height, im_width) = 1, img.shape

    #创建文件
    driver  = gdal.GetDriverByName("GTiff")                #数据类型必须有，因为要计算需要多大内存空间
    dataset = driver.Create(save_path, im_width, im_height, im_bands, datatype)
    dataset.SetGeoTransform(im_geotrans[-1])              #写入仿射变换参数
    dataset.SetProjection(im_prof[-1])                    #写入投影
    if im_bands == 1:
        dataset.GetRasterBand(1).WriteArray(img)  #写入数组数据
    else:
        for i in range(im_bands):
            dataset.GetRasterBand(i+1).WriteArray(img[i])
    del dataset
    # print('保存成功')

def get_tenser_mean(img_tensor, val_max=None, val_min=None):
    img_tensor = torch.mean(img_tensor, dim=1, keepdim=True).cpu().detach().numpy().squeeze()
    # img_tensor = F.interpolate(torch.sigmoid(img_tensor), size=(384, 384), mode="bilinear").cpu().detach().numpy().squeeze()
    if val_max==None:
        val_max, val_min = np.max(img_tensor), np.min(img_tensor)
        # val_max, val_min = 1., 0.
    img_tensor = ((((img_tensor - val_min)/(val_max-val_min)))*255).astype(np.uint8)
    # img_tensor = (img_tensor*255.).astype(np.uint8)
    return img_tensor#, val_max, val_min

def plot_img(datalists, save_path, name, f1=None):
    # datalists = {"pre_high": {}, "post_high": {}, "pre_s2": {}, "post_s2": {},}

    pre_high_datalists = datalists["pre_high"]
    post_high_datalists = datalists["post_high"]
    pre_s2_datalists = datalists["pre_s2"]
    post_s2_datalists = datalists["post_s2"]
    

    draw_datalists = [pre_high_datalists, pre_s2_datalists, post_s2_datalists] # , post_high_datalists 
    f1 = [f1[0], f1[2], f1[3]] # 
    col = len(pre_high_datalists)
    row = len(draw_datalists)
    keys_list = list(datalists.keys())
    fig = plt.figure(figsize=(col*2, row*2)) # (w, h)
    for id, datalists in enumerate(draw_datalists): 
        # 针对灾前画图
        keys_list = list(datalists.keys())
        for keys_id in range(len(keys_list)):
            plt.subplot(row, col, id*col + keys_id+1)
            plt.axis('off')
            if datalists[keys_list[keys_id]] is None:
                continue
            plt.imshow(datalists[keys_list[keys_id]], cmap = plt.cm.jet)
            if keys_list[keys_id]=="out":
                plt.title(f"{keys_list[keys_id]}:{f1[id]}")
            else:
                plt.title(keys_list[keys_id])

    plt.savefig(os.path.join(save_path, name))
    plt.cla()
    plt.close("all")

def plot_feature(draw_datalists, save_path, name, col = 8, row = 5, max_value = 1, min_value = 0, low_time_lists=[], output=None):

    fig = plt.figure(figsize=(col*2, row*2)) # (w, h)
    right_cbar_ax = fig.add_axes([0.93, 0.2, 0.02, 0.6])
    if "_diff" in name:
        cmap = LinearSegmentedColormap.from_list("seismic", plt.cm.seismic(np.linspace(0, 1, 256)))
    else:
        cmap = LinearSegmentedColormap.from_list("seismic", plt.cm.jet(np.linspace(0, 1, 256)))
    right_cbar_ax.imshow(np.linspace(0, 1, 256).reshape(256, 1), cmap=cmap, aspect='auto')
    right_cbar_ax.axis('off')
    max_color = cmap(max_value)
    min_color = cmap(min_value)
    fig.text(0.95, 0.8, str(min_value), color=min_color)
    fig.text(0.95, 0.2, str(max_value), color=max_color)

    for id, datalists in enumerate(draw_datalists):
        # print(f"datalists.shape is {datalists.shape}")
        # if id < 32:
        plt.subplot(row, col, id+1)
        plt.axis('off')
        if "_diff" in name:
            plt.imshow(datalists, cmap = plt.cm.seismic)
        else:
            plt.imshow(datalists, cmap = plt.cm.jet)
            # if id <= 30:
            #     X, Y, U, V = visualize_dcn_offset(output["pre_offset"], frame_index=id//2)
            #     plt.quiver(X, Y, U, V, angles='xy', scale_units='xy', scale=1, color='white')
            #     plt.gca().invert_yaxis()
        plt.title(low_time_lists[id])
        # else:
        #     continue
    plt.savefig(os.path.join(save_path, name))
    plt.cla()
    plt.close("all")

def load_net(model, device, model_path):
    print(model_path)
    net = model.to(device)
    net = torch.nn.DataParallel(net)
    net.load_state_dict(torch.load(model_path, map_location=device), True)
    net.eval()
    # print(net)
    return net

def add_lon_lat_band(gdal_dataset, x_size, y_size):
    image_array = gdal_dataset.ReadAsArray()
    # 获取UTM的投影信息
    source_srs = osr.SpatialReference()
    source_srs.ImportFromWkt(gdal_dataset.GetProjectionRef())

    # 创建WGS84的目标投影信息
    target_srs = osr.SpatialReference()
    target_srs.ImportFromEPSG(4326)  # WGS84坐标系

    # 增加经纬度
    geotransform = gdal_dataset.GetGeoTransform()
    # print(geotransform)
    # 创建坐标转换对象
    transformer = osr.CoordinateTransformation(source_srs, target_srs)

    lon = np.linspace(geotransform[0], geotransform[0] + geotransform[1] * x_size, x_size)
    lat = np.linspace(geotransform[3] + geotransform[5] * y_size, geotransform[3], y_size)
    # 创建坐标点的二维数组
    lon_lat_points = np.array([[l, la] for la in lat for l in lon])

    # 进行坐标转换
    transformed_points = transformer.TransformPoints(lon_lat_points)

    # 分离转换后的经纬度
    lon = np.array([point[0] for point in transformed_points]).reshape(1, x_size, y_size)
    lat = np.array([point[1] for point in transformed_points]).reshape(1, x_size, y_size)
    # print(lon.shape, lat.shape)
    # x = input()
    image_array = np.concatenate((image_array, lon, lat), axis=0)
    return image_array

def add_time_band(image_array, x_size, y_size, high_time_str, low_time_str):
    high_time = datetime.strptime(high_time_str, "%Y%m%d")
    low_time = datetime.strptime(low_time_str, "%Y%m%d")
    time_difference = (low_time - high_time).days
    normalized_difference = time_difference / (2*365)
    expanded_array = np.full((1, x_size, y_size), normalized_difference)
    image_array = np.concatenate((image_array, expanded_array), axis=0)
    # print(f"high_time:{high_time}, low_time:{low_time}, time_difference{time_difference}, normalized_difference:{normalized_difference}")
    # x = input()   
    return image_array

def load_s2_tif(s2_time_series_path: str):
    image_paths = glob(os.path.join(s2_time_series_path + "_S2**.tif"))
    image_paths.sort()

    # print(s2_time_series_path, len(image_paths), image_paths)
    # x = input()

    image_paths = image_paths[-16:]
    high_time_str = "20170617"
    low_time_lists = []
    s2_time_series_images = []
    for image_path in image_paths:
        dataset = gdal.Open(image_path)
        image_array = dataset.ReadAsArray()

        # 获取时间和经纬度
        # x_size = dataset.RasterXSize
        # y_size = dataset.RasterYSize 
        # image_array = add_lon_lat_band(dataset, x_size, y_size)
        # low_time_str = re.search(r'_(\d{8})\.tif$', os.path.basename(image_path)).group(1)
        # image_array = add_time_band(image_array, x_size, y_size, high_time_str, low_time_str)

        low_time_lists.append(image_path.split("_")[-1].rsplit(".", 1)[0])
        low_time_lists.append(image_path.split("_")[-1].rsplit(".", 1)[0])

        s2_time_series_images.append(image_array)
    s2_time_series_images = np.array(s2_time_series_images).reshape((len(image_paths), -1, 48, 48))
    s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)
    return s2_time_series_images, low_time_lists

def load_s1_tif(s1_time_series_path: str):
    image_paths = glob(os.path.join(s1_time_series_path + "**S1**.tif"))
    image_paths.sort()
    s1_time_series_images = []
    for image_path in image_paths:
        dataset = gdal.Open(image_path)
        image_array = dataset.ReadAsArray()
        s1_time_series_images.append(image_array)
    s1_time_series_images = np.array(s1_time_series_images).reshape((len(image_paths), -1, 48, 48))
    # print(image_paths, len(image_paths), s1_time_series_images.shape)
    return s1_time_series_images

def load_S1_C2_image(image_path, mode="S1_C2"):
    image_paths = glob(os.path.join(image_path.replace("S1", mode) +  f"**{mode}**.tif"))
    image_paths.sort()

    time_series_images = []
    for image_path in image_paths:
        dataset = gdal.Open(image_path)
        image_array = dataset.ReadAsArray()
        if "20171103" in image_path and mode=="S1_CohIfg":
            # print(image_array.shape)
            time_series_images.append(image_array[1::2])
        else:
            time_series_images.append(image_array)
    time_series_images = np.array(time_series_images).reshape((len(image_paths), -1, 48, 48))

    # 数据归一化
    time_series_images = torch.from_numpy(np.array(time_series_images, dtype=np.float32))
    return time_series_images

def load_high_image_tif(path):
    dataset = gdal.Open(path)
    high_image = dataset.ReadAsArray()
    high_image = np.array(high_image)
    return high_image

def sentinel22rgb(data):
    # for i in range(3):
    #     data[i, :, :] = (data[i, :, :] - np.nanmin(data[i, :, :])) / (np.nanmax(data[i, :, :]) - np.nanmin(data[i, :, :])) * 255
    # data = np.nan_to_num(data, nan=0.0)
    # data = zoom(data, (1, 8, 8), order=3)
    data = np.clip(data, 0, 4095)  # 限制范围，防止异常值
    data = (data / 4095.0 * 255).astype(np.uint8)  # 归一化到 8-bit

    # data = np.where(data<0, 0, data)
    # data = np.where(data>255, 0, data)
    return data

def sentinel12gray(data):
    data = (data - np.nanmin(data)) / (np.nanmax(data) - np.nanmin(data)) * 255
    data = np.nan_to_num(data, nan=0.0)
    data = np.where(data<0, 0, data)
    data = np.where(data>255, 0, data)
    return data

def load_data(id, config, name, datalists):
    pre_high_image = load_high_image_tif(config.pre_high_image_paths[id])
    post_high_image = load_high_image_tif(config.post_high_image_paths[id])

    pre_s2_image, pre_low_time_lists = load_s2_tif(config.pre_s2_image_paths[id])
    post_s2_image, post_low_time_lists = load_s2_tif(config.post_s2_image_paths[id])
    low_time_lists = [*pre_low_time_lists, *post_low_time_lists]

    pre_label = np.array(Image.open(config.pre_label_paths[id]))
    post_label = np.array(Image.open(config.post_label_paths[id]))
    pre_label = np.where(pre_label>=0.5, 1, 0)
    print(np.array(pre_high_image).shape, np.array(post_high_image).shape, pre_s2_image.shape, post_s2_image.shape, np.array(pre_label).shape, np.array(post_label).shape)
    # print(low_time_lists, len(low_time_lists))

    # =================================================================== #
    # 保存输入数据到文件夹
    pre_s2_rgb = sentinel22rgb(pre_s2_image[-1, [3, 2, 1], :, :])
    post_s2_rgb = sentinel22rgb(post_s2_image[-1, [3, 2, 1], :, :])
    # post_label = np.where(post_label>=0.5, 1, 0)

    datalists = {"pre_high": {}, "post_high": {}, "pre_s2": {}, "post_s2": {}, "images": []}

    datalists["pre_high"]["image"] = Image.fromarray((np.transpose(np.array(pre_high_image), (1, 2, 0))).astype(np.uint8))
    datalists["pre_s2"]["image"] =  Image.fromarray((np.transpose(np.array(pre_s2_rgb), (1, 2, 0))).astype(np.uint8))
    datalists["post_s2"]["image"] =  Image.fromarray((np.transpose(np.array(post_s2_rgb), (1, 2, 0))).astype(np.uint8))

    datalists["pre_high"]["label"] = pre_label*255
    datalists["pre_s2"]["label"] = pre_label*255
    datalists["post_s2"]["label"] = classId2rgb(post_label, config)

    for s2_id in range(pre_s2_image.shape[0]):
        pre_s2_rgb = sentinel22rgb(pre_s2_image[s2_id, [3, 2, 1], :, :])
        datalists["images"].append(Image.fromarray((np.transpose(np.array(pre_s2_rgb), (1, 2, 0))).astype(np.uint8))) 

    # 需要进一步处理灾后数据，以计算F1分数
    post_label = np.where(post_label>=3, 1, 0).astype(np.int8)
    # post_label = np.where(post_label>=0.5, 1, 0)

    pre_high_image = torch.from_numpy(np.array(pre_high_image/255., dtype=np.float32)).unsqueeze(0).to(torch.float32)
    post_high_image = torch.from_numpy(np.array(post_high_image/255., dtype=np.float32)).unsqueeze(0).to(torch.float32)
    pre_s2_image = torch.from_numpy(np.array(pre_s2_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    post_s2_image = torch.from_numpy(np.array(post_s2_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    pre_label = torch.from_numpy(np.array(pre_label, dtype=np.int8)).unsqueeze(0).to(torch.long)
    post_label = torch.from_numpy(np.array(post_label, dtype=np.int8)).unsqueeze(0).to(torch.long)

    pre_s2_image = torch.where(torch.isnan(pre_s2_image), torch.tensor(0.), pre_s2_image)
    pre_s2_image = (pre_s2_image - config.s2_mean[None, :, None, None]) / config.s2_std[None, :, None, None]
    # print(torch.max(pre_s2_image), torch.min(pre_s2_image))
    # x = input()

    post_s2_image = torch.where(torch.isnan(post_s2_image), torch.tensor(0.), post_s2_image)
    post_s2_image = (post_s2_image - config.s2_mean[None, :, None, None]) / config.s2_std[None, :, None, None]

    return [pre_high_image, pre_s2_image, post_s2_image, pre_label, post_label], datalists, low_time_lists

def cal_f1socre(label, result):
    result = np.where(result>=0.5, 1, 0)
    c = 1
    TP = np.logical_and(result == c, label == c).sum()
    FN = np.logical_and(result != c, label == c).sum()
    FP = np.logical_and(result == c, label != c).sum()
    TN = np.logical_and(result != c, label != c).sum()
    Recall = TP / (TP + FN)
    Precision = TP / (TP + FP)
    F1 = 2 * Recall * Precision / (Precision + Recall)
    return round(F1*100., 2)

def prosess_output(id, output, config, datalists, name, pre_label, post_label, low_time_lists=[]):

    def output2numpy(building, features):
        building = torch.argmax(building, dim=1).cpu().detach().numpy().squeeze()
        features = features
        return building, features
    
    pre_high_building, pre_high_features = output2numpy(output["pre_high_building"], output["pre_high_features"])
    pre_low_building, pre_low_features = output2numpy(output["pre_low_building"], output["pre_low_features"])
    post_low_damage, post_low_features = output2numpy(output["post_low_damage"], output["post_low_features"])

    datalists["pre_high"]["out"] = pre_high_building*255
    datalists["pre_s2"]["out"] = pre_low_building*255
    datalists["post_s2"]["out"] = post_low_damage*255

    datalists["pre_high"]["feature"] = get_tenser_mean(pre_high_features)
    datalists["pre_s2"]["feature"] = get_tenser_mean(pre_low_features)
    datalists["post_s2"]["feature"] = get_tenser_mean(post_low_features)

    pre_high_f1 = cal_f1socre(pre_label.detach().numpy().squeeze(), pre_high_building)
    pre_s2_f1 = cal_f1socre(pre_label.detach().numpy().squeeze(), pre_low_building)
    post_s2_f1 = cal_f1socre(post_label.detach().numpy().squeeze(), post_low_damage)

    pre_high_f1 = cal_f1socre(pre_label.detach().numpy().squeeze(), pre_high_building)
    pre_s2_f1 = cal_f1socre(pre_label.detach().numpy().squeeze(), pre_low_building)
    post_s2_f1 = cal_f1socre(post_label.detach().numpy().squeeze(), post_low_damage)

    # 保存结果至文件夹
    plot_img(datalists, config.plot_save_path, name, f1=[pre_high_f1, pre_high_f1, pre_s2_f1, post_s2_f1])
    # save_to_tif(pre_high_building, save_path=os.path.join(config.pre_high_result_save_path, name), test_image_path=config.pre_high_image_paths[id])
    # save_to_tif(pre_low_building, save_path=os.path.join(config.pre_s2_result_save_path, name), test_image_path=config.pre_high_image_paths[id])
    # save_to_tif(post_low_damage, save_path=os.path.join(config.post_s2_result_save_path, name.replace("_pre_", "_post_")), test_image_path=config.pre_high_image_paths[id])

    out_lists = output["low_feature_lists"]
    feature_lists = []
    for out_id in range(out_lists.shape[1]): #  -1
        feature_lists.append(datalists["images"][out_id])
        img_tensor = out_lists[:, out_id] # torch.sigmoid(out_lists[:, out_id])
        feature = get_tenser_mean(img_tensor)
        # print("feature shape is",datalists[:, i].shape, feature.shape)
        feature_lists.append(feature)

    feature_lists.append(datalists["post_s2"]["image"])
    feature_lists.append(get_tenser_mean(out_lists[:, -1]))
    # print(len(feature_lists), len(low_time_lists))

    feature_lists.append(datalists["pre_high"]["image"])
    feature_lists.append(get_tenser_mean(pre_high_features))
    low_time_lists.append("high")
    low_time_lists.append("high")

    feature_lists.append(datalists["pre_high"]["label"])
    feature_lists.append(datalists["post_s2"]["label"])
    low_time_lists.append("b_label")
    low_time_lists.append("d_label")

    # feature_lists.append(pre_high_building*255)
    feature_lists.append(pre_low_building*255)
    feature_lists.append(post_low_damage*255)
    low_time_lists.append(f"b_f1:{pre_s2_f1}")
    low_time_lists.append(f"d_f1:{post_s2_f1}")
    # print(len(feature_lists), len(low_time_lists))

    # feature_lists.append(get_tenser_mean(output["pre_low_features"]))
    # low_time_lists.append(f"DCN_pre")
    # feature_lists.append(get_tenser_mean(output["post_low_features"]))
    # low_time_lists.append(f"DCN_post")


    plot_feature(feature_lists, config.plot_feature_save_path, name, low_time_lists=low_time_lists, col = 8, row = 6, output=output)

    # # 以灾前最后一帧为主，找出特征差异：
    # out_lists = output["pre_low_feature_lists"]
    # feature_lists = []
    # for out_id in range(out_lists.shape[1]):
    #     pre_high_features = F.interpolate(pre_high_features, size=(48, 48), mode='bilinear', align_corners=True)
    #     img_tensor = torch.sigmoid(out_lists[:, out_id]) - torch.sigmoid(pre_high_features)
    #     # img_tensor = torch.abs(img_tensor)
    #     feature = get_tenser_mean(img_tensor, val_max=1, val_min=-1)
    #     feature_lists.append(feature)
    # feature_lists.append(get_tenser_mean(pre_high_features))
    # feature_lists.append(datalists["pre_high"]["label"])
    # plot_feature(feature_lists, config.plot_save_path, name.replace(".tif", "_feature_diff.tif"), col = 8, row = 3)

    # 可视化 offset
    for point_index in range(9):
        offset_save_path = os.path.join(config.plot_feature_save_path, name.replace(".tif", f"_point_index{point_index}.png"))
        print(offset_save_path)
        visualize_dcn_offset(feature_lists=feature_lists, offset_tensor=output["pre_offset"], point_index=point_index, save_path=offset_save_path)
    # x = input()



def test(config):
    device = torch.device(config.device)
    net = load_net(config.model, device, config.model_path)
    print(f"test datasets size: {len(config.pre_high_image_paths)}")
    
    for id, image_high_path in tqdm(enumerate(config.pre_high_image_paths)):
        datalists = {"pre_high": {}, "post_high": {}, "pre_s2": {}, "post_s2": {}, "images": []}
        name = os.path.basename(image_high_path).replace(".png", ".tif")
        inputs, datalists, low_time_lists = load_data(id, config, name, datalists)

        with torch.no_grad():
            output = net(inputs, device)
            # visualize_dcn_offset(output["pre_offset"])
            # x = input()

        prosess_output(id, output, config, datalists, name, inputs[-2], inputs[-1], low_time_lists)


def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument
    # arg("-c", "--config_path", default="./config/santa_s2_LBD.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/santa_s2_LBD2eaton.py", type=Path, help="Path to the config.", required=False)
    arg("-c", "--config_path", default="./config/santa_s2_LBDv22.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/UKRxBD_HRNet3.py", type=Path, help="Path to the config.", required=False)
    return parser.parse_args()

if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '1'
    args = get_args()
    config = py2cfg(args.config_path)
    test(config)

