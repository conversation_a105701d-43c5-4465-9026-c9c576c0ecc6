"""
Building Segmentation RDDM Denoiser - 基于CVPR2024 RDDM的建筑物分割去噪模型
仿照l2h_diffusers.py的风格，专门用于从低分分割结果中去除噪声

主要功能：
- 输入：低质量建筑物分割结果（边缘圆滑、有噪声）
- 输出：高质量建筑物分割结果（边缘清晰）
- 核心：使用RDDM的残差扩散思想，学习残差映射

参数放置在函数入口，无脚本文件
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from einops import rearrange
from functools import partial
from tqdm import tqdm
import os


def calculate_f1_score(pred_seg, target_seg, threshold=0.5):
    """
    计算F1分数
    Args:
        pred_seg: 预测分割结果 [B, 2, H, W] (前景, 背景)
        target_seg: 目标分割结果 [B, 2, H, W] (前景, 背景)
        threshold: 二值化阈值
    Returns:
        f1_score: F1分数
        precision: 精确率
        recall: 召回率
    """
    # 取前景通道并二值化
    pred_fg = (pred_seg[:, 0:1] > threshold).float()  # [B, 1, H, W]
    target_fg = (target_seg[:, 0:1] > threshold).float()  # [B, 1, H, W]

    # 计算TP, FP, FN
    tp = (pred_fg * target_fg).sum()
    fp = (pred_fg * (1 - target_fg)).sum()
    fn = ((1 - pred_fg) * target_fg).sum()

    # 计算精确率和召回率
    precision = tp / (tp + fp + 1e-8)
    recall = tp / (tp + fn + 1e-8)

    # 计算F1分数
    f1_score = 2 * precision * recall / (precision + recall + 1e-8)

    return f1_score.item(), precision.item(), recall.item()


def softmax_to_binary(seg_2ch):
    """
    将2通道分割结果转换为二值分割
    Args:
        seg_2ch: [B, 2, H, W] 前景和背景概率
    Returns:
        binary_seg: [B, 1, H, W] 二值分割（前景）
    """
    # Softmax归一化
    seg_prob = F.softmax(seg_2ch, dim=1)
    # 取前景通道
    fg_prob = seg_prob[:, 0:1]  # [B, 1, H, W]
    return fg_prob


def extract(a, t, x_shape):
    """从系数数组中提取对应时间步的值"""
    batch_size = t.shape[0]
    out = a.gather(-1, t.cpu())
    return out.reshape(batch_size, *((1,) * (len(x_shape) - 1))).to(t.device)


def gen_coefficients(timesteps, schedule="decreased", sum_scale=1.0):
    """生成RDDM的系数"""
    if schedule == "decreased":
        # alpha系数递减
        alphas = torch.linspace(1.0, 0.1, timesteps)
    elif schedule == "increased":
        # beta系数递增
        betas = torch.linspace(0.0001, 0.02, timesteps)
        return betas
    else:
        raise ValueError(f"Unknown schedule: {schedule}")
    
    # 归一化
    alphas = alphas / alphas.sum() * sum_scale
    return alphas


class ResBlock(nn.Module):
    """残差块"""
    def __init__(self, dim, dim_out=None, groups=8):
        super().__init__()
        dim_out = dim_out or dim

        # 自动调整groups以适应小维度
        groups = min(groups, dim, dim_out)
        groups = max(groups, 1)  # 至少为1

        self.block1 = nn.Sequential(
            nn.GroupNorm(groups, dim),
            nn.SiLU(),
            nn.Conv2d(dim, dim_out, 3, padding=1)
        )

        self.block2 = nn.Sequential(
            nn.GroupNorm(groups, dim_out),
            nn.SiLU(),
            nn.Conv2d(dim_out, dim_out, 3, padding=1)
        )
        
        self.res_conv = nn.Conv2d(dim, dim_out, 1) if dim != dim_out else nn.Identity()
    
    def forward(self, x):
        h = self.block1(x)
        h = self.block2(h)
        return h + self.res_conv(x)


class Attention(nn.Module):
    """简单的自注意力机制"""
    def __init__(self, dim, heads=4, dim_head=32):
        super().__init__()
        self.scale = dim_head ** -0.5
        self.heads = heads
        hidden_dim = dim_head * heads
        
        self.to_qkv = nn.Conv2d(dim, hidden_dim * 3, 1, bias=False)
        self.to_out = nn.Conv2d(hidden_dim, dim, 1)
    
    def forward(self, x):
        _, _, h, w = x.shape
        qkv = self.to_qkv(x).chunk(3, dim=1)
        q, k, v = map(lambda t: rearrange(t, 'b (h c) x y -> b h c (x y)', h=self.heads), qkv)
        
        q = q * self.scale
        sim = torch.einsum('b h d i, b h d j -> b h i j', q, k)
        sim = sim - sim.amax(dim=-1, keepdim=True).detach()
        attn = sim.softmax(dim=-1)
        
        out = torch.einsum('b h i j, b h d j -> b h i d', attn, v)
        out = rearrange(out, 'b h (x y) d -> b (h d) x y', x=h, y=w)
        return self.to_out(out)


class TimeEmbedding(nn.Module):
    """时间步嵌入"""
    def __init__(self, dim):
        super().__init__()
        self.dim = dim
        
    def forward(self, time):
        device = time.device
        half_dim = self.dim // 2
        embeddings = math.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((embeddings.sin(), embeddings.cos()), dim=-1)
        return embeddings


class UNetRDDM(nn.Module):
    """基于RDDM的U-Net模型，用于建筑物分割去噪"""

    def __init__(
        self,
        dim=64,
        channels=2,  # 输入为B*2*H*W（低分+高分分割）
        dim_mults=(1, 2, 4, 8),
        resnet_block_groups=None,  # 自动计算
        condition=True,  # 启用条件输入
        self_condition=False
    ):
        super().__init__()
        
        self.channels = channels
        self.condition = condition
        self.self_condition = self_condition
        
        # 确定维度
        input_channels = channels
        if condition:
            input_channels += channels  # 条件输入（低质量分割）
        if self_condition:
            input_channels += channels  # 自条件

        # 自动计算合适的groups数量
        if resnet_block_groups is None:
            resnet_block_groups = min(8, dim)  # 确保groups不超过最小维度

        init_dim = dim
        dims = [init_dim, *map(lambda m: dim * m, dim_mults)]
        in_out = list(zip(dims[:-1], dims[1:]))

        # 时间嵌入 - 简化
        time_dim = max(dim * 2, 8)  # 确保时间嵌入维度不会太小
        self.time_mlp = nn.Sequential(
            TimeEmbedding(dim),
            nn.Linear(dim, time_dim),
            nn.GELU()
        )
        
        # 初始卷积
        self.init_conv = nn.Conv2d(input_channels, init_dim, 7, padding=3)

        # 添加pixel_unshuffle进行4倍下采样，减少计算复杂度
        self.pixel_unshuffle = nn.PixelUnshuffle(4)  # 4倍下采样: H/4, W/4, C*16
        # pixel_unshuffle后通道数会变成 init_dim * 16 (4^2 = 16)
        self.post_unshuffle_conv = nn.Conv2d(init_dim * 16, init_dim, 3, padding=1)

        # 条件输入也需要相同的下采样处理
        if condition:
            self.condition_unshuffle = nn.PixelUnshuffle(4)
            self.condition_post_unshuffle_conv = nn.Conv2d(channels * 16, channels, 3, padding=1)
        
        # 下采样层
        self.downs = nn.ModuleList([])
        num_resolutions = len(in_out)
        
        for ind, (dim_in, dim_out) in enumerate(in_out):
            is_last = ind >= (num_resolutions - 1)
            
            self.downs.append(nn.ModuleList([
                ResBlock(dim_in, dim_in, groups=resnet_block_groups),
                nn.Identity(),  # 移除第二个ResBlock以减少参数
                Attention(dim_in) if dim_in <= 64 else nn.Identity(),  # 只在很小维度使用注意力
                nn.Conv2d(dim_in, dim_out, 4, 2, 1) if not is_last else nn.Conv2d(dim_in, dim_out, 3, padding=1)
            ]))
        
        # 中间层
        mid_dim = dims[-1]
        self.mid_block1 = ResBlock(mid_dim, mid_dim, groups=resnet_block_groups)
        self.mid_attn = nn.Identity()  # 移除中间注意力以减少计算
        self.mid_block2 = ResBlock(mid_dim, mid_dim, groups=resnet_block_groups)
        
        # 上采样层
        self.ups = nn.ModuleList([])
        
        for ind, (dim_in, dim_out) in enumerate(reversed(in_out)):
            is_last = ind == (len(in_out) - 1)
            
            self.ups.append(nn.ModuleList([
                ResBlock(dim_out + dim_in, dim_out, groups=resnet_block_groups),
                nn.Identity(),  # 移除第二个ResBlock以减少参数
                Attention(dim_out) if dim_out <= 64 else nn.Identity(),  # 只在很小维度使用注意力
                nn.ConvTranspose2d(dim_out, dim_in, 4, 2, 1) if not is_last else nn.Conv2d(dim_out, dim_in, 3, padding=1)
            ]))
        
        # 输出层 - 同时预测残差和噪声
        self.out_dim = channels * 2  # 2*inchannels: 残差 + 噪声
        self.final_res_block = ResBlock(dim * 2, dim, groups=resnet_block_groups)
        self.final_conv = nn.Conv2d(dim, self.out_dim, 1)

        # 添加pixel_shuffle进行4倍上采样，恢复原始分辨率
        # 需要先调整通道数以匹配pixel_shuffle的要求
        self.pre_shuffle_conv = nn.Conv2d(self.out_dim, self.out_dim * 16, 3, padding=1)
        self.pixel_shuffle = nn.PixelShuffle(4)  # 4倍上采样: H*4, W*4, C/16
    
    def forward(self, x, time, x_self_cond=None, x_condition=None):
        """
        x: 噪声分割图 [B, 1, H, W]
        time: 时间步 [B]
        x_self_cond: 自条件（可选）
        x_condition: 条件输入（低质量分割）[B, 1, H, W]
        """
        
        # 准备输入
        if self.self_condition:
            x_self_cond = x_self_cond if x_self_cond is not None else torch.zeros_like(x)
            x = torch.cat((x_self_cond, x), dim=1)
        
        # 处理条件输入 - 也需要进行下采样
        if self.condition and x_condition is not None:
            # 对条件输入进行相同的下采样处理
            x_condition_downsampled = self.condition_unshuffle(x_condition)
            x_condition_downsampled = self.condition_post_unshuffle_conv(x_condition_downsampled)
            x = torch.cat((x, x_condition_downsampled), dim=1)
        
        # 初始卷积
        x = self.init_conv(x)

        # 使用pixel_unshuffle进行4倍下采样，减少计算复杂度
        # 原始: [B, C, H, W] -> [B, C*16, H/4, W/4]
        x = self.pixel_unshuffle(x)
        x = self.post_unshuffle_conv(x)  # 调整通道数回到原来的维度

        r = x.clone()
        
        # 时间嵌入
        _ = self.time_mlp(time)  # 时间嵌入暂未使用，但保留接口
        
        h = []
        
        # 下采样
        for block1, block2, attn, downsample in self.downs:
            x = block1(x)
            h.append(x)
            
            x = block2(x)
            x = attn(x)
            h.append(x)
            
            x = downsample(x)
        
        # 中间层
        x = self.mid_block1(x)
        x = self.mid_attn(x)
        x = self.mid_block2(x)
        
        # 上采样
        for block1, block2, attn, upsample in self.ups:
            x = torch.cat((x, h.pop()), dim=1)
            x = block1(x)
            
            x = torch.cat((x, h.pop()), dim=1)
            x = block2(x)
            x = attn(x)
            
            x = upsample(x)
        
        # 输出 - 同时预测残差和噪声
        x = torch.cat((x, r), dim=1)
        x = self.final_res_block(x)
        output = self.final_conv(x)  # [B, 2*C, H/4, W/4]

        # 使用pixel_shuffle恢复原始分辨率
        # 先调整通道数以匹配pixel_shuffle的要求
        output = self.pre_shuffle_conv(output)  # [B, 2*C*16, H/4, W/4]
        output = self.pixel_shuffle(output)     # [B, 2*C, H, W] 恢复原始分辨率

        # 分离残差和噪声
        residual, noise = output.chunk(2, dim=1)  # 各自 [B, C, H, W]
        return residual, noise


class BuildingSegmentationRDDM(nn.Module):
    """建筑物分割RDDM去噪器 - 仿照l2h_diffusers.py的风格"""
    
    def __init__(
        self,
        unet_dim=32,  # 减少基础维度
        unet_dim_mults=(1, 2, 4),  # 减少层数
        channels=2,  # B*2*H*W: 前景和背景两个通道
        timesteps=1000,
        sampling_timesteps=250,
        loss_type='l1',
        objective='pred_res_noise',  # RDDM的核心：预测残差和噪声
        sum_scale=0.01,  # alpha系数的缩放
        beta_scale=0.001  # beta系数很小，适合图像恢复任务
    ):
        super().__init__()
        
        # UNet模型：预测残差
        self.unet = UNetRDDM(
            dim=unet_dim,
            channels=channels,
            dim_mults=unet_dim_mults,
            condition=True  # 启用条件输入
        )
        
        # RDDM参数
        self.channels = channels
        self.num_timesteps = timesteps
        self.sampling_timesteps = sampling_timesteps
        self.loss_type = loss_type
        self.objective = objective
        self.sum_scale = sum_scale
        self.beta_scale = beta_scale

        # 生成RDDM系数 - 按照 I_T = I_IN + alpha*I_RES + beta*噪声
        alphas = gen_coefficients(timesteps, schedule="decreased")
        alphas_cumsum = alphas.cumsum(dim=0).clamp(0, 1) * sum_scale  # alpha系数
        alphas_cumsum_prev = F.pad(alphas_cumsum[:-1], (1, 0), value=1.)

        # beta系数设置得很小，适合图像恢复任务
        betas = torch.linspace(0.0001, 0.02, timesteps) * beta_scale
        betas_cumsum = betas.cumsum(dim=0).clamp(0, 1)
        
        # 注册为buffer
        self.register_buffer('alphas', alphas)
        self.register_buffer('alphas_cumsum', alphas_cumsum)
        self.register_buffer('alphas_cumsum_prev', alphas_cumsum_prev)
        self.register_buffer('betas', betas)
        self.register_buffer('betas_cumsum', betas_cumsum)

        # 后验分布参数（简化）
        posterior_variance = betas * 0.1  # 简化的后验方差
        posterior_variance[0] = 0
        self.register_buffer('posterior_variance', posterior_variance)
    
    def predict_start_from_res_noise(self, x_t, t, x_res, noise):
        """RDDM核心：从残差和噪声预测原始图像
        根据RDDM公式: x_t = x_input + alpha_cumsum * x_res + beta_cumsum * noise
        反推: x_start = x_t - alpha_cumsum * x_res - beta_cumsum * noise
        """
        return (
            x_t -
            extract(self.alphas_cumsum, t, x_t.shape) * x_res -
            extract(self.betas_cumsum, t, x_t.shape) * noise
        )

    def q_sample(self, x_start, x_condition, t, noise=None):
        """
        RDDM前向过程：x_t = x_input + alpha_cumsum * x_res + beta_cumsum * noise

        这是RDDM的核心创新：
        - x_input: 输入的低质量图像（条件）
        - x_res: 残差 = x_start - x_input（需要学习的部分）
        - noise: 随机噪声（beta系数很小，适合图像恢复）
        - alpha_cumsum: 残差的累积系数（主要信号）
        - beta_cumsum: 噪声的累积系数（辅助信号，很小）
        """
        if noise is None:
            noise = torch.randn_like(x_start)

        # 计算真实残差: x_res = x_start - x_input
        x_res = x_start - x_condition

        # RDDM前向过程: x_t = x_input + alpha_cumsum * x_res + beta_cumsum * noise
        alpha_cumsum = extract(self.alphas_cumsum, t, x_start.shape)
        beta_cumsum = extract(self.betas_cumsum, t, x_start.shape)

        x_noisy = (
            x_condition +  # x_input (低质量输入)
            alpha_cumsum * x_res +  # alpha_cumsum * x_res (主要信号)
            beta_cumsum * noise  # beta_cumsum * noise (辅助噪声，很小)
        )

        return x_noisy, x_res, noise
    
    def forward(self, high_seg, low_seg, timesteps=None):
        """
        训练前向传播
        high_seg: [B, 1, H, W] 高质量分割（目标）
        low_seg: [B, 1, H, W] 低质量分割（条件）
        timesteps: [B] 时间步（可选）
        """
        B = high_seg.shape[0]
        device = high_seg.device
        
        # 随机采样时间步
        if timesteps is None:
            timesteps = torch.randint(0, self.num_timesteps, (B,), device=device).long()
        
        # 前向扩散过程
        x_noisy, x_res_true, noise_true = self.q_sample(high_seg, low_seg, timesteps)
        
        # UNet预测 - 同时预测残差和噪声
        pred_res, pred_noise = self.unet(x_noisy, timesteps, x_condition=low_seg)

        # 计算损失
        if self.loss_type == 'l1':
            loss_res = F.l1_loss(pred_res, x_res_true)
            loss_noise = F.l1_loss(pred_noise, noise_true)
        elif self.loss_type == 'l2':
            loss_res = F.mse_loss(pred_res, x_res_true)
            loss_noise = F.mse_loss(pred_noise, noise_true)
        else:
            raise ValueError(f'unknown loss type {self.loss_type}')

        # 总损失：残差损失 + 噪声损失（权重较小）
        loss = loss_res + 0.1 * loss_noise  # 噪声损失权重较小

        # 计算恢复结果用于评估 - 使用RDDM公式
        pred_x_start = self.predict_start_from_res_noise(x_noisy, timesteps, pred_res, pred_noise)

        return loss, pred_x_start

    @torch.no_grad()
    def p_sample(self, x, t, x_condition, clip_denoised=True):
        """RDDM单步去噪采样"""
        b, *_, device = *x.shape, x.device
        batched_times = torch.full((b,), t, device=device, dtype=torch.long)

        # UNet预测 - 同时预测残差和噪声
        pred_res, pred_noise = self.unet(x, batched_times, x_condition=x_condition)

        # 预测原始图像: 使用RDDM公式反推
        pred_x_start = self.predict_start_from_res_noise(x, batched_times, pred_res, pred_noise)

        if clip_denoised:
            pred_x_start = pred_x_start.clamp(0., 1.)

        # 计算后验均值
        if t == 0:
            return pred_x_start
        else:
            # 简化的后验采样 - 逐步去噪
            alpha_cumsum = extract(self.alphas_cumsum, batched_times, x.shape)
            beta_cumsum = extract(self.betas_cumsum, batched_times, x.shape)

            # 计算前一步的值: I_T-1 = I_IN + alpha_prev * I_RES + beta_prev * 噪声
            # 这里简化为逐步减少噪声
            noise_factor = 0.8  # 逐步减少噪声
            pred_x_prev = (
                x_condition +
                alpha_cumsum * pred_res * noise_factor +
                beta_cumsum * pred_noise * noise_factor
            )

            return pred_x_prev

    @torch.no_grad()
    def p_sample_loop(self, low_seg, shape=None, return_all_timesteps=False, start_from_noise=False):
        """RDDM完整采样循环 - 图像恢复任务可以从输入开始而非纯噪声"""
        device = low_seg.device

        if shape is None:
            shape = low_seg.shape

        if start_from_noise:
            # 传统扩散：从纯噪声开始
            img = torch.randn(shape, device=device)
        else:
            # 图像恢复：从低质量输入开始，添加少量噪声
            img = low_seg + torch.randn_like(low_seg) * 0.1  # 添加少量噪声

        imgs = [img]

        # 采样时间步 - 图像恢复可以用更少的步数
        timesteps = torch.linspace(self.num_timesteps - 1, 0, self.sampling_timesteps).long()

        for t in tqdm(timesteps, desc='RDDM denoising step', total=self.sampling_timesteps):
            img = self.p_sample(img, t, low_seg)
            imgs.append(img)

        ret = img if not return_all_timesteps else imgs
        return ret

    @torch.no_grad()
    def sample(self, low_seg, return_all_timesteps=False, start_from_noise=False):
        """采样接口"""
        return self.p_sample_loop(low_seg, low_seg.shape, return_all_timesteps, start_from_noise)

    @torch.no_grad()
    def enhance_segmentation(self, low_seg, start_from_noise=False):
        """
        增强分割结果的主要接口
        Args:
            low_seg: 低质量分割输入 [B, 2, H, W]
            start_from_noise: 是否从纯噪声开始（False表示从输入开始，适合图像恢复）
        """
        return self.sample(low_seg, return_all_timesteps=False, start_from_noise=start_from_noise)

    @torch.no_grad()
    def direct_enhance(self, low_seg, num_steps=50):
        """
        直接增强方法 - 适合图像恢复，不需要完整的扩散过程
        Args:
            low_seg: 低质量分割输入 [B, 2, H, W]
            num_steps: 迭代步数（比完整扩散少得多）
        """
        device = low_seg.device
        x = low_seg.clone()

        # 使用较少的时间步进行快速恢复
        timesteps = torch.linspace(self.num_timesteps // 4, 0, num_steps).long()

        for i, t in enumerate(timesteps):
            batched_times = torch.full((x.shape[0],), t, device=device, dtype=torch.long)

            # 预测残差和噪声
            pred_res, pred_noise = self.unet(x, batched_times, x_condition=low_seg)

            # 直接应用残差修正
            alpha = 0.1 * (1 - i / num_steps)  # 逐渐减小修正强度
            x = x + alpha * pred_res

            # 确保概率和为1（对于2通道分割）
            x = F.softmax(x, dim=1)

        return x


def train_building_rddm(
    model,
    train_dataloader,
    val_dataloader=None,
    num_epochs=100,
    learning_rate=1e-4,
    device='cuda',
    save_every=10,
    save_path='./checkpoints'
):
    """
    训练Building RDDM模型

    Args:
        model: BuildingSegmentationRDDM模型
        train_dataloader: 训练数据加载器，返回(high_seg, low_seg)
        val_dataloader: 验证数据加载器（可选）
        num_epochs: 训练轮数
        learning_rate: 学习率
        device: 设备
        save_every: 保存间隔
        save_path: 保存路径
    """
    import os
    from torch.optim import Adam

    os.makedirs(save_path, exist_ok=True)

    model = model.to(device)
    optimizer = Adam(model.parameters(), lr=learning_rate)

    best_f1 = 0.0  # 跟踪最佳F1分数

    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_f1 = 0.0

        pbar = tqdm(train_dataloader, desc=f'Epoch {epoch+1}/{num_epochs}')
        for _, (high_seg, low_seg) in enumerate(pbar):
            high_seg = high_seg.to(device)
            low_seg = low_seg.to(device)

            optimizer.zero_grad()

            # 前向传播
            outputs = model(high_seg, low_seg)
            loss = outputs[0]  # 第一个输出是loss
            pred_x_start = outputs[-1]  # 最后一个输出是恢复结果

            # 计算F1分数（每个batch）
            f1_score, precision, recall = calculate_f1_score(pred_x_start, high_seg)

            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()

            train_loss += loss.item()
            train_f1 += f1_score
            pbar.set_postfix({
                'loss': loss.item(),
                'f1': f1_score,
                'prec': precision,
                'rec': recall
            })

        train_loss /= len(train_dataloader)
        train_f1 /= len(train_dataloader)

        # 验证阶段
        if val_dataloader is not None:
            model.eval()
            val_loss = 0.0
            val_f1 = 0.0

            with torch.no_grad():
                for high_seg, low_seg in val_dataloader:
                    high_seg = high_seg.to(device)
                    low_seg = low_seg.to(device)

                    outputs = model(high_seg, low_seg)
                    loss = outputs[0]
                    pred_x_start = outputs[-1]

                    # 计算验证F1分数
                    f1_score, _, _ = calculate_f1_score(pred_x_start, high_seg)

                    val_loss += loss.item()
                    val_f1 += f1_score

            val_loss /= len(val_dataloader)
            val_f1 /= len(val_dataloader)
            print(f'Epoch {epoch+1}: Train Loss: {train_loss:.6f}, Train F1: {train_f1:.4f}, Val Loss: {val_loss:.6f}, Val F1: {val_f1:.4f}')

            # 保存最佳模型（基于F1分数）
            if val_f1 > best_f1:
                best_f1 = val_f1
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'loss': val_loss,
                    'f1_score': val_f1
                }, os.path.join(save_path, 'best_model.pt'))
        else:
            print(f'Epoch {epoch+1}: Train Loss: {train_loss:.6f}, Train F1: {train_f1:.4f}')

        # 定期保存
        if (epoch + 1) % save_every == 0:
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'loss': train_loss
            }, os.path.join(save_path, f'model_epoch_{epoch+1}.pt'))


def inference_building_rddm(
    model,
    low_seg_input,
    high_seg_target=None,
    checkpoint_path=None,
    device='cuda',
    mode='direct'  # 'direct' 或 'full_diffusion'
):
    """
    Building RDDM推理

    Args:
        model: BuildingSegmentationRDDM模型
        low_seg_input: 低质量分割输入 [B, 2, H, W] 或 [2, H, W]
        high_seg_target: 高质量分割目标 [B, 2, H, W]（用于评估F1分数，可选）
        checkpoint_path: 模型权重路径（可选）
        device: 设备

    Returns:
        enhanced_seg: 增强后的分割结果
        f1_metrics: F1分数指标（如果提供了target）
    """
    model = model.to(device)

    # 加载权重
    if checkpoint_path is not None:
        checkpoint = torch.load(checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Loaded model from {checkpoint_path}")
        if 'f1_score' in checkpoint:
            print(f"Model F1 score: {checkpoint['f1_score']:.4f}")

    model.eval()

    # 处理输入维度
    if len(low_seg_input.shape) == 3:
        low_seg_input = low_seg_input.unsqueeze(0)

    low_seg_input = low_seg_input.to(device)

    # 推理 - 根据模式选择不同的方法
    with torch.no_grad():
        if mode == 'direct':
            # 直接增强模式 - 适合图像恢复，速度快
            enhanced_seg = model.direct_enhance(low_seg_input, num_steps=50)
            print("使用直接增强模式（图像恢复优化）")
        elif mode == 'full_diffusion':
            # 完整扩散模式 - 从输入开始而非纯噪声
            enhanced_seg = model.enhance_segmentation(low_seg_input, start_from_noise=False)
            print("使用完整扩散模式（从输入开始）")
        elif mode == 'noise_diffusion':
            # 从纯噪声开始的传统扩散
            enhanced_seg = model.enhance_segmentation(low_seg_input, start_from_noise=True)
            print("使用传统扩散模式（从纯噪声开始）")
        else:
            raise ValueError(f"未知模式: {mode}")

        # 计算F1分数（如果提供了目标）
        f1_metrics = None
        if high_seg_target is not None:
            if len(high_seg_target.shape) == 3:
                high_seg_target = high_seg_target.unsqueeze(0)
            high_seg_target = high_seg_target.to(device)

            # 计算低分辨率vs高分辨率的F1分数
            f1_low, prec_low, rec_low = calculate_f1_score(low_seg_input, high_seg_target)
            # 计算增强结果vs高分辨率的F1分数
            f1_enhanced, prec_enhanced, rec_enhanced = calculate_f1_score(enhanced_seg, high_seg_target)

            f1_metrics = {
                'low_res_f1': f1_low,
                'low_res_precision': prec_low,
                'low_res_recall': rec_low,
                'enhanced_f1': f1_enhanced,
                'enhanced_precision': prec_enhanced,
                'enhanced_recall': rec_enhanced,
                'f1_improvement': f1_enhanced - f1_low
            }

            print(f"低分辨率 F1: {f1_low:.4f}, 增强后 F1: {f1_enhanced:.4f}, 提升: {f1_enhanced - f1_low:.4f}")

    return enhanced_seg, f1_metrics


# 使用示例
if __name__ == "__main__":
    # 创建轻量化模型
    model = BuildingSegmentationRDDM(
        unet_dim=32,  # 减少基础维度
        unet_dim_mults=(1, 2, 4),  # 减少层数
        channels=2,  # B*2*H*W: 前景和背景
        timesteps=500,  # 减少时间步
        sampling_timesteps=50,  # 减少采样步数
        objective='pred_res_noise'
    )

    # 模拟数据 - 使用更小的尺寸进行测试
    batch_size = 1  # 减少batch size
    height, width = 128, 128  # 减少图像尺寸

    # 模拟低质量和高质量分割 - B*2*H*W格式
    # 前景和背景概率，确保和为1
    low_seg_fg = torch.rand(batch_size, 1, height, width) * 0.6 + 0.2  # 前景概率
    low_seg_bg = 1.0 - low_seg_fg  # 背景概率
    low_seg = torch.cat([low_seg_fg, low_seg_bg], dim=1)  # [B, 2, H, W]

    high_seg_fg = torch.rand(batch_size, 1, height, width) * 0.8 + 0.1  # 前景概率
    high_seg_bg = 1.0 - high_seg_fg  # 背景概率
    high_seg = torch.cat([high_seg_fg, high_seg_bg], dim=1)  # [B, 2, H, W]

    print("Building Segmentation RDDM - 测试")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 测试训练前向传播
    model.train()
    outputs = model(high_seg, low_seg)
    loss = outputs[0]
    pred_x_start = outputs[-1]
    print(f"训练损失: {loss.item():.6f}")
    print(f"恢复结果形状: {pred_x_start.shape}")

    # 计算F1分数
    f1_score, precision, recall = calculate_f1_score(pred_x_start, high_seg)
    print(f"训练F1分数: {f1_score:.4f}, 精确率: {precision:.4f}, 召回率: {recall:.4f}")

    # 测试不同推理模式
    model.eval()

    print("\n=== 测试直接增强模式（推荐用于图像恢复）===")
    enhanced_seg_direct, f1_metrics_direct = inference_building_rddm(
        model, low_seg, high_seg_target=high_seg, device='cpu', mode='direct'
    )
    print(f"直接增强结果形状: {enhanced_seg_direct.shape}")
    print(f"直接增强结果范围: [{enhanced_seg_direct.min():.3f}, {enhanced_seg_direct.max():.3f}]")

    print("\n=== 测试完整扩散模式（从输入开始）===")
    enhanced_seg_full, f1_metrics_full = inference_building_rddm(
        model, low_seg, high_seg_target=high_seg, device='cpu', mode='full_diffusion'
    )
    print(f"完整扩散结果形状: {enhanced_seg_full.shape}")

    # 显示F1分数比较
    if f1_metrics_direct and f1_metrics_full:
        print(f"\n=== F1分数比较 ===")
        print(f"低分辨率F1: {f1_metrics_direct['low_res_f1']:.4f}")
        print(f"直接增强F1: {f1_metrics_direct['enhanced_f1']:.4f} (提升: {f1_metrics_direct['f1_improvement']:.4f})")
        print(f"完整扩散F1: {f1_metrics_full['enhanced_f1']:.4f} (提升: {f1_metrics_full['f1_improvement']:.4f})")

    print("\n✓ 所有测试通过！支持多种推理模式，直接增强模式最适合图像恢复任务")
