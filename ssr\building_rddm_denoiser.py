"""
Building Segmentation RDDM Denoiser - 基于CVPR2024 RDDM的建筑物分割去噪模型
仿照l2h_diffusers.py的风格，专门用于从低分分割结果中去除噪声

主要功能：
- 输入：低质量建筑物分割结果（边缘圆滑、有噪声）
- 输出：高质量建筑物分割结果（边缘清晰）
- 核心：使用RDDM的残差扩散思想，学习残差映射

参数放置在函数入口，无脚本文件
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from einops import rearrange
from functools import partial
from tqdm import tqdm
import os


def extract(a, t, x_shape):
    """从系数数组中提取对应时间步的值"""
    batch_size = t.shape[0]
    out = a.gather(-1, t.cpu())
    return out.reshape(batch_size, *((1,) * (len(x_shape) - 1))).to(t.device)


def gen_coefficients(timesteps, schedule="decreased", sum_scale=1.0):
    """生成RDDM的系数"""
    if schedule == "decreased":
        # alpha系数递减
        alphas = torch.linspace(1.0, 0.1, timesteps)
    elif schedule == "increased":
        # beta系数递增
        betas = torch.linspace(0.0001, 0.02, timesteps)
        return betas
    else:
        raise ValueError(f"Unknown schedule: {schedule}")
    
    # 归一化
    alphas = alphas / alphas.sum() * sum_scale
    return alphas


class ResBlock(nn.Module):
    """残差块"""
    def __init__(self, dim, dim_out=None, groups=8):
        super().__init__()
        dim_out = dim_out or dim
        
        self.block1 = nn.Sequential(
            nn.GroupNorm(groups, dim),
            nn.SiLU(),
            nn.Conv2d(dim, dim_out, 3, padding=1)
        )
        
        self.block2 = nn.Sequential(
            nn.GroupNorm(groups, dim_out),
            nn.SiLU(),
            nn.Conv2d(dim_out, dim_out, 3, padding=1)
        )
        
        self.res_conv = nn.Conv2d(dim, dim_out, 1) if dim != dim_out else nn.Identity()
    
    def forward(self, x):
        h = self.block1(x)
        h = self.block2(h)
        return h + self.res_conv(x)


class Attention(nn.Module):
    """简单的自注意力机制"""
    def __init__(self, dim, heads=4, dim_head=32):
        super().__init__()
        self.scale = dim_head ** -0.5
        self.heads = heads
        hidden_dim = dim_head * heads
        
        self.to_qkv = nn.Conv2d(dim, hidden_dim * 3, 1, bias=False)
        self.to_out = nn.Conv2d(hidden_dim, dim, 1)
    
    def forward(self, x):
        _, _, h, w = x.shape
        qkv = self.to_qkv(x).chunk(3, dim=1)
        q, k, v = map(lambda t: rearrange(t, 'b (h c) x y -> b h c (x y)', h=self.heads), qkv)
        
        q = q * self.scale
        sim = torch.einsum('b h d i, b h d j -> b h i j', q, k)
        sim = sim - sim.amax(dim=-1, keepdim=True).detach()
        attn = sim.softmax(dim=-1)
        
        out = torch.einsum('b h i j, b h d j -> b h i d', attn, v)
        out = rearrange(out, 'b h (x y) d -> b (h d) x y', x=h, y=w)
        return self.to_out(out)


class TimeEmbedding(nn.Module):
    """时间步嵌入"""
    def __init__(self, dim):
        super().__init__()
        self.dim = dim
        
    def forward(self, time):
        device = time.device
        half_dim = self.dim // 2
        embeddings = math.log(10000) / (half_dim - 1)
        embeddings = torch.exp(torch.arange(half_dim, device=device) * -embeddings)
        embeddings = time[:, None] * embeddings[None, :]
        embeddings = torch.cat((embeddings.sin(), embeddings.cos()), dim=-1)
        return embeddings


class UNetRDDM(nn.Module):
    """基于RDDM的U-Net模型，用于建筑物分割去噪"""
    
    def __init__(
        self,
        dim=64,
        channels=1,  # 分割图为单通道
        dim_mults=(1, 2, 4, 8),
        resnet_block_groups=8,
        condition=True,  # 启用条件输入
        self_condition=False
    ):
        super().__init__()
        
        self.channels = channels
        self.condition = condition
        self.self_condition = self_condition
        
        # 确定维度
        input_channels = channels
        if condition:
            input_channels += channels  # 条件输入（低质量分割）
        if self_condition:
            input_channels += channels  # 自条件
            
        init_dim = dim
        dims = [init_dim, *map(lambda m: dim * m, dim_mults)]
        in_out = list(zip(dims[:-1], dims[1:]))
        
        # 时间嵌入
        time_dim = dim * 4
        self.time_mlp = nn.Sequential(
            TimeEmbedding(dim),
            nn.Linear(dim, time_dim),
            nn.GELU(),
            nn.Linear(time_dim, time_dim)
        )
        
        # 初始卷积
        self.init_conv = nn.Conv2d(input_channels, init_dim, 7, padding=3)
        
        # 下采样层
        self.downs = nn.ModuleList([])
        num_resolutions = len(in_out)
        
        for ind, (dim_in, dim_out) in enumerate(in_out):
            is_last = ind >= (num_resolutions - 1)
            
            self.downs.append(nn.ModuleList([
                ResBlock(dim_in, dim_in, groups=resnet_block_groups),
                ResBlock(dim_in, dim_in, groups=resnet_block_groups),
                Attention(dim_in) if dim_in < 512 else nn.Identity(),
                nn.Conv2d(dim_in, dim_out, 4, 2, 1) if not is_last else nn.Conv2d(dim_in, dim_out, 3, padding=1)
            ]))
        
        # 中间层
        mid_dim = dims[-1]
        self.mid_block1 = ResBlock(mid_dim, mid_dim, groups=resnet_block_groups)
        self.mid_attn = Attention(mid_dim)
        self.mid_block2 = ResBlock(mid_dim, mid_dim, groups=resnet_block_groups)
        
        # 上采样层
        self.ups = nn.ModuleList([])
        
        for ind, (dim_in, dim_out) in enumerate(reversed(in_out)):
            is_last = ind == (len(in_out) - 1)
            
            self.ups.append(nn.ModuleList([
                ResBlock(dim_out + dim_in, dim_out, groups=resnet_block_groups),
                ResBlock(dim_out + dim_in, dim_out, groups=resnet_block_groups),
                Attention(dim_out) if dim_out < 512 else nn.Identity(),
                nn.ConvTranspose2d(dim_out, dim_in, 4, 2, 1) if not is_last else nn.Conv2d(dim_out, dim_in, 3, padding=1)
            ]))
        
        # 输出层 - 预测残差
        self.out_dim = channels
        self.final_res_block = ResBlock(dim * 2, dim, groups=resnet_block_groups)
        self.final_conv = nn.Conv2d(dim, self.out_dim, 1)
    
    def forward(self, x, time, x_self_cond=None, x_condition=None):
        """
        x: 噪声分割图 [B, 1, H, W]
        time: 时间步 [B]
        x_self_cond: 自条件（可选）
        x_condition: 条件输入（低质量分割）[B, 1, H, W]
        """
        
        # 准备输入
        if self.self_condition:
            x_self_cond = x_self_cond if x_self_cond is not None else torch.zeros_like(x)
            x = torch.cat((x_self_cond, x), dim=1)
        
        if self.condition and x_condition is not None:
            x = torch.cat((x, x_condition), dim=1)
        
        # 初始卷积
        x = self.init_conv(x)
        r = x.clone()
        
        # 时间嵌入
        _ = self.time_mlp(time)  # 时间嵌入暂未使用，但保留接口
        
        h = []
        
        # 下采样
        for block1, block2, attn, downsample in self.downs:
            x = block1(x)
            h.append(x)
            
            x = block2(x)
            x = attn(x)
            h.append(x)
            
            x = downsample(x)
        
        # 中间层
        x = self.mid_block1(x)
        x = self.mid_attn(x)
        x = self.mid_block2(x)
        
        # 上采样
        for block1, block2, attn, upsample in self.ups:
            x = torch.cat((x, h.pop()), dim=1)
            x = block1(x)
            
            x = torch.cat((x, h.pop()), dim=1)
            x = block2(x)
            x = attn(x)
            
            x = upsample(x)
        
        # 输出
        x = torch.cat((x, r), dim=1)
        x = self.final_res_block(x)
        return self.final_conv(x)


class BuildingSegmentationRDDM(nn.Module):
    """建筑物分割RDDM去噪器 - 仿照l2h_diffusers.py的风格"""
    
    def __init__(
        self,
        unet_dim=64,
        unet_dim_mults=(1, 2, 4, 8),
        channels=1,
        timesteps=1000,
        sampling_timesteps=250,
        loss_type='l1',
        objective='pred_res_noise',  # RDDM的核心：预测残差和噪声
        sum_scale=0.01
    ):
        super().__init__()
        
        # UNet模型：预测残差
        self.unet = UNetRDDM(
            dim=unet_dim,
            channels=channels,
            dim_mults=unet_dim_mults,
            condition=True  # 启用条件输入
        )
        
        # RDDM参数
        self.channels = channels
        self.num_timesteps = timesteps
        self.sampling_timesteps = sampling_timesteps
        self.loss_type = loss_type
        self.objective = objective
        self.sum_scale = sum_scale
        
        # 生成RDDM系数
        alphas = gen_coefficients(timesteps, schedule="decreased")
        alphas_cumsum = alphas.cumsum(dim=0).clamp(0, 1)
        alphas_cumsum_prev = F.pad(alphas_cumsum[:-1], (1, 0), value=1.)
        
        betas2 = gen_coefficients(timesteps, schedule="increased", sum_scale=sum_scale)
        betas2_cumsum = betas2.cumsum(dim=0).clamp(0, 1)
        betas_cumsum = torch.sqrt(betas2_cumsum)
        
        # 注册为buffer
        self.register_buffer('alphas', alphas)
        self.register_buffer('alphas_cumsum', alphas_cumsum)
        self.register_buffer('alphas_cumsum_prev', alphas_cumsum_prev)
        self.register_buffer('betas2', betas2)
        self.register_buffer('betas2_cumsum', betas2_cumsum)
        self.register_buffer('betas_cumsum', betas_cumsum)
        
        # 后验分布参数
        posterior_variance = betas2 * alphas_cumsum_prev / betas2_cumsum
        posterior_variance[0] = 0
        self.register_buffer('posterior_variance', posterior_variance)
    
    def predict_start_from_res_noise(self, x_t, t, x_res, noise):
        """RDDM核心：从残差和噪声预测原始图像"""
        return (
            x_t - extract(self.alphas_cumsum, t, x_t.shape) * x_res -
            extract(self.betas_cumsum, t, x_t.shape) * noise
        )
    
    def q_sample(self, x_start, x_condition, t, noise=None):
        """RDDM前向过程：给高质量分割添加噪声"""
        if noise is None:
            noise = torch.randn_like(x_start)
        
        # 计算残差
        x_res = x_start - x_condition
        
        # RDDM前向过程
        return (
            x_condition +
            extract(self.alphas_cumsum, t, x_start.shape) * x_res +
            extract(self.betas_cumsum, t, x_start.shape) * noise
        ), x_res, noise
    
    def forward(self, high_seg, low_seg, timesteps=None):
        """
        训练前向传播
        high_seg: [B, 1, H, W] 高质量分割（目标）
        low_seg: [B, 1, H, W] 低质量分割（条件）
        timesteps: [B] 时间步（可选）
        """
        B, C, H, W = high_seg.shape
        device = high_seg.device
        
        # 随机采样时间步
        if timesteps is None:
            timesteps = torch.randint(0, self.num_timesteps, (B,), device=device).long()
        
        # 前向扩散过程
        x_noisy, x_res_true, noise_true = self.q_sample(high_seg, low_seg, timesteps)
        
        # UNet预测
        model_out = self.unet(x_noisy, timesteps, x_condition=low_seg)
        
        if self.objective == 'pred_res_noise':
            # 预测残差和噪声的组合
            target = x_res_true + noise_true
        elif self.objective == 'pred_res':
            # 只预测残差
            target = x_res_true
        elif self.objective == 'pred_noise':
            # 只预测噪声
            target = noise_true
        else:
            raise ValueError(f'unknown objective {self.objective}')
        
        # 计算损失
        if self.loss_type == 'l1':
            loss = F.l1_loss(model_out, target)
        elif self.loss_type == 'l2':
            loss = F.mse_loss(model_out, target)
        else:
            raise ValueError(f'unknown loss type {self.loss_type}')
        
        return loss, model_out, target, x_noisy, x_res_true, noise_true

    @torch.no_grad()
    def p_sample(self, x, t, x_condition, clip_denoised=True):
        """RDDM单步去噪采样"""
        b, *_, device = *x.shape, x.device
        batched_times = torch.full((b,), t, device=device, dtype=torch.long)

        # UNet预测
        model_out = self.unet(x, batched_times, x_condition=x_condition)

        if self.objective == 'pred_res_noise':
            # 分离残差和噪声（简单平均分配）
            pred_res = model_out * 0.5
            pred_noise = model_out * 0.5
        elif self.objective == 'pred_res':
            pred_res = model_out
            pred_noise = torch.zeros_like(model_out)
        elif self.objective == 'pred_noise':
            pred_res = torch.zeros_like(model_out)
            pred_noise = model_out
        else:
            raise ValueError(f'unknown objective {self.objective}')

        # 预测原始图像
        pred_x_start = self.predict_start_from_res_noise(x, batched_times, pred_res, pred_noise)

        if clip_denoised:
            pred_x_start = pred_x_start.clamp(0., 1.)

        # 计算后验均值
        if t == 0:
            return pred_x_start
        else:
            # 简化的后验采样
            alpha_cumsum = extract(self.alphas_cumsum, batched_times, x.shape)
            beta_cumsum = extract(self.betas_cumsum, batched_times, x.shape)

            # 计算前一步的值
            pred_x_prev = (
                x_condition +
                alpha_cumsum * (pred_x_start - x_condition) +
                beta_cumsum * torch.randn_like(x) * 0.1  # 添加少量噪声
            )

            return pred_x_prev

    @torch.no_grad()
    def p_sample_loop(self, low_seg, shape=None, return_all_timesteps=False):
        """RDDM完整采样循环"""
        device = low_seg.device

        if shape is None:
            shape = low_seg.shape

        # 从纯噪声开始
        img = torch.randn(shape, device=device)

        imgs = [img]

        # 采样时间步
        timesteps = torch.linspace(self.num_timesteps - 1, 0, self.sampling_timesteps).long()

        for t in tqdm(timesteps, desc='Sampling loop time step', total=self.sampling_timesteps):
            img = self.p_sample(img, t, low_seg)
            imgs.append(img)

        ret = img if not return_all_timesteps else imgs
        return ret

    @torch.no_grad()
    def sample(self, low_seg, return_all_timesteps=False):
        """采样接口"""
        return self.p_sample_loop(low_seg, low_seg.shape, return_all_timesteps)

    @torch.no_grad()
    def enhance_segmentation(self, low_seg):
        """增强分割结果的主要接口"""
        return self.sample(low_seg, return_all_timesteps=False)


def train_building_rddm(
    model,
    train_dataloader,
    val_dataloader=None,
    num_epochs=100,
    learning_rate=1e-4,
    device='cuda',
    save_every=10,
    save_path='./checkpoints'
):
    """
    训练Building RDDM模型

    Args:
        model: BuildingSegmentationRDDM模型
        train_dataloader: 训练数据加载器，返回(high_seg, low_seg)
        val_dataloader: 验证数据加载器（可选）
        num_epochs: 训练轮数
        learning_rate: 学习率
        device: 设备
        save_every: 保存间隔
        save_path: 保存路径
    """
    import os
    from torch.optim import Adam

    os.makedirs(save_path, exist_ok=True)

    model = model.to(device)
    optimizer = Adam(model.parameters(), lr=learning_rate)

    best_loss = float('inf')

    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0

        pbar = tqdm(train_dataloader, desc=f'Epoch {epoch+1}/{num_epochs}')
        for _, (high_seg, low_seg) in enumerate(pbar):
            high_seg = high_seg.to(device)
            low_seg = low_seg.to(device)

            optimizer.zero_grad()

            # 前向传播
            loss, _, _, _, _, _ = model(high_seg, low_seg)

            # 反向传播
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            optimizer.step()

            train_loss += loss.item()
            pbar.set_postfix({'loss': loss.item()})

        train_loss /= len(train_dataloader)

        # 验证阶段
        if val_dataloader is not None:
            model.eval()
            val_loss = 0.0

            with torch.no_grad():
                for high_seg, low_seg in val_dataloader:
                    high_seg = high_seg.to(device)
                    low_seg = low_seg.to(device)

                    loss, _, _, _, _, _ = model(high_seg, low_seg)
                    val_loss += loss.item()

            val_loss /= len(val_dataloader)
            print(f'Epoch {epoch+1}: Train Loss: {train_loss:.6f}, Val Loss: {val_loss:.6f}')

            # 保存最佳模型
            if val_loss < best_loss:
                best_loss = val_loss
                torch.save({
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'epoch': epoch,
                    'loss': val_loss
                }, os.path.join(save_path, 'best_model.pt'))
        else:
            print(f'Epoch {epoch+1}: Train Loss: {train_loss:.6f}')

        # 定期保存
        if (epoch + 1) % save_every == 0:
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'epoch': epoch,
                'loss': train_loss
            }, os.path.join(save_path, f'model_epoch_{epoch+1}.pt'))


def inference_building_rddm(
    model,
    low_seg_input,
    checkpoint_path=None,
    device='cuda'
):
    """
    Building RDDM推理

    Args:
        model: BuildingSegmentationRDDM模型
        low_seg_input: 低质量分割输入 [B, 1, H, W] 或 [1, H, W]
        checkpoint_path: 模型权重路径（可选）
        device: 设备

    Returns:
        enhanced_seg: 增强后的分割结果
    """
    model = model.to(device)

    # 加载权重
    if checkpoint_path is not None:
        checkpoint = torch.load(checkpoint_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        print(f"Loaded model from {checkpoint_path}")

    model.eval()

    # 处理输入维度
    if len(low_seg_input.shape) == 3:
        low_seg_input = low_seg_input.unsqueeze(0)

    low_seg_input = low_seg_input.to(device)

    # 推理
    with torch.no_grad():
        enhanced_seg = model.enhance_segmentation(low_seg_input)

    return enhanced_seg


# 使用示例
if __name__ == "__main__":
    # 创建模型
    model = BuildingSegmentationRDDM(
        unet_dim=64,
        unet_dim_mults=(1, 2, 4, 8),
        channels=1,
        timesteps=1000,
        sampling_timesteps=250,
        objective='pred_res_noise'
    )

    # 模拟数据
    batch_size = 2
    height, width = 256, 256

    # 模拟低质量和高质量分割
    low_seg = torch.rand(batch_size, 1, height, width)  # 低质量分割
    high_seg = torch.rand(batch_size, 1, height, width)  # 高质量分割（训练时使用）

    print("Building Segmentation RDDM - 测试")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 测试训练前向传播
    model.train()
    loss, model_out, target, x_noisy, x_res_true, noise_true = model(high_seg, low_seg)
    print(f"训练损失: {loss.item():.6f}")
    print(f"模型输出形状: {model_out.shape}")

    # 测试推理
    model.eval()
    with torch.no_grad():
        enhanced_seg = model.enhance_segmentation(low_seg)
        print(f"增强分割形状: {enhanced_seg.shape}")
        print(f"增强分割范围: [{enhanced_seg.min():.3f}, {enhanced_seg.max():.3f}]")

    print("✓ 所有测试通过！")
