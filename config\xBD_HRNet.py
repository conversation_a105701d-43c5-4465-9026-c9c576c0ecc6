import torch
from torch import nn
import os
from glob import glob
import albumentations as albu
import argparse
from pathlib import Path
import numpy as np
# from model.LBD_S12v15 import HRNet_S2, HRNet_single
# from model.PVTv2 import PVTv2_Segmentation
# from model.UANet import UANet_pvt
from model.UANet_LN import UANet_pvt
from loss.loss import OhemCELoss
from dataset.xBD import xBD_Dataset, dataset_collate

# =============================训练参数============================ #
batch_size = 10
lr = 1e-4
size = 384
epochs = 100
output_building = 2
output_damage = 2
data_name = "xBD_512"
model_name = "UANet_pvt_ln"
save_path = os.path.join("./result", data_name, model_name)
os.makedirs(save_path, exist_ok=True)

# 模型加载
device = 'cuda'
model = UANet_pvt(num_classes=output_building)
pre_train_model_path = None#"/mnt/d2/zxq/BuildingDamage/result/xBD_512/S2_HRNet2_single/27.pt"
pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/13.pt"

# 损失函数
criterion1 = OhemCELoss(thresh=0.7, n_min=batch_size*size*size//16, ignore_index=255)
criterion2 = nn.L1Loss()
criterion3 = nn.KLDivLoss(reduction="mean")

# 数据集加载
base_path = "/mnt/d2/zxq/BuildingDamage/dataset/xBD_512"
train_dataset = xBD_Dataset('train', base_path)
val_dataset = xBD_Dataset('test', base_path)
collate_fn = dataset_collate
# =============================训练参数============================ #
