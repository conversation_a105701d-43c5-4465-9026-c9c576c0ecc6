import math
from osgeo import gdal, osr
import numpy as np
import os
from glob import glob
import matplotlib.pyplot as plt
from multiprocessing import Pool
import shutil
import random
from tqdm import tqdm
from PIL import Image
from shapely.geometry import Polygon, box
import geopandas as gpd

label_keys = [
    [  0,   0,   0], # 0 非建筑物区域
    [255, 255, 255], # 1 未损毁
    [  0, 255,   0], # 3 Severe
    [255,   0,   0], # 4 Destroyed
]

def classId2rgb(out):
    out = np.array(out).astype(np.float32)
    result = np.zeros((out.shape[0], out.shape[1], 3)).astype(np.int16)
    for i in range(len(label_keys)):
        print(i, out==i)
        result[out==i, :] = label_keys[i]
    result = Image.fromarray(result.astype(np.uint8))
    return result

def tif_resample(input_file, output_file, xRes=0.5, yRes=0.5):
    gdal.Warp(output_file, input_file, xRes=xRes,  yRes=yRes, resampleAlg=gdal.GRA_NearestNeighbour, dstSRS="EPSG:3857") # , Es=EPSG:3857


def get_tif_bounds(tif_path):
    """获取TIF文件的地理范围 (min_x, min_y, max_x, max_y)"""
    dataset = gdal.Open(tif_path)
    if not dataset:
        raise ValueError(f"无法打开TIF文件: {tif_path}")
    
    gt = dataset.GetGeoTransform()
    width = dataset.RasterXSize
    height = dataset.RasterYSize

    min_x = gt[0]
    max_y = gt[3]
    max_x = min_x + width * gt[1]
    min_y = max_y + height * gt[5]
    
    # gdal以像素中心为基准，所以要确保坐标顺序正确
    if max_x < min_x: min_x, max_x = max_x, min_x
    if max_y < min_y: min_y, max_y = max_y, min_y
    
    return (min_x, min_y, max_x, max_y)

def get_tif_bounds(tif_path):
    """获取tif图像边界"""
    ds = gdal.Open(tif_path)
    gt = ds.GetGeoTransform()
    x_size = ds.RasterXSize
    y_size = ds.RasterYSize
    min_x = gt[0]
    max_y = gt[3]
    max_x = min_x + x_size * gt[1]
    min_y = max_y + y_size * gt[5]
    ds = None
    return (min_x, min_y, max_x, max_y)

def get_epsg(tif_path):
    """获取图像的EPSG编号"""
    ds = gdal.Open(tif_path)
    proj = osr.SpatialReference(wkt=ds.GetProjection())
    epsg = proj.GetAttrValue("AUTHORITY", 1)
    ds = None
    return int(epsg)

def transform_bounds(bounds, src_epsg, dst_epsg):
    """边界坐标转换：从src_epsg到dst_epsg"""
    min_x, min_y, max_x, max_y = bounds

    src = osr.SpatialReference()
    src.ImportFromEPSG(src_epsg)

    dst = osr.SpatialReference()
    dst.ImportFromEPSG(dst_epsg)

    transform = osr.CoordinateTransformation(src, dst)

    # 四个角变换
    (min_x_t, min_y_t, _) = transform.TransformPoint(min_x, min_y)
    (max_x_t, max_y_t, _) = transform.TransformPoint(max_x, max_y)

    # 返回投影后的边界
    return (min(min_x_t, max_x_t), min(min_y_t, max_y_t),
            max(min_x_t, max_x_t), max(min_y_t, max_y_t))


def clip_big_tif_by_small(small_tif_path, big_tif_path, save_path):
    small_bounds = get_tif_bounds(small_tif_path)
    s_min_x, s_min_y, s_max_x, s_max_y = small_bounds
    # small_epsg = get_epsg(small_tif_path)
    # big_epsg = get_epsg(big_tif_path)

    # 将小图边界转换为大图坐标系下的边界
    # transformed_bounds = transform_bounds(small_bounds, small_epsg, big_epsg)
    
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    # options = gdal.WarpOptions(format="GTiff", outputBounds=(s_min_x, s_min_y, s_max_x, s_max_y), cutlineBlend=0, dstNodata=0, outputType=gdal.GDT_Float32, options=["PHOTOMETRIC=MINISBLACK"])
    # options = gdal.WarpOptions(format="GTiff", outputBounds=(s_min_x, s_min_y, s_max_x, s_max_y), cutlineBlend=0, dstNodata=0, outputType=gdal.GDT_Byte, options=["PHOTOMETRIC=MINISBLACK"])
    options = gdal.WarpOptions(
        format="GTiff",
        outputBounds=small_bounds,
        xRes=8, yRes=8,  # 明确指定输出分辨率
        resampleAlg='near',  # 最近邻，保持原始值（比如分类或整数波段）
        dstNodata=255,
        outputType=gdal.GDT_Float32
    )
    gdal.Warp(destNameOrDestDS=save_path,  srcDSOrSrcDSTab=big_tif_path,  options=options)

def split_train_test(high_resample_tif_paths):
    random.seed(0)
    random.shuffle(high_resample_tif_paths)
    train_num = int(len(high_resample_tif_paths) * 0.8)
    for id, high_resample_tif_path in enumerate(high_resample_tif_paths):
        if id <= train_num:
            mode = "train"
        else:
            mode = "test"
        img_dst_path = high_resample_tif_path.replace(f"High/TIF_clip_resample", f"dataset/{mode}/images")
        os.makedirs(os.path.dirname(img_dst_path), exist_ok=True)
        print(high_resample_tif_path, img_dst_path)
        shutil.copyfile(high_resample_tif_path, img_dst_path)
    return

def clip_high_tif_2_patch(high_tif_path):
    # 读取 SHP

    dataset = gdal.Open(high_tif_path)
    geotransform = dataset.GetGeoTransform()
    projection = dataset.GetProjection()

    image_array = dataset.ReadAsArray()
    image_array = np.array(image_array).astype(np.float32)
    _, w, h = image_array.shape
    print(h, w)
    split_withd = 384
    r           = 0
    row         = int(np.ceil((h-r*2)/(split_withd-r*2)))
    col         = int(np.ceil((w-r*2)/(split_withd-r*2)))
    print(h, w, split_withd, r, row, col)
    args = []
    for i in range(row):
        for j in range(col):
            if i < (row-1) and j < (col-1):
                x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
                y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd         
            
            #-------------------------------------------------#
            #   裁剪的图像在最后一行
            elif i == row-1 and j < (col-1):
                x1, x2 = h-split_withd, h
                y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd
            
            #-------------------------------------------------#
            #   裁剪的图像在最后一列
            elif i < row-1 and j == (col-1):
                x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
                y1, y2 = w-split_withd, w

            #-------------------------------------------------#
            #   裁剪的图像在右下角
            elif i == row-1 and j == (col-1):
                x1, x2 = h-split_withd, h
                y1, y2 = w-split_withd, w

            min_x = geotransform[0] + x1 * geotransform[1]
            max_y = geotransform[3] + y1 * geotransform[5]
            max_x = min_x + split_withd * geotransform[1]
            min_y = max_y + split_withd * geotransform[5]          


            patch_path = os.path.join(os.path.dirname(high_tif_path).replace("/images", "/images_clip"), os.path.basename(high_tif_path).replace(".tif", f"_{i}_{j}.tif"))
            os.makedirs(os.path.dirname(patch_path), exist_ok=True)
            # args.append([patch_path, high_tif_path, [min_x, min_y, max_x, max_y]])
            print(patch_path, high_tif_path, [min_x, min_y, max_x, max_y])
            # x = input()

            gdal.Warp(patch_path, high_tif_path, outputBounds=(min_x, min_y, max_x, max_y), dstSRS=projection)

def clip_big_tif_by_small_margin(small_tif_path, big_tif_path, save_path):
    small_bounds = get_tif_bounds(small_tif_path)
    s_min_x, s_min_y, s_max_x, s_max_y = small_bounds
    aligned_left = math.floor(s_min_x / 10) * 10
    aligned_top  = math.ceil(s_max_y  / 10) * 10
    # 计算右下角坐标
    aligned_right = aligned_left + 22*10
    aligned_bottom = aligned_top - 22*10

    output_bounds = (aligned_left, aligned_bottom, aligned_right, aligned_top)
    contains = (aligned_left <= s_min_x and
                aligned_right >= s_max_x and
                aligned_top >= s_max_y and
                aligned_bottom <= s_min_y)    
    if not contains:
        raise ValueError(
            f"[裁剪范围错误] 输出裁剪区域未能完全包含高分图像范围。\n"
            f"输出裁剪 bounds: {output_bounds}\n"
            f"高分影像 bounds: {(s_min_x, s_min_y, s_max_x, s_max_y)} \n"
            f"[保存路径]{save_path}"
        )
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    # options = gdal.WarpOptions(format="GTiff", outputBounds=(s_min_x, s_min_y, s_max_x, s_max_y), cutlineBlend=0, dstNodata=255, outputType=gdal.GDT_Float32, options=["PHOTOMETRIC=MINISBLACK"])
    options = gdal.WarpOptions(
        format="GTiff",
        outputBounds=output_bounds,
        xRes=10, yRes=10,  # 明确指定输出分辨率
        resampleAlg='near',  # 最近邻，保持原始值（比如分类或整数波段）
        dstNodata=None,
        outputType=gdal.GDT_Float32
    )
    gdal.Warp(destNameOrDestDS=save_path,  srcDSOrSrcDSTab=big_tif_path,  options=options)




def save_to_tif(pred_result, save_path, test_image_path):
    """
    pred_result: 需要保存的图像
    save_path: 保存路径
    test_image_path: 空间参考系
    """
    img = pred_result
    im_geotrans, im_prof = [], []
    tif = gdal.Open(os.path.join(test_image_path))
    im_geotrans.append(tif.GetGeoTransform())
    im_prof.append(tif.GetProjection())
    if 'uint8' in img.dtype.name:
        datatype = gdal.GDT_Byte
    elif 'uint16' in img.dtype.name:
        datatype = gdal.GDT_UInt16
    else:
        datatype = gdal.GDT_Float32
    # print(img.dtype.name)
    #判读数组维数
    if len(img.shape) == 3:
        im_bands, im_height, im_width = img.shape
    else:
        im_bands, (im_height, im_width) = 1, img.shape

    #创建文件
    driver  = gdal.GetDriverByName("GTiff")                #数据类型必须有，因为要计算需要多大内存空间
    dataset = driver.Create(save_path, im_width, im_height, im_bands, datatype)
    dataset.SetGeoTransform(im_geotrans[-1])              #写入仿射变换参数
    dataset.SetProjection(im_prof[-1])                    #写入投影
    if im_bands == 1:
        dataset.GetRasterBand(1).WriteArray(img)  #写入数组数据
    else:
        for i in range(im_bands):
            dataset.GetRasterBand(i+1).WriteArray(img[i])
    del dataset
    # print('保存成功')


if __name__ == "__main__":
    event_name = "hawaii"
    event_base_path = f"/mnt/E/Dataset/Hawaii/"

    # 2: 灾前 RGB 重采样至 0.5米分辨率
    # small_tif_paths = glob(os.path.join(event_base_path, "High/TIF/*.tif"))
    # args = []
    # for small_tif_path in small_tif_paths:
    #     resample_path = small_tif_path.replace("/TIF", "/TIF_clip_resample")
    #     os.makedirs(os.path.dirname(resample_path), exist_ok=True)
    #     args.append((small_tif_path, resample_path))
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(tif_resample, args), total=len(args), desc="tif resample"))

    # 3: 灾前 RGB 随机划分
    # split_train_test(high_resample_tif_paths=glob(os.path.join(event_base_path, "High/TIF_clip_resample/*pre*.tif")))

    # 4: 灾前 RGB 裁剪为384x384的patch
    # small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images/*.tif"))
    # for small_tif_path in small_tif_paths:
    #     clip_high_tif_2_patch(small_tif_path)

    # 5: 根据 384x384 的 patch 裁剪label_post
    # small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images_clip/*.tif"))
    # big_tif_paths = glob(os.path.join(event_base_path, "Label/post_disaster_modifyv2.tif"))
    # args = []
    # for small_tif_path in tqdm(small_tif_paths):
    #     for big_id, big_tif_path in enumerate(big_tif_paths):
    #         save_path = small_tif_path.replace("/images_clip/", "/labels/").replace("turkey-", "kahramanmara-").replace("_pre_", "_post_")
    #         # print(save_path)
    #         os.makedirs(os.path.dirname(save_path), exist_ok=True)
    #         args.append((small_tif_path, big_tif_path, save_path))
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(clip_big_tif_by_small, args), total=len(args), desc="clip S2 tif"))

    # 5:1 根据 384x384 的 patch 裁剪 灾后影像
    # small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images_clip/*.tif"))
    # big_tif_paths = glob(os.path.join(event_base_path, "High/Kahramanmara_post_resample.tif"))
    # args = []
    # for small_tif_path in small_tif_paths:
    #     for big_id, big_tif_path in enumerate(big_tif_paths):
    #         save_path = small_tif_path.replace("/images_clip/", "/images_clip/").replace("turkey-", "kahramanmara-").replace("_pre_", "_post_")
    #         print(save_path)
    #         os.makedirs(os.path.dirname(save_path), exist_ok=True)
    #         args.append((small_tif_path, big_tif_path, save_path))
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(clip_big_tif_by_small, args), total=len(args), desc="clip S2 tif"))

    # 6: 根据 384x384 的 label_post 生成 label_pre
    # small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/labels/*post*.tif"))
    # for small_tif_path in small_tif_paths:
    #     save_path = small_tif_path.replace("_post_", "_pre_")
    #     os.makedirs(os.path.dirname(save_path), exist_ok=True)
    #     # print(small_tif_path, save_path)
    #     dataset = gdal.Open(small_tif_path)
    #     geotransform = dataset.GetGeoTransform()
    #     projection = dataset.GetProjection()
    #     image_array = dataset.ReadAsArray()
    #     image_array = np.array(image_array).astype(np.float32)
    #     image_array = np.where(image_array >= 1, 1, 0)
    #     save_to_tif(image_array, save_path, small_tif_path)


    # # 7: 根据 384x384 的 label_post 生成 label_rgb
    # small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/labels/*post*.tif"))
    # for small_tif_path in small_tif_paths:
    #     save_path = small_tif_path.replace("/labels", "/labels_rgb")
    #     os.makedirs(os.path.dirname(save_path), exist_ok=True)

    #     dataset = gdal.Open(small_tif_path)
    #     geotransform = dataset.GetGeoTransform()
    #     projection = dataset.GetProjection()

    #     image_array = dataset.ReadAsArray()
    #     image_array = np.array(image_array).astype(np.float32)
    #     image_array = np.where(image_array >= 4, 0, image_array)
    #     image_array = np.where(image_array <= 0, 0, image_array)

    #     print(np.max(image_array), np.min(image_array), image_array.shape)

    #     image_array = classId2rgb(image_array)
        # save_to_tif(image_array, save_path, small_tif_path)
        # image_array.save(save_path)

    # 8：S2数据重采样
    # s2_tif_paths = glob(os.path.join(event_base_path, "S2_Python/**.tif"))
    # args = []
    # for s2_tif_path in s2_tif_paths:
    #     resample_path = s2_tif_path.replace("/S2_Python/", "/S2_Python_resample/")
    #     os.makedirs(os.path.dirname(resample_path), exist_ok=True)
    #     args.append((s2_tif_path, resample_path, 8, 8))
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(tif_resample, args), total=len(args), desc="tif resample"))

    # 9: 根据 384x384 的 clip_rgb 裁剪 S2 tif
    small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images_clip/*pre*.tif"))
    big_tif_paths = glob(os.path.join(event_base_path, "S2_Python/**.tif"))
    small_tif_paths.sort()
    big_tif_paths.sort()
    args = []
    print(len(small_tif_paths), len(big_tif_paths))
    small_count = 0
    for small_tif_path in tqdm(small_tif_paths):
        small_bounds = get_tif_bounds(small_tif_path)
        big_count = 0
        small_count += 1
        for big_id, big_tif_path in enumerate(big_tif_paths):
            big_bounds = get_tif_bounds(big_tif_path)
            # if is_small_in_big(small_bounds, big_bounds):
            if len(big_tif_paths)-1 == big_id:
                save_path = os.path.dirname(small_tif_path.replace("/images_clip/", "/S2_Python_Margin/S2_post/"))
                save_name = os.path.basename(small_tif_path).split(".")[0].replace("_pre_", "_post_") + "_S2_" + os.path.basename(big_tif_path).split("_S2_")[-1]
            else:
                save_path = os.path.dirname(small_tif_path.replace("/images_clip/", "/S2_Python_Margin/S2/"))
                save_name = os.path.basename(small_tif_path).split(".")[0] + "_S2_" + os.path.basename(big_tif_path).split("_S2_")[-1]

            os.makedirs(save_path, exist_ok=True)
            big_count += 1
            # print(small_tif_path, big_tif_path, os.path.join(save_path, save_name))
            args.append((small_tif_path, big_tif_path, os.path.join(save_path, save_name)))
        # if len(args) >=100:
        #     break
    print(f"Total: {len(args)}")
    num_workers = os.cpu_count()
    with Pool(num_workers) as pool:
        list(tqdm(pool.starmap(clip_big_tif_by_small_margin, args), total=len(args), desc="clip S2 tif"))

    # 10：S1数据重采样
    # s2_tif_paths = glob(os.path.join(event_base_path, "S1**/**.tif"))
    # args = []
    # for s2_tif_path in s2_tif_paths:
    #     resample_path = s2_tif_path.replace("/S1", "/S1_resample")
    #     os.makedirs(os.path.dirname(resample_path), exist_ok=True)
    #     args.append((s2_tif_path, resample_path, 4, 4))
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(tif_resample, args), total=len(args), desc="s1 tif resample"))

    # 11：将S1数据裁剪成 384x384 的小图像
    # small_tif_paths = glob(os.path.join(event_base_path, "dataset/**/images_clip/*pre*.tif"))
    # big_tif_paths = glob(os.path.join(event_base_path, "S1_resample/**.tif"))
    # small_tif_paths.sort()
    # big_tif_paths.sort()
    # args = []
    # print(len(small_tif_paths), len(big_tif_paths))
    # small_count = 0
    # for small_tif_path in tqdm(small_tif_paths):
    #     small_bounds = get_tif_bounds(small_tif_path)
    #     big_count = 0
    #     small_count += 1
    #     for big_id, big_tif_path in enumerate(big_tif_paths):
    #         if len(big_tif_paths)-1 == big_id:
    #             save_path = os.path.dirname(small_tif_path.replace("/images_clip/", "/S1_post/"))
    #             save_name = os.path.basename(small_tif_path).split(".")[0].replace("_pre_", "_post_") + "_S1_" + os.path.basename(big_tif_path).split("_S1_")[-1]
    #         else:
    #             save_path = os.path.dirname(small_tif_path.replace("/images_clip/", "/S1/"))
    #             save_name = os.path.basename(small_tif_path).split(".")[0] + "_S1_" + os.path.basename(big_tif_path).split("_S1_")[-1]

    #         # save_path = os.path.dirname(small_tif_path.replace("/images_clip/", "/S1_CohIfg/"))
    #         # save_name = os.path.basename(small_tif_path).split(".")[0] + "_S1_CohIfg_" +os.path.basename(big_tif_path).split("_S1_CohIfg")[0] + ".tif"
    #         print(save_name)
    #         # os.makedirs(save_path, exist_ok=True)
    #         big_count += 1
    #         print(small_tif_path, big_tif_path, os.path.join(save_path, save_name))
    #         args.append((small_tif_path, big_tif_path, os.path.join(save_path, save_name)))
    # print(f"Total: {len(args)}")
    # num_workers = os.cpu_count()
    # with Pool(num_workers) as pool:
    #     list(tqdm(pool.starmap(clip_big_tif_by_small, args), total=len(args), desc="clip S2 tif"))