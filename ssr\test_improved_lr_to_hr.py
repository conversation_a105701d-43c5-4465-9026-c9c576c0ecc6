#!/usr/bin/env python3
"""
测试改进后的LR到HR RDDM模型
验证：
1. 复杂的alpha/beta系数调度
2. 推理过程中正确使用噪声预测
"""

import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt
import numpy as np
from lr_to_hr_rddm import create_lr_to_hr_model, generate_rddm_schedules

def test_schedule_generation():
    """测试不同的调度策略"""
    print("=== 测试调度策略 ===")
    
    timesteps = 100
    alpha_scale = 0.15
    beta_scale = 0.002
    
    schedule_types = ["cosine", "linear", "exponential", "sigmoid"]
    
    plt.figure(figsize=(15, 10))
    
    for i, schedule_type in enumerate(schedule_types):
        alphas, betas = generate_rddm_schedules(
            timesteps=timesteps,
            alpha_scale=alpha_scale,
            beta_scale=beta_scale,
            schedule_type=schedule_type
        )
        
        # 绘制alpha调度
        plt.subplot(2, 4, i + 1)
        plt.plot(alphas.numpy(), label=f'Alpha ({schedule_type})')
        plt.title(f'Alpha Schedule - {schedule_type}')
        plt.xlabel('Timestep')
        plt.ylabel('Alpha Value')
        plt.grid(True)
        plt.legend()
        
        # 绘制beta调度
        plt.subplot(2, 4, i + 5)
        plt.plot(betas.numpy(), label=f'Beta ({schedule_type})')
        plt.title(f'Beta Schedule - {schedule_type}')
        plt.xlabel('Timestep')
        plt.ylabel('Beta Value')
        plt.grid(True)
        plt.legend()
        
        print(f"{schedule_type} 调度:")
        print(f"  Alpha范围: [{alphas.min():.6f}, {alphas.max():.6f}]")
        print(f"  Beta范围: [{betas.min():.6f}, {betas.max():.6f}]")
    
    plt.tight_layout()
    plt.savefig('rddm_schedules.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return True

def create_test_features(batch_size=1, channels=2, height=64, width=64):
    """创建测试特征数据"""
    # 创建HR特征：具有清晰结构
    hr_features = torch.zeros(batch_size, channels, height, width)
    
    # 添加一些结构化特征
    for b in range(batch_size):
        # 通道0：主要特征
        hr_features[b, 0, 15:25, 15:25] = 1.5
        hr_features[b, 0, 35:45, 35:45] = -1.2
        hr_features[b, 0, 20:30, 45:55] = 0.8
        
        # 通道1：辅助特征
        hr_features[b, 1, 10:20, 30:40] = -0.9
        hr_features[b, 1, 40:50, 10:20] = 1.1
    
    # 创建LR特征：模糊和噪声
    lr_features = hr_features.clone()
    
    # 高斯模糊
    kernel_size = 7
    sigma = 2.0
    kernel = torch.exp(-torch.arange(-(kernel_size//2), kernel_size//2 + 1)**2 / (2*sigma**2))
    kernel = kernel / kernel.sum()
    kernel_2d = kernel.unsqueeze(0) * kernel.unsqueeze(1)
    kernel_2d = kernel_2d.unsqueeze(0).unsqueeze(0).repeat(channels, 1, 1, 1)
    
    lr_features = F.conv2d(lr_features, kernel_2d, padding=kernel_size//2, groups=channels)
    
    # 添加噪声
    lr_features += torch.randn_like(lr_features) * 0.15
    
    # 降低动态范围
    lr_features = lr_features * 0.7
    
    return hr_features, lr_features

def test_different_schedules():
    """测试不同调度策略的效果"""
    print("\n=== 测试不同调度策略的效果 ===")
    
    hr_features, lr_features = create_test_features()
    
    schedule_types = ["cosine", "linear", "exponential", "sigmoid"]
    results = {}
    
    for schedule_type in schedule_types:
        print(f"\n--- 测试 {schedule_type} 调度 ---")
        
        try:
            model = create_lr_to_hr_model(
                dim=32,
                dim_mults=(1, 2, 4),
                channels=2,
                timesteps=50,
                sampling_timesteps=10,
                alpha_scale=0.12,
                beta_scale=0.002,
                schedule_type=schedule_type
            )
            
            # 测试训练
            model.train()
            loss, pred_hr = model(hr_features, lr_features)
            
            print(f"  训练损失: {loss.item():.6f}")
            
            # 测试推理 - DDIM方法
            model.eval()
            with torch.no_grad():
                enhanced_ddim = model.enhance_features(lr_features, num_steps=10, use_ddim=True)
                enhanced_direct = model.enhance_features(lr_features, num_steps=10, use_ddim=False)
                
                # 计算改进效果
                original_error = F.mse_loss(lr_features, hr_features)
                ddim_error = F.mse_loss(enhanced_ddim, hr_features)
                direct_error = F.mse_loss(enhanced_direct, hr_features)
                
                ddim_improvement = original_error - ddim_error
                direct_improvement = original_error - direct_error
                
                print(f"  原始误差: {original_error.item():.6f}")
                print(f"  DDIM误差: {ddim_error.item():.6f} (改进: {ddim_improvement.item():.6f})")
                print(f"  直接误差: {direct_error.item():.6f} (改进: {direct_improvement.item():.6f})")
                
                results[schedule_type] = {
                    'ddim_improvement': ddim_improvement.item(),
                    'direct_improvement': direct_improvement.item(),
                    'training_loss': loss.item()
                }
                
                print(f"  DDIM状态: {'✓' if ddim_improvement > 0 else '✗'}")
                print(f"  直接状态: {'✓' if direct_improvement > 0 else '✗'}")
                
        except Exception as e:
            print(f"  ✗ 失败: {str(e)}")
            results[schedule_type] = {'error': str(e)}
    
    # 总结结果
    print(f"\n=== 调度策略比较 ===")
    best_ddim = max(results.items(), key=lambda x: x[1].get('ddim_improvement', -999))
    best_direct = max(results.items(), key=lambda x: x[1].get('direct_improvement', -999))
    
    print(f"最佳DDIM调度: {best_ddim[0]} (改进: {best_ddim[1].get('ddim_improvement', 0):.6f})")
    print(f"最佳直接调度: {best_direct[0]} (改进: {best_direct[1].get('direct_improvement', 0):.6f})")
    
    return results

def test_noise_usage():
    """测试噪声预测的使用"""
    print("\n=== 测试噪声预测使用 ===")
    
    model = create_lr_to_hr_model(
        dim=32,
        dim_mults=(1, 2),
        channels=2,
        timesteps=30,
        sampling_timesteps=6,
        alpha_scale=0.1,
        beta_scale=0.001,
        schedule_type="cosine"
    )
    
    hr_features, lr_features = create_test_features()
    
    model.eval()
    with torch.no_grad():
        # 比较使用和不使用DDIM的效果
        enhanced_with_ddim = model.enhance_features(lr_features, num_steps=6, use_ddim=True)
        enhanced_without_ddim = model.enhance_features(lr_features, num_steps=6, use_ddim=False)
        
        # 计算误差
        original_error = F.mse_loss(lr_features, hr_features)
        ddim_error = F.mse_loss(enhanced_with_ddim, hr_features)
        direct_error = F.mse_loss(enhanced_without_ddim, hr_features)
        
        print(f"原始误差: {original_error.item():.6f}")
        print(f"DDIM方法误差: {ddim_error.item():.6f}")
        print(f"直接方法误差: {direct_error.item():.6f}")
        
        ddim_improvement = original_error - ddim_error
        direct_improvement = original_error - direct_error
        
        print(f"DDIM改进: {ddim_improvement.item():.6f}")
        print(f"直接改进: {direct_improvement.item():.6f}")
        
        # 检查噪声预测是否被正确使用
        print(f"\n噪声使用验证:")
        print(f"DDIM方法特征范围: [{enhanced_with_ddim.min():.3f}, {enhanced_with_ddim.max():.3f}]")
        print(f"直接方法特征范围: [{enhanced_without_ddim.min():.3f}, {enhanced_without_ddim.max():.3f}]")
        
        # 检查两种方法的差异
        method_diff = F.mse_loss(enhanced_with_ddim, enhanced_without_ddim)
        print(f"两种方法差异: {method_diff.item():.6f}")
        
        if method_diff > 0.001:  # 如果有显著差异，说明噪声预测被使用了
            print("✓ 噪声预测被正确使用")
            noise_used = True
        else:
            print("✗ 噪声预测可能未被使用")
            noise_used = False
    
    return noise_used and (ddim_improvement > 0 or direct_improvement > 0)

def test_step_by_step_inference():
    """逐步测试推理过程"""
    print("\n=== 逐步推理过程测试 ===")
    
    model = create_lr_to_hr_model(
        dim=16,
        dim_mults=(1, 2),
        channels=2,
        timesteps=20,
        sampling_timesteps=5,
        alpha_scale=0.08,
        beta_scale=0.001,
        schedule_type="cosine"
    )
    
    hr_features, lr_features = create_test_features(height=32, width=32)
    
    print(f"初始LR特征范围: [{lr_features.min():.3f}, {lr_features.max():.3f}]")
    print(f"目标HR特征范围: [{hr_features.min():.3f}, {hr_features.max():.3f}]")
    
    model.eval()
    with torch.no_grad():
        device = lr_features.device
        batch_size = lr_features.shape[0]
        num_steps = 5
        
        x = lr_features.clone()
        timesteps = torch.linspace(model.num_timesteps - 1, 0, num_steps, dtype=torch.long, device=device)
        
        print(f"\n逐步推理过程:")
        for i, t in enumerate(timesteps):
            t_batch = torch.full((batch_size,), t, device=device, dtype=torch.long)
            
            # 预测残差和噪声
            pred_res, pred_noise = model.unet(x, t_batch, x_condition=lr_features)
            
            print(f"步骤 {i+1}/{num_steps} (t={t}):")
            print(f"  输入范围: [{x.min():.3f}, {x.max():.3f}]")
            print(f"  残差范围: [{pred_res.min():.3f}, {pred_res.max():.3f}]")
            print(f"  噪声范围: [{pred_noise.min():.3f}, {pred_noise.max():.3f}]")
            
            # 应用DDIM更新
            pred_x_start = model.predict_start_from_res_noise(x, t_batch, pred_res, pred_noise)
            
            if i < len(timesteps) - 1:
                t_prev = timesteps[i + 1]
                t_prev_batch = torch.full((batch_size,), t_prev, device=device, dtype=torch.long)
                
                from lr_to_hr_rddm import extract
                alpha_prev = extract(model.alphas_cumsum, t_prev_batch, x.shape)
                beta_prev = extract(model.betas_cumsum, t_prev_batch, x.shape)
                
                x_res_prev = lr_features - pred_x_start
                x = pred_x_start + alpha_prev * x_res_prev + beta_prev * pred_noise * 0.1
            else:
                x = pred_x_start
            
            # 计算当前误差
            current_error = F.mse_loss(x, hr_features)
            print(f"  当前误差: {current_error.item():.6f}")
        
        final_error = F.mse_loss(x, hr_features)
        original_error = F.mse_loss(lr_features, hr_features)
        improvement = original_error - final_error
        
        print(f"\n最终结果:")
        print(f"原始误差: {original_error.item():.6f}")
        print(f"最终误差: {final_error.item():.6f}")
        print(f"总改进: {improvement.item():.6f}")
        
        return improvement > 0

if __name__ == "__main__":
    print("测试改进后的LR到HR RDDM模型...")
    
    success1 = test_schedule_generation()
    results = test_different_schedules()
    success2 = test_noise_usage()
    success3 = test_step_by_step_inference()
    
    print(f"\n=== 总体测试结果 ===")
    print(f"调度生成: {'✓' if success1 else '✗'}")
    print(f"噪声使用: {'✓' if success2 else '✗'}")
    print(f"逐步推理: {'✓' if success3 else '✗'}")
    
    # 推荐最佳配置
    if results:
        valid_results = {k: v for k, v in results.items() if 'error' not in v}
        if valid_results:
            best_overall = max(valid_results.items(), 
                             key=lambda x: x[1].get('ddim_improvement', 0) + x[1].get('direct_improvement', 0))
            print(f"\n推荐配置: {best_overall[0]} 调度")
            print(f"推荐参数: alpha_scale=0.12, beta_scale=0.002, use_ddim=True")
    
    if all([success1, success2, success3]):
        print("✓ 所有改进都已验证成功！")
    else:
        print("✗ 部分功能需要进一步优化")
