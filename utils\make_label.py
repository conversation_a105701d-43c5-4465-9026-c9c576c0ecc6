from glob import glob
from osgeo import gdal
import numpy as np

def clean_tif_values(tif_path):
    dataset = gdal.Open(tif_path, gdal.GA_Update)  # 以可写模式打开
    if not dataset:
        print(f"无法打开文件: {tif_path}")
        return

    band = dataset.GetRasterBand(1)
    array = band.ReadAsArray().astype(np.float32)  # 读取数据并转换为 float32

    # 替换 NaN 为 0
    nan_mask = np.isnan(array)
    array[nan_mask] = 0

    # 将值限制在 [0,1] 之间，超出范围的设为 0
    array[(array < -5) | (array > 5)] = 0

    # 写回数据
    band.WriteArray(array)
    dataset.FlushCache()  # 刷新缓存
    print(f"已处理: {tif_path}")

    dataset = None  # 关闭文件


def check_tif_values(tif_path):
    dataset = gdal.Open(tif_path, gdal.GA_ReadOnly)  # 只读模式打开
    if not dataset:
        print(f"无法打开文件: {tif_path}")
        return

    band = dataset.GetRasterBand(1)
    array = band.ReadAsArray().astype(np.float32)  # 读取数据并转换为 float32

    # 计算最小值、最大值
    min_val = np.nanmin(array)
    max_val = np.nanmax(array)

    # 检查是否存在 NaN
    nan_count = np.isnan(array).sum()

    print(f"文件: {tif_path}")
    print(f"  最小值: {min_val}, 最大值: {max_val}")
    print(f"  NaN 值个数: {nan_count}")

    dataset = None  # 关闭文件

# 批量处理所有 label tif 文件
label_paths = glob("/mnt/d2/zxq/BDS12/dataset/eaton/**/labels/**.tif", recursive=True)
for label_path in label_paths:
    clean_tif_values(label_path)
    # check_tif_values(label_path)