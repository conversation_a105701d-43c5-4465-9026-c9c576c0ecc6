from thop import profile
import torch
import torch.nn as nn
import timm
import numpy as np
import torch.nn.functional as F
from einops import rearrange
from dcn_v2 import DCN
# from torchvision.ops import DeformConv2d as DCN  # 也可以用 mmcv 的 DCNv2

class HRNet_single(nn.Module):
    def __init__(self, pretrained=False, s2_inchannel=18, num_classes=3):
        super(HRNet_single, self).__init__()
        self.num_classes = num_classes
        self.rgb_backbone = timm.create_model('hrnet_w48', pretrained=pretrained, features_only=True, out_indices=(1, 2, 3, 4))
        self.pre_train_model_path = "./net/hrnetv2_w48_imagenet_pretrained.pth"
        last_inp_channels = np.int32(np.sum(self.rgb_backbone.feature_info.channels()))
        decoder_channnel = self.rgb_backbone.feature_info.channels()[0]

        self.rgb_decoder_layer = nn.Sequential(
            nn.Conv2d(in_channels=last_inp_channels, out_channels=decoder_channnel, kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=decoder_channnel, out_channels=decoder_channnel, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
        )
        self.class_layer1 = nn.Conv2d(in_channels=decoder_channnel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)

    def _load_weight(self, net):
        if self.pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.pre_train_model_path)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
    
    def decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x

    def forward(self, images, device, mode="pre", output={}):
        B, C, H, W = images.shape
        high_features = self.rgb_backbone(images.to(device).to(torch.float32))
        high_features = self.decoder(high_features, self.rgb_decoder_layer)
        high_outs = self.class_layer1(high_features)
        high_outs = F.interpolate(high_outs, size=(H, W), mode='bilinear', align_corners=True)
        output[f"{mode}_high_building"] = high_outs
        output[f"{mode}_high_features"] = high_features
        return output


class residual_up_decoder(nn.Module):
    def __init__(self, decoder_channel):
        super(residual_up_decoder, self).__init__()
        self.conv_path = nn.Sequential(
            nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channel),
            nn.ReLU(),
            nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channel),
            nn.ReLU(),
        )

        self.res = nn.Conv2d(decoder_channel, decoder_channel, kernel_size=1)
        self.deconv = nn.Sequential(
            nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channel, momentum= 0.1),
            nn.ReLU(inplace=True),
            )

    def forward(self, x):
        x = self.conv_path(x) + self.res(x)
        x = self.deconv(x)
        return x

class DCN_guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.dcn = DCN(decoder_channel*2, decoder_channel, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
        self.bn = nn.BatchNorm2d(decoder_channel)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, current_frames, referent_frames):
        B, frame, C, H, W = referent_frames.shape
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

        assert current_frames.shape == referent_frames.shape

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")
        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)

        fusion_frames = self.dcn(fusion_frames)
        fusion_frames = self.bn(fusion_frames)
        fusion_frames = self.relu(fusion_frames)

        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames

class Res_DCN_guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.dcn = DCN(decoder_channel*2, decoder_channel, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
        self.bn = nn.BatchNorm2d(decoder_channel)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, current_frames, referent_frames):
        B, frame, C, H, W = referent_frames.shape
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

        assert current_frames.shape == referent_frames.shape

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")

        # res = referent_frames1 - current_frames1

        # current_frames1 = current_frames1 * torch.sigmoid(res)
        # referent_frames1 = referent_frames1 * torch.sigmoid(res)

        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)

        fusion_frames = self.dcn(fusion_frames)
        fusion_frames = self.bn(fusion_frames)
        fusion_frames = self.relu(fusion_frames)

        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames

class cross_fusion(nn.Module):
    def __init__(self, in_channels, decoder_channel, frame):
        super(cross_fusion, self).__init__()

        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=in_channels, out_channels=int(in_channels*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.BatchNorm2d(in_channels*frame),
            nn.ReLU(inplace=True),
        )

        self.up = nn.Sequential(
            # nn.PixelShuffle(upscale_factor=4),
            nn.Conv2d(in_channels=decoder_channel*frame, out_channels=decoder_channel, kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(decoder_channel),
            nn.ReLU(inplace=True),
            nn.UpsamplingBilinear2d(scale_factor=2)
        )
 
    def forward(self, time_serise_feature):
        B, frame, C, H, W = time_serise_feature.shape

        time_serise_feature = time_serise_feature.view(B, frame, C, H*W).permute(0, 2, 3, 1)
        time_serise_feature = self.conv1(time_serise_feature)
        time_serise_feature = time_serise_feature.view(B, frame*C, H*W, 1).squeeze(-1).view(B, frame*C, H, W)
        fusion_feature = self.up(time_serise_feature)

        return fusion_feature


class HRNet_S2(nn.Module):
    def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2, post_input="S2"):
        super(HRNet_S2, self).__init__()
        self.num_classes = num_classes
        self.post_input = post_input

        self.high_hrnet = HRNet_single(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BuildingDamage/result/xBD_512/S2_HRNet2_single/27.pt"
        self.high_hrnet = self._load_weight(self.high_hrnet, pre_train_model_path)

        self.s2_backbone, self.s2_decoder_layer = self._construct_low_backbone(s2_inchannel, num_classes)

        decoder_channel = self.s2_backbone.feature_info.channels()[0]

        # 提取分支
        self.dcn_guide = DCN_guide(decoder_channel)
        self.cross_fusion = cross_fusion(in_channels=decoder_channel, decoder_channel=decoder_channel, frame=frame)
        self.bres = residual_up_decoder(decoder_channel)
        self.s2_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel*2, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(decoder_channel, momentum= 0.1),
            nn.ReLU(inplace=True),
        )
        self.s2_class_layer = nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)

        # 损毁分支
        self.damage_dcn_guide = Res_DCN_guide(decoder_channel)
        self.dcross_fusion = cross_fusion(in_channels=decoder_channel, decoder_channel=decoder_channel, frame=frame)
        self.dres = residual_up_decoder(decoder_channel)
        self.damage_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel*2, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(decoder_channel, momentum= 0.1),
            nn.ReLU(inplace=True),
        )
        self.damage_layer = nn.Conv2d(in_channels=decoder_channel, out_channels=damage_classes, kernel_size=1, stride=1, padding=0)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net

    def _construct_low_backbone(self, inchannels, num_classes):
        net = HRNet_single(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BuildingDamage/result/xBD_512/S2_HRNet2_single/27.pt"
        net = self._load_weight(net, pre_train_model_path)
        backbone = net.rgb_backbone
        backbone.conv1.stride = 1
        backbone.conv2.stride = 1
        conv1_weights = backbone.conv1.weight
        conv1_weights_mean = conv1_weights.mean(dim=1, keepdim=True)
        new_conv1_weights = conv1_weights_mean.repeat(1, inchannels, 1, 1)
        backbone.conv1.in_inchannel = inchannels
        backbone.conv1.weight.data = new_conv1_weights        
        return backbone, net.rgb_decoder_layer

    def decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x

    def forward(self, inputs, device):
        output = {}
        output = self.high_hrnet(inputs[0].to(device).to(torch.float32), device, mode="pre")
        B, _, H, W = inputs[0].to(device).to(torch.float32).shape

        s2_images = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
        B, frame, C, h, w = s2_images.shape
        s2_images = s2_images.view(B*frame, C, h, w)
        low_encoder_features = self.s2_backbone(s2_images)
        low_decoder_features = self.decoder(low_encoder_features, self.s2_decoder_layer).view(B, frame, -1, h, w)
        high_features_down2 = F.interpolate(output["pre_high_features"], size=(H//4, W//4), mode='bilinear', align_corners=True)
        pre_building_map = F.interpolate(torch.sigmoid(output["pre_high_building"]), size=(H//4, W//4), mode='bilinear', align_corners=True)[:, 1:]

        # 第1步：P(St|LI0:t,HI): 得到灾前的建筑物结果
        # print(low_decoder_features[:, :-1].shape)
        low_pre_features2 = self.dcn_guide(current_frames=low_decoder_features[:, -2], referent_frames=low_decoder_features[:, :-1])
        low_pre_features2 = self.cross_fusion(low_pre_features2)
        low_pre_features2 = self.s2_fusion(torch.cat((low_pre_features2*pre_building_map, high_features_down2*pre_building_map), dim=1))
        low_pre_features2 = self.bres(low_pre_features2)
        low_outs = self.s2_class_layer(low_pre_features2)
        low_outs = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)
        output["pre_low_building"] = low_outs
        output["pre_low_features"] = low_pre_features2
        pre_building_map = F.interpolate(torch.sigmoid(output["pre_low_building"]), size=(H//4, W//4), mode='bilinear', align_corners=True)[:, 1:]

        # 第2步：P(LIt+1|LI0:t, HI, LIt+1): 得到灾后的损毁建筑特征
        # low_res_decoder_features = low_decoder_features[:, :-1] - low_decoder_features[:, -1:]
        # low_decoder_features = self.residual_linear_transformer(low_decoder_features[:, :-1], low_decoder_features[:, -1:])

        low_post_features2 = self.damage_dcn_guide(current_frames=low_decoder_features[:, -1], referent_frames=low_decoder_features[:, :-1])
        low_post_features2 = self.dcross_fusion(low_post_features2)
        low_post_features2 = self.damage_fusion(torch.cat((low_post_features2*pre_building_map, low_pre_features2*pre_building_map), dim=1))
        low_post_features2 = self.dres(low_post_features2)
        low_outs = self.damage_layer(low_post_features2*pre_building_map)
        low_outs = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)
        output["post_low_damage"] = low_outs
        output["post_low_features"] = low_post_features2
        return output


if __name__ == "__main__":
    # deep_model = HRNet_single(num_classes=2).cuda()
    deep_model = HRNet_S2(s2_inchannel=17, num_classes=2, frame=16, post_input="S2").cuda()
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384).cuda()
    low_images1 = torch.rand(bs, 16, 17, 48, 48).cuda()
    low_images2 = torch.rand(bs, 1, 17, 48, 48).cuda()
    pre_label = torch.rand(bs, 384, 384).cuda()
    post_label = torch.rand(bs, 384, 384).cuda()
    inputs = [hight_image, low_images1, low_images2, pre_label, post_label]
    device = torch.device("cuda")

    outputs = deep_model(inputs, device)


    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
