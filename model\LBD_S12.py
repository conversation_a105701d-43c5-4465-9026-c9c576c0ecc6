import math
from thop import profile
import torch
import torch.nn as nn
import timm
import numpy as np
import torch.nn.functional as F
from einops import rearrange
from dcn_v2 import DCN

class ConvBNReLU(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=kernel_size, padding=kernel_size//2),
            nn.BatchNorm2d(out_channels),
            nn.LeakyReLU(),
        )
    def forward(self, x):
        x = self.conv(x)
        return x
    

class HRNet(nn.Module):
    def __init__(self, num_classes=3):
        super(HRNet, self).__init__()
        self.num_classes = num_classes
        self.rgb_backbone = timm.create_model('hrnet_w48', pretrained=False, features_only=True, out_indices=(1, 2, 3, 4))
        self.pre_train_model_path = "./net/hrnetv2_w48_imagenet_pretrained.pth"
        encoder_channel = self.rgb_backbone.feature_info.channels()
        decoder_channel = self.rgb_backbone.feature_info.channels()[0]
        last_inp_channels = np.int32(np.sum(self.rgb_backbone.feature_info.channels()))

        self.rgb_decoder_layer = nn.Sequential(
            nn.Conv2d(in_channels=last_inp_channels, out_channels=decoder_channel, kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(decoder_channel, momentum= 0.1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(decoder_channel, momentum= 0.1),
            nn.ReLU(inplace=True),
        )
        self.class_layer1 = nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)

    def _load_weight(self, net):
        if self.pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.pre_train_model_path)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
    
    def decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x

    def forward(self, images, device, mode="pre", output={}):
        B, C, H, W = images.shape
        high_features = self.rgb_backbone(images.to(device).to(torch.float32))
        high_features = self.decoder(high_features, self.rgb_decoder_layer)
        high_outs = self.class_layer1(high_features)
        high_outs = F.interpolate(high_outs, size=(H, W), mode='bilinear', align_corners=True)
        output[f"{mode}_high_building"] = high_outs
        output[f"{mode}_high_features"] = high_features
        return output



class DCN_Fusion(nn.Module):
    def __init__(self, in_channels, decoder_channel, frame):
        super(DCN_Fusion, self).__init__()

        self.dcn = DCN(in_channels*frame, in_channels*frame, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=2)

        # conv1 conv2 是 Google 的论文，后期可以替换为 LTE 方式
        self.conv1 = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel*frame, kernel_size=(1, frame), stride=(1, 1)),
            nn.BatchNorm2d(decoder_channel*frame),
            nn.ReLU(inplace=True),
        )
        self.conv2 = nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=1, stride=1)


        self.conv3 = ConvBNReLU(in_channels=decoder_channel*frame, out_channels=decoder_channel, kernel_size=1)


    def forward(self, s2_time_serise_feature):
        B, frame, C, H, W = s2_time_serise_feature.shape

        fusion_frames = rearrange(s2_time_serise_feature, "b t c h w -> b (t c) h w")
        fusion_frames = self.dcn(fusion_frames.contiguous())
        fusion_frames = s2_time_serise_feature + rearrange(fusion_frames, "b (t c) h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        fusion_frames1 = fusion_frames.view(B, frame, C, H*W).permute(0, 2, 3, 1)
        fusion_frames1 = self.conv1(fusion_frames1)
        fusion_frames1 = fusion_frames1.view(B, frame*C, H*W, 1).squeeze(-1).view(B, frame, C, H, W).view(B*frame, C, H, W)
        fusion_frames1 = self.conv2(fusion_frames1)
        fusion_frames = fusion_frames + fusion_frames1.view(B, frame, C, H, W)

        fusion_frames = self.conv3(fusion_frames.view(B, frame*C, H, W))

        return fusion_frames


class MMSF(nn.Module):
    def __init__(self, decoder_channel):
        super(MMSF, self).__init__()

        self.conv1 = nn.Conv2d(in_channels=decoder_channel*2, out_channels=decoder_channel, kernel_size=1)
        self.conv2 = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel*2, out_channels=decoder_channel, kernel_size=1),
            ConvBNReLU(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3)
        )
        self.conv3 = ConvBNReLU(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3)


    def rank(self, mask):
        prob_map = torch.sigmoid(mask)[:, 1:]
        rank_map = torch.zeros_like(prob_map)

        rank_map[prob_map>0.0] = 1
        rank_map[prob_map>0.1] = 2
        rank_map[prob_map>0.2] = 3
        rank_map[prob_map>0.3] = 4
        rank_map[prob_map>0.4] = 5
        rank_map[prob_map>0.5] = 6
        rank_map[prob_map>0.6] = 7
        rank_map[prob_map>0.7] = 8
        rank_map[prob_map>0.8] = 9
        rank_map[prob_map>0.9] = 10

        return rank_map

    def forward(self, high_feature, low_feature, mask):
        mask = F.interpolate(mask, high_feature.size()[2:], mode='bilinear', align_corners=True)
        low_feature = F.interpolate(low_feature, high_feature.size()[2:], mode='bilinear', align_corners=True)
        rank_map = self.rank(mask)
        # print(high_feature.shape, low_feature.shape, rank_map.shape)
        x = torch.cat((high_feature*rank_map, low_feature*rank_map), 1)
        x1 = self.conv1(x)
        x2 = self.conv2(x)
        x = self.conv3(x1 + x2)
        return x

class Damage_Feature(nn.Module):
    def __init__(self, decoder_channel=128, num_heads=8, height=32, width=32):
        super(Damage_Feature, self).__init__()
        self.attn = nn.MultiheadAttention(embed_dim=decoder_channel, num_heads=num_heads, dropout=0.1, batch_first=True)
        self.conv = ConvBNReLU(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3)

        # 生成正弦位置编码
        self.register_buffer("positional_encoding", self.sinusoidal_encoding(height * width, decoder_channel))

    def sinusoidal_encoding(self, num_positions, embed_dim):
        pe = torch.zeros(num_positions, embed_dim)
        position = torch.arange(0, num_positions, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, embed_dim, 2).float() * (-math.log(10000.0) / embed_dim))
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        return pe.unsqueeze(0)

    def forward(self, pre_feature, post_feature):
        B, C, H, W = pre_feature.shape

        # pre_feature1 = pre_feature.flatten(2).permute(0, 2, 1) + self.positional_encoding
        # post_feature1 = post_feature.flatten(2).permute(0, 2, 1) + self.positional_encoding

        # damage_feature, _ = self.attn(pre_feature1, post_feature1, post_feature1)
        # damage_feature = damage_feature.permute(0, 2, 1).view(B, C, H, W)

        damage_feature = self.conv(pre_feature - post_feature)

        return damage_feature
    

class LBD_S2(nn.Module):
    def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2, post_input="S2"):
        super(LBD_S2, self).__init__()
        self.num_classes = num_classes
        self.post_input = post_input

        self.high_hrnet = HRNet(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BuildingDamage/result/xBD_512/S2_HRNet2_single/27.pt"
        self.high_hrnet = self._load_weight(self.high_hrnet, pre_train_model_path)

        self.s2_net, self.s2_backbone, self.s2_decoder_layer_list = self._construct_low_backbone(s2_inchannel, num_classes)
        decoder_channel = self.s2_backbone.feature_info.channels()[0]


        # 灾前S2时序数据融合分支
        self.pre_time_fusion = DCN_Fusion(in_channels=decoder_channel, decoder_channel=decoder_channel, frame=frame)
        # 灾前高分和灾前S2多尺度融合分支
        self.pre_scale_fusion = MMSF(decoder_channel)
        self.pre_s2_class = nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)

        # 灾后损毁特征构建
        self.post_time_feature = Damage_Feature(decoder_channel=decoder_channel, width=48, height=48)
        # 灾前高分和灾后
        self.post_scale_fusion = MMSF(decoder_channel)
        self.post_s2_class = nn.Conv2d(in_channels=decoder_channel, out_channels=damage_classes, kernel_size=1, stride=1, padding=0)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net

    def _construct_low_backbone(self, inchannels, num_classes):
        net = HRNet(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BuildingDamage/result/xBD_512/S2_HRNet2_single/27.pt"
        net = self._load_weight(net, pre_train_model_path)
        backbone = net.rgb_backbone
        backbone.conv1.stride = 1
        backbone.conv2.stride = 1
        conv1_weights = backbone.conv1.weight
        conv1_weights_mean = conv1_weights.mean(dim=1, keepdim=True)
        new_conv1_weights = conv1_weights_mean.repeat(1, inchannels, 1, 1)
        backbone.conv1.in_inchannel = inchannels
        backbone.conv1.weight.data = new_conv1_weights        
        return net, backbone, net.rgb_decoder_layer

    def forward(self, inputs, device):
        output = {}
        # 1. 获取灾前高分特征和分割结果
        output = self.high_hrnet(inputs[0].to(device).to(torch.float32), device, mode="pre")
        B, _, H, W = inputs[0].to(device).to(torch.float32).shape
        high_features_down4 = F.interpolate(output["pre_high_features"], size=(H//4, W//4), mode='bilinear', align_corners=True)

        # 1. 获取灾前低分时序特征
        s2_images = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
        B, frame, C, h, w = s2_images.shape
        s2_images = s2_images.view(B*frame, C, h, w)
        low_encoder_features = self.s2_backbone(s2_images)
        low_decoder_features = self.s2_net.decoder(low_encoder_features, self.s2_decoder_layer_list).view(B, frame, -1, h, w)

        # 2. P(St|LI0:t,HI): 融合灾前时序特征
        low_pre_features = self.pre_time_fusion(low_decoder_features[:, :-1])

        # 3. 多尺度数据融合
        low_pre_features2 = self.pre_scale_fusion(high_features_down4, low_pre_features, output["pre_high_building"])

        # 4. 灾前时序结果
        pre_low_outs = self.pre_s2_class(low_pre_features2)
        output["pre_low_building"] = F.interpolate(pre_low_outs, size=(H, W), mode='bilinear', align_corners=True)
        output["pre_low_features"] = low_pre_features


        # 5. 灾后损毁特征构建
        low_post_features = self.post_time_feature(low_pre_features, low_decoder_features[:, -1:])
        low_post_features2 = self.post_scale_fusion(high_features_down4, low_post_features, output["pre_low_building"])
        post_low_outs = self.post_s2_class(low_post_features2)
        output["post_low_damage"] = F.interpolate(post_low_outs, size=(H, W), mode='bilinear', align_corners=True)
        output["post_low_features"] = low_post_features2

        # print(low_post_features.shape)
        return output
    

if __name__ == "__main__":
    deep_model = LBD_S2(num_classes=2).cuda()
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384).cuda()
    low_images1 = torch.rand(bs, 16, 17, 48, 48).cuda()
    low_images2 = torch.rand(bs, 1, 17, 48, 48).cuda()
    pre_label = torch.rand(bs, 384, 384).cuda()
    post_label = torch.rand(bs, 384, 384).cuda()
    inputs = [hight_image, low_images1, low_images2]
    device = torch.device("cuda")

    outputs = deep_model(inputs, device)


    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
