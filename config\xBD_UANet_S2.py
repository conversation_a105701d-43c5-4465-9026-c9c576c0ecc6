import torch
from torch import nn
import os
from glob import glob
import albumentations as albu
import argparse
from pathlib import Path
import numpy as np
# from model.LBD_S12v15 import HRNet_S2, HRNet_single
# from model.PVTv2 import PVTv2_Segmentation
# from model.UANet import UANet_pvt
from model.LBD_S12v23 import LBDv23_S2
from loss.loss import OhemCELoss
from dataset.xBD import xBD_Dataset, dataset_collate

# =============================训练参数============================ #
batch_size = 5
lr = 1e-4
size = 384
epochs = 100
output_building = 2
output_damage = 2
data_name = "xBD_512"
model_name = "UANet_pvt_s2"
save_path = os.path.join("./result", data_name, model_name)
os.makedirs(save_path, exist_ok=True)

# 模型加载
device = 'cuda'
model = LBDv23_S2(backbone="pvt_v2_b2_s2", s2_inchannel=17, frame=16, num_classes=2, damage_classes=2)
pre_train_model_path = None
pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt_s2/13.pt"

# 损失函数
criterion1 = OhemCELoss(thresh=0.7, n_min=batch_size*size*size//16, ignore_index=255)
criterion2 = nn.L1Loss()
criterion3 = nn.KLDivLoss(reduction="mean")

# 数据集加载
base_path = "/mnt/d2/zxq/BuildingDamage/dataset/xBD_512"
train_dataset = xBD_Dataset('train', base_path)
val_dataset = xBD_Dataset('test', base_path)
collate_fn = dataset_collate
# =============================训练参数============================ #
