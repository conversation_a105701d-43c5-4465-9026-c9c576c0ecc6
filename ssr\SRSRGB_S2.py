import warnings
warnings.filterwarnings('ignore')

import os
import math
import random
import numpy as np
from thop import profile
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils import spectral_norm
from functools import partial

from einops import rearrange
from dcn_v2 import DCN, DCN_offset

import sys
sys.path.append("/mnt/d2/zxq/BDS12")
from model.UANet_modify import *

class ResidualDenseBlock(nn.Module):
    """Residual Dense Block.

    Used in RRDB block in ESRGAN.

    Args:
        num_feat (int): Channel number of intermediate features.
        num_grow_ch (int): Channels for each growth.
    """

    def __init__(self, num_feat=64, num_grow_ch=32):
        super(ResidualDenseBlock, self).__init__()
        self.conv1 = nn.Conv2d(num_feat, num_grow_ch, 3, 1, 1)
        self.conv2 = nn.Conv2d(num_feat + num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv3 = nn.Conv2d(num_feat + 2 * num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv4 = nn.Conv2d(num_feat + 3 * num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv5 = nn.Conv2d(num_feat + 4 * num_grow_ch, num_feat, 3, 1, 1)

        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)

    def forward(self, x):
        x1 = self.lrelu(self.conv1(x))
        x2 = self.lrelu(self.conv2(torch.cat((x, x1), 1)))
        x3 = self.lrelu(self.conv3(torch.cat((x, x1, x2), 1)))
        x4 = self.lrelu(self.conv4(torch.cat((x, x1, x2, x3), 1)))
        x5 = self.conv5(torch.cat((x, x1, x2, x3, x4), 1))
        # Empirically, we use 0.2 to scale the residual for better performance
        return x5 * 0.2 + x

class RRDB(nn.Module):
    """Residual in Residual Dense Block.

    Used in RRDB-Net in ESRGAN.

    Args:
        num_feat (int): Channel number of intermediate features.
        num_grow_ch (int): Channels for each growth.
    """

    def __init__(self, num_feat, num_grow_ch=32):
        super(RRDB, self).__init__()
        self.rdb1 = ResidualDenseBlock(num_feat, num_grow_ch)
        self.rdb2 = ResidualDenseBlock(num_feat, num_grow_ch)
        self.rdb3 = ResidualDenseBlock(num_feat, num_grow_ch)

    def forward(self, x):
        out = self.rdb1(x)
        out = self.rdb2(out)
        out = self.rdb3(out)
        # Empirically, we use 0.2 to scale the residual for better performance
        return out * 0.2 + x

def make_layer(basic_block, num_basic_block, **kwarg):
    """Make layers by stacking the same blocks.

    Args:
        basic_block (nn.module): nn.module class for basic block.
        num_basic_block (int): number of blocks.

    Returns:
        nn.Sequential: Stacked blocks in nn.Sequential.
    """
    layers = []
    for _ in range(num_basic_block):
        layers.append(basic_block(**kwarg))
    return nn.Sequential(*layers)

class RRDBNet(nn.Module):
    """Networks consisting of Residual in Residual Dense Block, which is used in ESRGAN.

    ESRGAN: Enhanced Super-Resolution Generative Adversarial Networks.

    We extend ESRGAN for scale x2 and scale x1.
    Note: This is one option for scale 1, scale 2 in RRDBNet.
    We first employ the pixel-unshuffle (an inverse operation of pixelshuffle to reduce the spatial size
    and enlarge the channel size before feeding inputs into the main ESRGAN architecture.

    Args:
        num_in_ch (int): Channel number of inputs.
        num_out_ch (int): Channel number of outputs.
        num_feat (int): Channel number of intermediate features.
            Default: 64
        num_block (int): Block number in the trunk network. Defaults: 23
        num_grow_ch (int): Channels for each growth. Default: 32.
    """

    def __init__(self, num_in_ch=17, num_feat=64, num_block=23, num_grow_ch=32):
        super(RRDBNet, self).__init__()
        self.conv_first = nn.Conv2d(num_in_ch, num_feat, 3, 1, 1)
        self.body = make_layer(RRDB, num_block, num_feat=num_feat, num_grow_ch=num_grow_ch)
        self.conv_body = nn.Conv2d(num_feat, num_feat, 3, 1, 1)

        self.conv_up1 = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_up2 = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_hr = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_last = nn.Conv2d(num_feat, 3, 3, 1, 1)
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)

    def forward(self, x):
        feat = self.conv_first(x)
        body_feat = self.conv_body(self.body(feat))
        feat = feat + body_feat

        # cancle the nearest upsampling x4
        feat = self.lrelu(self.conv_up1(feat))
        feat = self.lrelu(self.conv_up2(feat))
        feat = self.lrelu(self.conv_hr(feat))
        return feat

class DCN_guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.dcn = DCN_offset(decoder_channel*2, decoder_channel, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
        self.LeakyReLU = nn.LeakyReLU(inplace=True)

        self.global_offsets = nn.Conv2d(8, 2, kernel_size=3, stride=1, padding=1, bias=True,)

    def forward(self, current_frames, referent_frames, angles):
        B, frame, C, H, W = referent_frames.shape
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

        assert current_frames.shape == referent_frames.shape

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")
        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)


        current_angle1 = rearrange(angles[:, -1].unsqueeze(1).repeat(1, frame, 1, 1, 1), "b t c h w -> (b t) c h w")
        referent_angle1 = rearrange(angles, "b t c h w -> (b t) c h w")
        fusion_angles = torch.cat((current_angle1, referent_angle1), dim=1)
        global_offset = self.global_offsets(fusion_angles)
        # global_offset = global_offset

        # print(global_offset.shape)

        fusion_frames = self.dcn(fusion_frames, global_offset)
        fusion_frames = self.LeakyReLU(fusion_frames)

        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames

class time_fusion(nn.Module):
    def __init__(self, decoder_channel, frame):
        super().__init__()
        self.conv_time_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=int(decoder_channel*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.LeakyReLU(inplace=True),
        )
        self.conv_spatial_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(inplace=True),
        )

    def forward(self, x):
        B, frame, C, H, W = x.shape
        x = rearrange(x, "b t c h w -> b c (h w) t", b=B, t=frame, c=C, h=H, w=W)
        fusion_frames = rearrange(self.conv_time_fusion(x).squeeze(-1), "b (t c) (h w) -> (b t) c h w", b=B, t=frame, c=C, h=H, w=W)
        fusion_frames = rearrange(self.conv_spatial_fusion(fusion_frames), "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)
        return fusion_frames

class SuperResolution(nn.Module):
    def __init__(self, decoder_channel, frame):
        super().__init__()

        self.ts = time_fusion(decoder_channel, frame)
        self.sr = nn.Sequential(           
            nn.PixelShuffle(upscale_factor=4),
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(inplace=True),
        )

    def forward(self, x):
        x = self.ts(x)
        x = self.sr(rearrange(x, "b t c h w -> b (t c) h w"))
        return x

class S2_Building_Damage(nn.Module):
    def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        decoder_channel = 64

        self.s2_backbone = RRDBNet(num_in_ch=s2_inchannel, num_feat=decoder_channel, num_block=23, num_grow_ch=32)

        self.fcn = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])
        self.offset_guide = DCN_guide(decoder_channel)

        self.sr_net = SuperResolution(decoder_channel, frame=frame)
        self.s2_last_layer = make_layer(RRDB, 1, num_feat=decoder_channel, num_grow_ch=32)

        self.conv_up1 = nn.Conv2d(decoder_channel, decoder_channel, 3, 1, 1)
        self.conv_up2 = nn.Conv2d(decoder_channel, decoder_channel, 3, 1, 1)
        self.conv_hr = nn.Conv2d(decoder_channel, decoder_channel, 3, 1, 1)
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)


        self.building_layer = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=1, stride=1, padding=0),
            nn.LeakyReLU(),
            nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes+3, kernel_size=1, stride=1, padding=0)
        )

    def _feature_transfer(self, time_serise_feature):
        B, total_frame, C, H, W = time_serise_feature.shape
        # print("shape is", B, total_frame, C, H, W)
        aligned_features = []
        T15 = time_serise_feature[:, -2]
        
        for t in range(total_frame):  # 对于T1到T17
            curr_feat = time_serise_feature[:, t]  # [B, C, H, W]
            feat_pair = torch.cat([curr_feat, T15], dim=1)
            A = self.fcn[t](feat_pair)
            aligned_feat = torch.mul(curr_feat, A).squeeze(1)
            aligned_features.append(aligned_feat)
        aligned_features = torch.stack(aligned_features, dim=1) # [B, 17, C, H, W]
        return aligned_features
 
    def _encoder_feature_constraint(self, inputs, device, low_decoder_features, outputs):
        B, frame, _, h, w = low_decoder_features.shape
        outputs["loss"] = 0
        for frame_id in range(frame - 2):
            frame_feature = F.normalize(low_decoder_features[:, frame_id])
            outputs["loss"] += F.mse_loss(frame_feature, F.normalize(low_decoder_features[:, -2].detach()), reduction='mean')
        return outputs

    def forward(self, inputs, device, outputs, H, W):
        s2_images = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
        B, frame, C, h, w = s2_images.shape
        s2_angles = s2_images[:, :, 13:]
        s2_images = s2_images[:, :, :13]

        low_encoder_features = self.s2_backbone(s2_images.view(B*frame, -1, h, w))
        low_encoder_features = low_encoder_features.view(B, frame, -1, h, w)
        low_encoder_features = self._feature_transfer(low_encoder_features)
        low_pre_features_offset = self.offset_guide(current_frames=low_encoder_features[:, -2], referent_frames=low_encoder_features[:, :-1], angles=s2_angles[:, :-1])

        if self.training:
            outputs = self._encoder_feature_constraint(inputs, device, low_encoder_features, outputs)

        low_pre_features = self.sr_net(low_pre_features_offset)
        low_pre_features = self.s2_last_layer(low_pre_features)


        # add the nearest upsampling x4 
        low_pre_features = self.lrelu(self.conv_up1(F.interpolate(low_pre_features, scale_factor=2, mode='nearest')))
        low_pre_features = self.lrelu(self.conv_up2(F.interpolate(low_pre_features, scale_factor=2, mode='nearest')))
        low_pre_features = self.lrelu(self.conv_hr(low_pre_features))

        low_outs = self.building_layer(low_pre_features)
        low_outs = F.interpolate(low_outs, scale_factor=1.25, mode='bilinear', align_corners=True)
        low_pre_features = F.interpolate(low_pre_features, scale_factor=1.25, mode='bilinear', align_corners=True)
        # outputs["pre_low_building"] = low_outs[:, :2]
        # outputs["low_all_rgb_out"] = low_outs[:, 2:]
        # outputs["pre_low_features"] = low_pre_features

        # return outputs

        # outputs["pre_low_gray"] = torch.clamp(outputs["pre_low_gray"], 0.0, 1.0)
        # print(low_outs.shape)

        shfit_dx_dy = inputs[5].to(device).to(torch.long)
        crop_size = 384
        cropped_outs = torch.zeros((B, 5, crop_size, crop_size), dtype=low_outs.dtype, device=low_outs.device)
        cropped_feat = torch.zeros((B, 64, crop_size, crop_size), dtype=low_outs.dtype, device=low_outs.device)

        for i in range(B):
            dy, dx = shfit_dx_dy[i]
            # dy, dx = int(dy), int(dx)  # 保证是整数
            cropped_outs[i] = low_outs[i, :, dy:dy+crop_size, dx:dx+crop_size]
            cropped_feat[i] = low_pre_features[i, :, dy:dy+crop_size, dx:dx+crop_size]

        outputs["pre_low_building"] = cropped_outs[:, :2]
        outputs["low_all_rgb_out"] = cropped_outs[:, 2:]
        outputs["pre_low_features"] = cropped_feat

        return outputs

class UNetDiscriminatorSN(nn.Module):
    """Defines a U-Net discriminator with spectral normalization (SN)

    It is used in Real-ESRGAN: Training Real-World Blind Super-Resolution with Pure Synthetic Data.

    Arg:
        num_in_ch (int): Channel number of inputs. Default: 3.
        num_feat (int): Channel number of base intermediate features. Default: 64.
        skip_connection (bool): Whether to use skip connections between U-Net. Default: True.
    """

    def __init__(self, num_in_ch, num_feat=64, skip_connection=True):
        super(UNetDiscriminatorSN, self).__init__()
        self.skip_connection = skip_connection
        norm = spectral_norm
        # the first convolution
        self.conv0 = nn.Conv2d(num_in_ch, num_feat, kernel_size=3, stride=1, padding=1)
        # downsample
        self.conv1 = norm(nn.Conv2d(num_feat, num_feat * 2, 4, 2, 1, bias=False))
        self.conv2 = norm(nn.Conv2d(num_feat * 2, num_feat * 4, 4, 2, 1, bias=False))
        self.conv3 = norm(nn.Conv2d(num_feat * 4, num_feat * 8, 4, 2, 1, bias=False))
        # upsample
        self.conv4 = norm(nn.Conv2d(num_feat * 8, num_feat * 4, 3, 1, 1, bias=False))
        self.conv5 = norm(nn.Conv2d(num_feat * 4, num_feat * 2, 3, 1, 1, bias=False))
        self.conv6 = norm(nn.Conv2d(num_feat * 2, num_feat, 3, 1, 1, bias=False))
        # extra convolutions
        self.conv7 = norm(nn.Conv2d(num_feat, num_feat, 3, 1, 1, bias=False))
        self.conv8 = norm(nn.Conv2d(num_feat, num_feat, 3, 1, 1, bias=False))
        self.conv9 = nn.Conv2d(num_feat, 1, 3, 1, 1)

    def forward(self, x):
        # downsample
        x0 = F.leaky_relu(self.conv0(x), negative_slope=0.2, inplace=True)
        x1 = F.leaky_relu(self.conv1(x0), negative_slope=0.2, inplace=True)
        x2 = F.leaky_relu(self.conv2(x1), negative_slope=0.2, inplace=True)
        x3 = F.leaky_relu(self.conv3(x2), negative_slope=0.2, inplace=True)

        # upsample
        x3 = F.interpolate(x3, scale_factor=2, mode='bilinear', align_corners=False)
        x4 = F.leaky_relu(self.conv4(x3), negative_slope=0.2, inplace=True)

        if self.skip_connection:
            x4 = x4 + x2
        x4 = F.interpolate(x4, scale_factor=2, mode='bilinear', align_corners=False)
        x5 = F.leaky_relu(self.conv5(x4), negative_slope=0.2, inplace=True)

        if self.skip_connection:
            x5 = x5 + x1
        x5 = F.interpolate(x5, scale_factor=2, mode='bilinear', align_corners=False)
        x6 = F.leaky_relu(self.conv6(x5), negative_slope=0.2, inplace=True)

        if self.skip_connection:
            x6 = x6 + x0

        # extra convolutions
        out = F.leaky_relu(self.conv7(x6), negative_slope=0.2, inplace=True)
        out = F.leaky_relu(self.conv8(out), negative_slope=0.2, inplace=True)
        out = self.conv9(out)

        return out


class LBD_S12v27(nn.Module):
    def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        self.high_net = UANet_pvt(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/13.pt"
        self.high_net = self._load_weight(self.high_net, pre_train_model_path)
        for p in self.high_net.parameters():
            p.requires_grad = False

        self.low_net = S2_Building_Damage(s2_inchannel=s2_inchannel, frame=frame, num_classes=num_classes, damage_classes=damage_classes)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
 

    def forward(self, inputs, device):
        outputs = {}
        with torch.no_grad():
            B, _, H, W = inputs[0].to(device).to(torch.float32).shape
            outputs = self.high_net(inputs[0].to(device).to(torch.float32), device, mode="pre", outputs=outputs)
        outputs = self.low_net(inputs, device, outputs, H, W)

        return outputs



# ================================
# MAE (Masked Autoencoder) Components for Sentinel-2
# ================================

def drop_path(x, drop_prob: float = 0., training: bool = False):
    """Drop paths (Stochastic Depth) per sample (when applied in main path of residual blocks)."""
    if drop_prob == 0. or not training:
        return x
    keep_prob = 1 - drop_prob
    shape = (x.shape[0],) + (1,) * (x.ndim - 1)  # work with diff dim tensors, not just 2D ConvNets
    random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
    random_tensor.floor_()  # binarize
    output = x.div(keep_prob) * random_tensor
    return output

class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample  (when applied in main path of residual blocks)."""
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        return drop_path(x, self.drop_prob, self.training)

class Mlp(nn.Module):
    """MLP module for Transformer."""
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x

class MAEAttention(nn.Module):
    """Multi-head Attention for MAE."""
    def __init__(self, dim, num_heads=8, qkv_bias=False, qk_scale=None, attn_drop=0., proj_drop=0.):
        super().__init__()
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = qk_scale or head_dim ** -0.5

        self.qkv = nn.Linear(dim, dim * 3, bias=qkv_bias)
        self.attn_drop = nn.Dropout(attn_drop)
        self.proj = nn.Linear(dim, dim)
        self.proj_drop = nn.Dropout(proj_drop)

    def forward(self, x):
        B, N, C = x.shape
        qkv = self.qkv(x).reshape(B, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]   # make torchscript happy (cannot use tensor as tuple)

        attn = (q @ k.transpose(-2, -1)) * self.scale
        attn = attn.softmax(dim=-1)
        attn = self.attn_drop(attn)

        x = (attn @ v).transpose(1, 2).reshape(B, N, C)
        x = self.proj(x)
        x = self.proj_drop(x)
        return x

class MAEBlock(nn.Module):
    """Transformer Block for MAE."""
    def __init__(self, dim, num_heads, mlp_ratio=4., qkv_bias=False, qk_scale=None, drop=0., attn_drop=0.,
                 drop_path=0., act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        self.norm1 = norm_layer(dim)
        self.attn = MAEAttention(
            dim, num_heads=num_heads, qkv_bias=qkv_bias, qk_scale=qk_scale, attn_drop=attn_drop, proj_drop=drop)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.norm2 = norm_layer(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)

    def forward(self, x):
        x = x + self.drop_path(self.attn(self.norm1(x)))
        x = x + self.drop_path(self.mlp(self.norm2(x)))
        return x

class PatchEmbed_S2_Temporal(nn.Module):
    """Temporal Sentinel-2 to Patch Embedding with patch_size=1."""
    def __init__(self, img_size=22, patch_size=1, in_chans=13, embed_dim=768, temporal_frames=16):
        super().__init__()
        img_size = (img_size, img_size) if isinstance(img_size, int) else img_size
        patch_size = (patch_size, patch_size) if isinstance(patch_size, int) else patch_size
        self.img_size = img_size
        self.patch_size = patch_size
        self.temporal_frames = temporal_frames
        self.in_chans = in_chans

        # For patch_size=1, each pixel becomes a patch
        self.grid_size = (img_size[0] // patch_size[0], img_size[1] // patch_size[1])
        self.num_patches_per_frame = self.grid_size[0] * self.grid_size[1]
        self.total_patches = self.num_patches_per_frame * temporal_frames

        # Project each temporal frame separately, then combine
        # Input: [B, T, C, H, W] -> [B, T*H*W, embed_dim]
        self.temporal_proj = nn.ModuleList([
            nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)
            for _ in range(temporal_frames)
        ])

        # Alternative: shared projection across time
        self.shared_proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=patch_size)

        # Temporal embedding to distinguish different time frames
        self.temporal_embed = nn.Parameter(torch.zeros(1, temporal_frames, 1, embed_dim))

    def forward(self, x):
        """
        Args:
            x: [B, T, C, H, W] where T=16, C=13, H=22, W=22
        Returns:
            patches: [B, T*H*W, embed_dim]
        """
        B, T, C, H, W = x.shape
        assert T == self.temporal_frames, f"Expected {self.temporal_frames} temporal frames, got {T}"
        assert C == self.in_chans, f"Expected {self.in_chans} channels, got {C}"
        assert H == self.img_size[0] and W == self.img_size[1], \
            f"Input image size ({H}*{W}) doesn't match model ({self.img_size[0]}*{self.img_size[1]})."

        # Process each temporal frame
        patches_list = []
        for t in range(T):
            # x[:, t] shape: [B, C, H, W]
            frame_patches = self.shared_proj(x[:, t]).flatten(2).transpose(1, 2)  # [B, H*W, embed_dim]

            # Add temporal embedding
            frame_patches = frame_patches + self.temporal_embed[:, t, :, :]  # [B, H*W, embed_dim]
            patches_list.append(frame_patches)

        # Concatenate all temporal patches
        patches = torch.cat(patches_list, dim=1)  # [B, T*H*W, embed_dim]

        return patches

class MaskGenerator(nn.Module):
    """Random mask generator for MAE."""
    def __init__(self, mask_ratio=0.75):
        super().__init__()
        self.mask_ratio = mask_ratio

    def forward(self, x, mask_ratio=None):
        """
        Perform per-sample random masking by per-sample shuffling.
        Per-sample shuffling is done by argsort random noise.
        x: [N, L, D], sequence
        """
        N, L, D = x.shape  # batch, length, dim
        if mask_ratio is None:
            mask_ratio = self.mask_ratio
        len_keep = int(L * (1 - mask_ratio))

        noise = torch.rand(N, L, device=x.device)  # noise in [0, 1]

        # sort noise for each sample
        ids_shuffle = torch.argsort(noise, dim=1)  # ascend: small is keep, large is remove
        ids_restore = torch.argsort(ids_shuffle, dim=1)

        # keep the first subset
        ids_keep = ids_shuffle[:, :len_keep]
        x_masked = torch.gather(x, dim=1, index=ids_keep.unsqueeze(-1).repeat(1, 1, D))

        # generate the binary mask: 0 is keep, 1 is remove
        mask = torch.ones([N, L], device=x.device)
        mask[:, :len_keep] = 0
        # unshuffle to get the binary mask
        mask = torch.gather(mask, dim=1, index=ids_restore)

        return x_masked, mask, ids_restore

class MAEEncoder_S2_Temporal(nn.Module):
    """MAE Encoder with Vision Transformer backbone for temporal Sentinel-2 data."""
    def __init__(self, img_size=22, patch_size=1, in_chans=13, temporal_frames=16, embed_dim=768, depth=12,
                 num_heads=12, mlp_ratio=4., qkv_bias=True, qk_scale=None, drop_rate=0., attn_drop_rate=0.,
                 drop_path_rate=0., norm_layer=None):
        super().__init__()
        norm_layer = norm_layer or partial(nn.LayerNorm, eps=1e-6)

        # temporal patch embedding for [B, T, C, H, W] input
        self.patch_embed = PatchEmbed_S2_Temporal(img_size, patch_size, in_chans, embed_dim, temporal_frames)
        num_patches = self.patch_embed.total_patches  # T * H * W

        # class token and position embedding
        self.cls_token = nn.Parameter(torch.zeros(1, 1, embed_dim))
        self.pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, embed_dim), requires_grad=False)  # fixed sin-cos embedding

        # mask generator
        self.mask_generator = MaskGenerator()

        # transformer blocks (standard ViT blocks)
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, depth)]  # stochastic depth decay rule
        self.blocks = nn.ModuleList([
            MAEBlock(embed_dim, num_heads, mlp_ratio, qkv_bias, qk_scale, drop_rate, attn_drop_rate, dpr[i], norm_layer=norm_layer)
            for i in range(depth)])
        self.norm = norm_layer(embed_dim)

        self.initialize_weights()

    def initialize_weights(self):
        # initialization
        # initialize (and freeze) pos_embed by sin-cos embedding
        pos_embed = self.get_2d_sincos_pos_embed(self.pos_embed.shape[-1], int(self.patch_embed.num_patches**.5), cls_token=True)
        self.pos_embed.data.copy_(torch.from_numpy(pos_embed).float().unsqueeze(0))

        # initialize patch_embed like nn.Linear (instead of nn.Conv2d)
        w = self.patch_embed.proj.weight.data
        torch.nn.init.xavier_uniform_(w.view([w.shape[0], -1]))

        # timm's trunc_normal_(std=.02) is effectively normal_(std=0.02) as cutoff is too big (2.)
        torch.nn.init.normal_(self.cls_token, std=.02)

        # initialize nn.Linear and nn.LayerNorm
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            # we use xavier_uniform following official JAX ViT:
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def get_2d_sincos_pos_embed(self, embed_dim, grid_size, cls_token=False):
        """
        grid_size: int of the grid height and width
        return:
        pos_embed: [grid_size*grid_size, embed_dim] or [1+grid_size*grid_size, embed_dim] (w/ or w/o cls_token)
        """
        grid_h = np.arange(grid_size, dtype=np.float32)
        grid_w = np.arange(grid_size, dtype=np.float32)
        grid = np.meshgrid(grid_w, grid_h)  # here w goes first
        grid = np.stack(grid, axis=0)

        grid = grid.reshape([2, 1, grid_size, grid_size])
        pos_embed = self.get_2d_sincos_pos_embed_from_grid(embed_dim, grid)
        if cls_token:
            pos_embed = np.concatenate([np.zeros([1, embed_dim]), pos_embed], axis=0)
        return pos_embed

    def get_2d_sincos_pos_embed_from_grid(self, embed_dim, grid):
        assert embed_dim % 2 == 0

        # use half of dimensions to encode grid_h
        emb_h = self.get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[0])  # (H*W, D/2)
        emb_w = self.get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[1])  # (H*W, D/2)

        emb = np.concatenate([emb_h, emb_w], axis=1) # (H*W, D)
        return emb

    def get_1d_sincos_pos_embed_from_grid(self, embed_dim, pos):
        """
        embed_dim: output dimension for each position
        pos: a list of positions to be encoded: size (M,)
        out: (M, D)
        """
        assert embed_dim % 2 == 0
        omega = np.arange(embed_dim // 2, dtype=np.float64)
        omega /= embed_dim / 2.
        omega = 1. / 10000**omega  # (D/2,)

        pos = pos.reshape(-1)  # (M,)
        out = np.einsum('m,d->md', pos, omega)  # (M, D/2), outer product

        emb_sin = np.sin(out) # (M, D/2)
        emb_cos = np.cos(out) # (M, D/2)

        emb = np.concatenate([emb_sin, emb_cos], axis=1)  # (M, D)
        return emb

    def forward_encoder(self, x, mask_ratio):
        """
        Args:
            x: [B, T, C, H, W] temporal Sentinel-2 data
            mask_ratio: ratio of patches to mask
        Returns:
            encoded features, mask, and restore indices
        """
        # embed patches: [B, T, C, H, W] -> [B, T*H*W, embed_dim]
        x = self.patch_embed(x)

        # add pos embed w/o cls token
        x = x + self.pos_embed[:, 1:, :]

        # masking: length -> length * mask_ratio
        x, mask, ids_restore = self.mask_generator(x, mask_ratio)

        # append cls token
        cls_token = self.cls_token + self.pos_embed[:, :1, :]
        cls_tokens = cls_token.expand(x.shape[0], -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)

        # apply Transformer blocks (standard ViT)
        for blk in self.blocks:
            x = blk(x)
        x = self.norm(x)

        return x, mask, ids_restore

    def forward(self, x, mask_ratio=0.75):
        """
        Args:
            x: [B, T, C, H, W] where T=16, C=13, H=22, W=22
        """
        latent, mask, ids_restore = self.forward_encoder(x, mask_ratio)
        return latent, mask, ids_restore

class MAEDecoder_S2_Temporal(nn.Module):
    """MAE Decoder for reconstructing masked temporal Sentinel-2 patches."""
    def __init__(self, num_patches, patch_size=1, in_chans=13, temporal_frames=16, embed_dim=512, decoder_embed_dim=512,
                 decoder_depth=8, decoder_num_heads=16, mlp_ratio=4., norm_layer=None):
        super().__init__()
        norm_layer = norm_layer or partial(nn.LayerNorm, eps=1e-6)

        self.num_patches = num_patches  # T * H * W
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.temporal_frames = temporal_frames

        self.decoder_embed = nn.Linear(embed_dim, decoder_embed_dim, bias=True)

        self.mask_token = nn.Parameter(torch.zeros(1, 1, decoder_embed_dim))

        self.decoder_pos_embed = nn.Parameter(torch.zeros(1, num_patches + 1, decoder_embed_dim), requires_grad=False)  # fixed sin-cos embedding

        self.decoder_blocks = nn.ModuleList([
            MAEBlock(decoder_embed_dim, decoder_num_heads, mlp_ratio, qkv_bias=True, norm_layer=norm_layer)
            for i in range(decoder_depth)])

        self.decoder_norm = norm_layer(decoder_embed_dim)
        # For patch_size=1, each patch reconstructs 1*1*C = C values per temporal frame
        self.decoder_pred = nn.Linear(decoder_embed_dim, patch_size**2 * in_chans, bias=True) # decoder to patch

        self.initialize_weights()

    def initialize_weights(self):
        # initialize (and freeze) pos_embed by sin-cos embedding
        decoder_pos_embed = self.get_2d_sincos_pos_embed(self.decoder_pos_embed.shape[-1], int(self.num_patches**.5), cls_token=True)
        self.decoder_pos_embed.data.copy_(torch.from_numpy(decoder_pos_embed).float().unsqueeze(0))

        # timm's trunc_normal_(std=.02) is effectively normal_(std=0.02) as cutoff is too big (2.)
        torch.nn.init.normal_(self.mask_token, std=.02)

        # initialize nn.Linear and nn.LayerNorm
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            # we use xavier_uniform following official JAX ViT:
            torch.nn.init.xavier_uniform_(m.weight)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)

    def get_2d_sincos_pos_embed(self, embed_dim, grid_size, cls_token=False):
        """
        grid_size: int of the grid height and width
        return:
        pos_embed: [grid_size*grid_size, embed_dim] or [1+grid_size*grid_size, embed_dim] (w/ or w/o cls_token)
        """
        grid_h = np.arange(grid_size, dtype=np.float32)
        grid_w = np.arange(grid_size, dtype=np.float32)
        grid = np.meshgrid(grid_w, grid_h)  # here w goes first
        grid = np.stack(grid, axis=0)

        grid = grid.reshape([2, 1, grid_size, grid_size])
        pos_embed = self.get_2d_sincos_pos_embed_from_grid(embed_dim, grid)
        if cls_token:
            pos_embed = np.concatenate([np.zeros([1, embed_dim]), pos_embed], axis=0)
        return pos_embed

    def get_2d_sincos_pos_embed_from_grid(self, embed_dim, grid):
        assert embed_dim % 2 == 0

        # use half of dimensions to encode grid_h
        emb_h = self.get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[0])  # (H*W, D/2)
        emb_w = self.get_1d_sincos_pos_embed_from_grid(embed_dim // 2, grid[1])  # (H*W, D/2)

        emb = np.concatenate([emb_h, emb_w], axis=1) # (H*W, D)
        return emb

    def get_1d_sincos_pos_embed_from_grid(self, embed_dim, pos):
        """
        embed_dim: output dimension for each position
        pos: a list of positions to be encoded: size (M,)
        out: (M, D)
        """
        assert embed_dim % 2 == 0
        omega = np.arange(embed_dim // 2, dtype=np.float64)
        omega /= embed_dim / 2.
        omega = 1. / 10000**omega  # (D/2,)

        pos = pos.reshape(-1)  # (M,)
        out = np.einsum('m,d->md', pos, omega)  # (M, D/2), outer product

        emb_sin = np.sin(out) # (M, D/2)
        emb_cos = np.cos(out) # (M, D/2)

        emb = np.concatenate([emb_sin, emb_cos], axis=1)  # (M, D)
        return emb

    def forward_decoder(self, x, ids_restore):
        # embed tokens
        x = self.decoder_embed(x)

        # append mask tokens to sequence
        mask_tokens = self.mask_token.repeat(x.shape[0], ids_restore.shape[1] + 1 - x.shape[1], 1)
        x_ = torch.cat([x[:, 1:, :], mask_tokens], dim=1)  # no cls token
        x_ = torch.gather(x_, dim=1, index=ids_restore.unsqueeze(-1).repeat(1, 1, x.shape[2]))  # unshuffle
        x = torch.cat([x[:, :1, :], x_], dim=1)  # append cls token

        # add pos embed
        x = x + self.decoder_pos_embed

        # apply Transformer blocks
        for blk in self.decoder_blocks:
            x = blk(x)
        x = self.decoder_norm(x)

        # predictor projection
        x = self.decoder_pred(x)

        # remove cls token
        x = x[:, 1:, :]

        return x

    def forward(self, latent, ids_restore):
        pred = self.forward_decoder(latent, ids_restore)
        return pred

class MAE_Sentinel2_Temporal(nn.Module):
    """Masked Autoencoder for temporal Sentinel-2 reconstruction pretraining."""
    def __init__(self, img_size=22, patch_size=1, in_chans=13, temporal_frames=16,
                 embed_dim=768, depth=12, num_heads=12,
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
                 mlp_ratio=4., norm_layer=None, norm_pix_loss=False):
        super().__init__()
        norm_layer = norm_layer or partial(nn.LayerNorm, eps=1e-6)

        # MAE encoder specifics for temporal data
        self.encoder = MAEEncoder_S2_Temporal(
            img_size=img_size, patch_size=patch_size, in_chans=in_chans, temporal_frames=temporal_frames,
            embed_dim=embed_dim, depth=depth, num_heads=num_heads,
            mlp_ratio=mlp_ratio, norm_layer=norm_layer)

        # MAE decoder specifics
        num_patches = self.encoder.patch_embed.total_patches
        self.decoder = MAEDecoder_S2_Temporal(
            num_patches=num_patches, patch_size=patch_size, in_chans=in_chans, temporal_frames=temporal_frames,
            embed_dim=embed_dim, decoder_embed_dim=decoder_embed_dim,
            decoder_depth=decoder_depth, decoder_num_heads=decoder_num_heads,
            mlp_ratio=mlp_ratio, norm_layer=norm_layer)

        self.norm_pix_loss = norm_pix_loss
        self.temporal_frames = temporal_frames
        self.img_size = img_size
        self.patch_size = patch_size

    def patchify(self, imgs):
        """
        imgs: (B, T, C, H, W) temporal Sentinel-2 data
        x: (B, T*H*W, patch_size**2 * C)
        For patch_size=1: (B, T*H*W, C)
        """
        B, T, C, H, W = imgs.shape
        p = self.patch_size
        assert H % p == 0 and W % p == 0

        # Reshape to patches
        h = H // p
        w = W // p

        # For each temporal frame, create patches
        patches_list = []
        for t in range(T):
            frame = imgs[:, t]  # [B, C, H, W]
            frame_patches = frame.reshape(B, C, h, p, w, p)
            frame_patches = torch.einsum('bchpwq->bhwpqc', frame_patches)
            frame_patches = frame_patches.reshape(B, h * w, p**2 * C)
            patches_list.append(frame_patches)

        # Concatenate temporal patches: [B, T*H*W, p*p*C]
        x = torch.cat(patches_list, dim=1)
        return x

    def unpatchify(self, x):
        """
        x: (B, T*H*W, patch_size**2 * C)
        imgs: (B, T, C, H, W)
        """
        B = x.shape[0]
        T = self.temporal_frames
        C = self.encoder.patch_embed.in_chans
        p = self.patch_size
        h = w = self.img_size // p

        patches_per_frame = h * w
        assert x.shape[1] == T * patches_per_frame

        # Split temporal patches and reconstruct each frame
        frames_list = []
        for t in range(T):
            start_idx = t * patches_per_frame
            end_idx = (t + 1) * patches_per_frame
            frame_patches = x[:, start_idx:end_idx, :]  # [B, H*W, p*p*C]

            frame_patches = frame_patches.reshape(B, h, w, p, p, C)
            frame_patches = torch.einsum('bhwpqc->bchpwq', frame_patches)
            frame = frame_patches.reshape(B, C, h * p, w * p)
            frames_list.append(frame)

        # Stack temporal frames: [B, T, C, H, W]
        imgs = torch.stack(frames_list, dim=1)
        return imgs

    def forward_loss(self, imgs, pred, mask):
        """
        imgs: [B, T, C, H, W] temporal Sentinel-2 data
        pred: [B, T*H*W, p*p*C] predicted patches
        mask: [B, T*H*W], 0 is keep, 1 is remove
        """
        target = self.patchify(imgs)
        if self.norm_pix_loss:
            mean = target.mean(dim=-1, keepdim=True)
            var = target.var(dim=-1, keepdim=True)
            target = (target - mean) / (var + 1.e-6)**.5

        loss = (pred - target) ** 2
        loss = loss.mean(dim=-1)  # [B, T*H*W], mean loss per patch

        loss = (loss * mask).sum() / mask.sum()  # mean loss on removed patches
        return loss

    def forward(self, imgs, mask_ratio=0.75):
        """
        Args:
            imgs: [B, T, C, H, W] where T=16, C=13, H=22, W=22
        Returns:
            loss, pred, mask
        """
        latent, mask, ids_restore = self.encoder(imgs, mask_ratio)
        pred = self.decoder(latent, ids_restore)  # [B, T*H*W, p*p*C]
        loss = self.forward_loss(imgs, pred, mask)
        return loss, pred, mask

class MAE_FineTune_BuildingDetection_Temporal(nn.Module):
    """Fine-tuning model for building detection using pretrained temporal MAE encoder."""
    def __init__(self, img_size=22, patch_size=1, in_chans=13, temporal_frames=16,
                 embed_dim=768, depth=12, num_heads=12, mlp_ratio=4.,
                 num_classes=2, norm_layer=None, global_pool=False):
        super().__init__()
        norm_layer = norm_layer or partial(nn.LayerNorm, eps=1e-6)

        # Use pretrained temporal MAE encoder
        self.encoder = MAEEncoder_S2_Temporal(
            img_size=img_size, patch_size=patch_size, in_chans=in_chans, temporal_frames=temporal_frames,
            embed_dim=embed_dim, depth=depth, num_heads=num_heads,
            mlp_ratio=mlp_ratio, norm_layer=norm_layer)

        # Remove mask generator for fine-tuning (we don't need masking)
        self.encoder.mask_generator = None

        self.global_pool = global_pool
        self.temporal_frames = temporal_frames
        self.img_size = img_size

        if self.global_pool:
            self.fc_norm = norm_layer(embed_dim)
            del self.encoder.norm  # remove the original norm

        # Classifier head
        self.head = nn.Linear(embed_dim, num_classes) if num_classes > 0 else nn.Identity()

        # Temporal aggregation for building detection
        # Aggregate temporal features to single frame output
        self.temporal_aggregator = nn.Sequential(
            nn.Linear(embed_dim, embed_dim),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim, embed_dim)
        )

        # Segmentation decoder for dense prediction (22x22 output)
        # Since patch_size=1, we already have 22x22 spatial resolution
        self.seg_decoder = nn.Sequential(
            nn.Linear(embed_dim, embed_dim//2),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim//2, embed_dim//4),
            nn.ReLU(inplace=True),
            nn.Linear(embed_dim//4, num_classes)
        )

    def load_pretrained_mae(self, checkpoint_path):
        """Load pretrained temporal MAE weights."""
        checkpoint = torch.load(checkpoint_path, map_location='cpu')

        # Load encoder weights
        encoder_state_dict = {}
        for k, v in checkpoint.items():
            if k.startswith('encoder.'):
                encoder_state_dict[k[8:]] = v  # remove 'encoder.' prefix

        msg = self.encoder.load_state_dict(encoder_state_dict, strict=False)
        print(f"Loaded pretrained temporal MAE encoder: {msg}")

    def forward_features(self, x):
        """
        Args:
            x: [B, T, C, H, W] temporal Sentinel-2 data
        Returns:
            aggregated features and full features
        """
        # embed patches: [B, T, C, H, W] -> [B, T*H*W, embed_dim]
        x = self.encoder.patch_embed(x)

        # add pos embed w/o cls token
        x = x + self.encoder.pos_embed[:, 1:, :]

        # append cls token
        cls_token = self.encoder.cls_token + self.encoder.pos_embed[:, :1, :]
        cls_tokens = cls_token.expand(x.shape[0], -1, -1)
        x = torch.cat((cls_tokens, x), dim=1)

        # apply Transformer blocks
        for blk in self.encoder.blocks:
            x = blk(x)

        if self.global_pool:
            x = x[:, 1:, :].mean(dim=1)  # global pool without cls token
            outcome = self.fc_norm(x)
        else:
            x = self.encoder.norm(x)
            outcome = x[:, 0]  # cls token

        return outcome, x

    def forward_segmentation(self, x):
        """
        Forward pass for dense segmentation.
        Args:
            x: [B, T, C, H, W] where T=16, C=13, H=22, W=22
        Returns:
            segmentation output: [B, num_classes, H, W]
        """
        B, T, C, H, W = x.shape

        # Get patch features (without cls token)
        features, full_features = self.forward_features(x)
        patch_features = full_features[:, 1:, :]  # Remove cls token: [B, T*H*W, embed_dim]

        # Reshape to spatial-temporal format: [B, T, H, W, embed_dim]
        patch_features = patch_features.reshape(B, T, H, W, -1)

        # Aggregate temporal information
        # Option 1: Mean pooling across time
        aggregated_features = patch_features.mean(dim=1)  # [B, H, W, embed_dim]

        # Option 2: Learnable temporal aggregation
        # aggregated_features = self.temporal_aggregator(patch_features.mean(dim=1))

        # Apply segmentation decoder: [B, H, W, embed_dim] -> [B, H, W, num_classes]
        seg_output = self.seg_decoder(aggregated_features)

        # Permute to standard format: [B, num_classes, H, W]
        seg_output = seg_output.permute(0, 3, 1, 2)

        return seg_output

    def forward(self, x, mode='segmentation'):
        """
        Args:
            x: [B, T, C, H, W] temporal Sentinel-2 data
        """
        if mode == 'classification':
            features, _ = self.forward_features(x)
            return self.head(features)
        elif mode == 'segmentation':
            return self.forward_segmentation(x)
        else:
            raise ValueError(f"Unknown mode: {mode}")

class MAE_S2_BuildingDetection_Integrated_Temporal(nn.Module):
    """Integrated model combining temporal MAE pretraining and building detection fine-tuning."""
    def __init__(self, img_size=22, patch_size=1, in_chans=13, temporal_frames=16,
                 embed_dim=768, depth=12, num_heads=12,
                 decoder_embed_dim=512, decoder_depth=8, decoder_num_heads=16,
                 mlp_ratio=4., num_classes=2, norm_layer=None):
        super().__init__()

        # Temporal MAE for pretraining
        self.mae_model = MAE_Sentinel2_Temporal(
            img_size=img_size, patch_size=patch_size, in_chans=in_chans, temporal_frames=temporal_frames,
            embed_dim=embed_dim, depth=depth, num_heads=num_heads,
            decoder_embed_dim=decoder_embed_dim, decoder_depth=decoder_depth,
            decoder_num_heads=decoder_num_heads, mlp_ratio=mlp_ratio,
            norm_layer=norm_layer)

        # Fine-tuning model for building detection
        self.finetune_model = MAE_FineTune_BuildingDetection_Temporal(
            img_size=img_size, patch_size=patch_size, in_chans=in_chans, temporal_frames=temporal_frames,
            embed_dim=embed_dim, depth=depth, num_heads=num_heads,
            mlp_ratio=mlp_ratio, num_classes=num_classes, norm_layer=norm_layer)

        self.training_mode = 'pretrain'  # 'pretrain' or 'finetune'

    def set_training_mode(self, mode):
        """Set training mode: 'pretrain' for MAE pretraining, 'finetune' for building detection."""
        assert mode in ['pretrain', 'finetune']
        self.training_mode = mode

        if mode == 'pretrain':
            # Freeze finetune model, unfreeze MAE
            for param in self.finetune_model.parameters():
                param.requires_grad = False
            for param in self.mae_model.parameters():
                param.requires_grad = True
        else:
            # Freeze MAE, unfreeze finetune model
            for param in self.mae_model.parameters():
                param.requires_grad = False
            for param in self.finetune_model.parameters():
                param.requires_grad = True

    def transfer_pretrained_weights(self):
        """Transfer pretrained MAE encoder weights to fine-tuning model."""
        # Copy encoder weights from MAE to fine-tuning model
        finetune_encoder_dict = self.finetune_model.encoder.state_dict()
        mae_encoder_dict = self.mae_model.encoder.state_dict()

        # Update fine-tuning encoder with MAE encoder weights
        for k, v in mae_encoder_dict.items():
            if k in finetune_encoder_dict and finetune_encoder_dict[k].shape == v.shape:
                finetune_encoder_dict[k] = v

        self.finetune_model.encoder.load_state_dict(finetune_encoder_dict)
        print("Transferred pretrained MAE encoder weights to fine-tuning model")

    def forward(self, x, mask_ratio=0.75, mode='segmentation'):
        if self.training_mode == 'pretrain':
            return self.mae_model(x, mask_ratio)
        else:
            return self.finetune_model(x, mode)


if __name__ == "__main__":
    import os
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'

    # Test temporal MAE pretraining
    print("Testing Temporal MAE for Sentinel-2 reconstruction pretraining...")
    mae_model = MAE_Sentinel2_Temporal(
        img_size=22, patch_size=1, in_chans=13, temporal_frames=16,
        embed_dim=256, depth=6, num_heads=8,  # Smaller model for testing
        decoder_embed_dim=128, decoder_depth=4, decoder_num_heads=8
    )

    # Test input: [B, T, C, H, W] where T=16, C=13, H=22, W=22
    test_input = torch.randn(2, 16, 13, 22, 22)

    # Forward pass for pretraining
    loss, pred, mask = mae_model(test_input, mask_ratio=0.75)
    print(f"Temporal MAE Loss: {loss.item():.4f}")
    print(f"Prediction shape: {pred.shape}")  # [B, T*H*W, p*p*C]
    print(f"Mask shape: {mask.shape}")  # [B, T*H*W]

    # Test fine-tuning model
    print("\nTesting temporal fine-tuning model for building detection...")
    finetune_model = MAE_FineTune_BuildingDetection_Temporal(
        img_size=22, patch_size=1, in_chans=13, temporal_frames=16,
        embed_dim=256, depth=6, num_heads=8,
        num_classes=2
    )

    # Test segmentation
    seg_output = finetune_model(test_input, mode='segmentation')
    print(f"Segmentation output shape: {seg_output.shape}")  # [B, num_classes, H, W]

    # Test classification
    cls_output = finetune_model(test_input, mode='classification')
    print(f"Classification output shape: {cls_output.shape}")  # [B, num_classes]

    # Test integrated model
    print("\nTesting integrated temporal model...")
    integrated_model = MAE_S2_BuildingDetection_Integrated_Temporal(
        img_size=22, patch_size=1, in_chans=13, temporal_frames=16,
        embed_dim=256, depth=6, num_heads=8,
        num_classes=2
    )

    # Test pretraining mode
    integrated_model.set_training_mode('pretrain')
    loss, pred, mask = integrated_model(test_input, mask_ratio=0.75)
    print(f"Integrated model - Pretrain loss: {loss.item():.4f}")

    # Transfer weights and test fine-tuning mode
    integrated_model.transfer_pretrained_weights()
    integrated_model.set_training_mode('finetune')
    seg_output = integrated_model(test_input, mode='segmentation')
    print(f"Integrated model - Segmentation output shape: {seg_output.shape}")

    # Test original model with correct input format
    print("\nTesting original LBD_S12v27 model...")
    deep_model = LBD_S12v27(s2_inchannel=13, frame=16, num_classes=2, damage_classes=2)
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384)
    low_images1 = torch.rand(bs, 16, 17, 22, 22)  # This matches our temporal format!
    low_images2 = torch.rand(bs, 1, 17, 22, 22)
    pre_label = torch.rand(bs, 384, 384)
    post_label = torch.rand(bs, 384, 384)
    shfit_dxdy = torch.ones(bs, 2)

    inputs = [hight_image, low_images1, low_images2, pre_label, post_label, shfit_dxdy]
    device = torch.device("cpu")  # Use CPU for testing

    outputs = deep_model(inputs, device)
    print(f"Original model output keys: {outputs.keys()}")

    print("\n🎉 All temporal MAE models tested successfully!")
    print("\nKey improvements:")
    print("- ✓ Supports temporal input [B, T=16, C=13, H=22, W=22]")
    print("- ✓ Uses ViT encoder with patch_size=1 (no downsampling)")
    print("- ✓ Temporal embedding for distinguishing time frames")
    print("- ✓ Modular design for easy pretraining and fine-tuning")
    print("- ✓ Compatible with your existing data format")
