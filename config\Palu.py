import torch
from torch import nn
import os
from glob import glob
import albumentations as albu
import argparse
from pathlib import Path
import numpy as np
# from net.HRNet2_plus import HRNet, HRNet_S2
# from net.HRNet3 import HRNet, HRNet_S2
# from net.HRNet3_plus import HRNet_S2_DeConv
from net.HRNet3_plusv4 import HRNet_S2_DeConv
from net.HRNet3_plusv4_S1_S2 import HRNet_S1_S2
from loss.loss import <PERSON>emCELoss, MaskLoss, BCEFocalLoss, one_hot_CrossEntropy, myOhemCELoss, BCE_OhemCELoss, WBCE_OhemCELoss
# from dataset.UKR_xBD_S2 import UKR_xBD_S2, dataset_collate
from dataset.UKR_xBD_S1_S2 import UKR_xBD_S1_S2, dataset_collate

# =============================训练参数============================ #
batch_size = 3
lr = 1e-4
size = 384
epochs = 100
output_building = 2
output_damage = 2
data_name = "palu"
model_name = "HRNet3_plusv4"
save_path = os.path.join("./result", data_name, model_name, "6_train_hold_S1_S2")
os.makedirs(save_path, exist_ok=True)

# model = HRNet_S2_DeConv(pretrained=False, s2_inchannel=17, frame=16, num_classes=3, damage_classes=output_damaged)
# model = HRNet_S2_DeConv(pretrained=False, s2_inchannel=8, frame=1, num_classes=3, damage_classes=output_damage)
model = HRNet_S1_S2(pretrained=False, s1_inchannel=8, s2_inchannel=17, frame=16, num_classes=3, damage_classes=output_damage)
pre_train_model_path = None
# pre_train_model_path = "/mnt/d2/zxq/BuildingDamage/result/UKRxBD_HRnet2/HRNet3_plusv4/5_angle/53.pt"
# pre_train_model_path = "/mnt/d2/zxq/BuildingDamage/result/palu/HRNet3_plusv4/5_train_hold_S1/20.pt"

criterion1 = OhemCELoss(thresh=0.7, n_min=batch_size*size*size//16, ignore_index=255)
criterion2 = nn.L1Loss()
criterion3 = nn.KLDivLoss(reduction="mean")

# 数据集加载 + 多GPU训练
device = 'cuda'
train_dataset = UKR_xBD_S1_S2('train')
val_dataset   = UKR_xBD_S1_S2('test')
collate_fn = dataset_collate
# =============================训练参数============================ #


# =============================测试参数============================ #
data_name = "palu"
save_name = "6_train_hold_S1_S2"
test_base_path = os.path.join("./dataset/palu", "test")
test_base_save_path = os.path.join("./result", data_name)
model_path = os.path.join("./result/", data_name, model_name, save_name, "25.pt")
time_series_path = os.path.join(test_base_path, "images")
s2_pre_path = os.path.join(test_base_path, "S2")
s2_post_path = os.path.join(test_base_path, "S2_post")

def make_dirs(file_path):
    os.makedirs(file_path, exist_ok=True)
    return file_path

pre_high_image_save_path = make_dirs(os.path.join(test_base_save_path, "pre_high_image"))
post_high_image_save_path = make_dirs(os.path.join(test_base_save_path, "post_high_image"))
pre_s2_image_save_path = make_dirs(os.path.join(test_base_save_path, "pre_s2_image"))
post_s2_image_post_save_path = make_dirs(os.path.join(test_base_save_path, "post_s2_image"))
pre_label_save_path = make_dirs(os.path.join(test_base_save_path, "pre_label"))
post_label_save_path = make_dirs(os.path.join(test_base_save_path, "post_label"))
pre_s1_image_save_path = make_dirs(os.path.join(test_base_save_path, "pre_s1_image"))
post_s1_image_post_save_path = make_dirs(os.path.join(test_base_save_path, "post_s1_image"))

pre_high_label_save_path = make_dirs(os.path.join(test_base_save_path, model_name, save_name, "pre_high_label"))
post_high_label_save_path = make_dirs(os.path.join(test_base_save_path, model_name, save_name, "post_high_label"))
pre_s2_label_save_path = make_dirs(os.path.join(test_base_save_path, model_name, save_name, "pre_s2_label"))
post_s2_label_save_path = make_dirs(os.path.join(test_base_save_path, model_name, save_name, "post_s2_label"))
high_gray_save_path = make_dirs(os.path.join(test_base_save_path, model_name, save_name, "high_gray"))
s2_gray_save_path = make_dirs(os.path.join(test_base_save_path, model_name, save_name, "s2_gray"))
plot_save_path = make_dirs(os.path.join(test_base_save_path, model_name, save_name, "plot"))
pre_s1_label_save_path = make_dirs(os.path.join(test_base_save_path, model_name, save_name, "pre_s1_label"))
post_s1_label_save_path = make_dirs(os.path.join(test_base_save_path, model_name, save_name, "post_s1_label"))

def get_pre_image_name(image_name):
    pre_images = glob(os.path.join(time_series_path, image_name + "**.tif"))
    pre_images.sort()
    return pre_images[-1]

# print(names)
pre_high_image_paths = []
post_high_image_paths = []
pre_s2_image_paths = []
post_s2_image_paths = []
pre_label_paths = sorted(glob(os.path.join(test_base_path, "labels", "*santa*pre**.tif")))
pre_label_paths+= sorted(glob(os.path.join(test_base_path, "labels", "*palu*pre**.tif")))
post_label_paths = []
pre_s1_image_paths = []
post_s1_image_paths = []

for pre_label_path in pre_label_paths:
    image_name = str(os.path.basename(pre_label_path)).split(".tif")[0]
    pre_high_image_paths.append(get_pre_image_name(image_name.replace("pre", "*pre*")))
    post_high_image_paths.append(get_pre_image_name(image_name.replace("pre", "*post*")))
    pre_s2_image_paths.append(pre_label_path.split(".tif")[0].replace("pre", "*pre*").replace("labels", "S2"))
    post_s2_image_paths.append(pre_label_path.split(".tif")[0].replace("labels", "S2_post").replace("pre", "*post*"))
    post_label_paths.append(pre_label_path.replace("pre", "post"))
    pre_s1_image_paths.append(pre_label_path.split(".tif")[0].replace("pre", "*pre*").replace("labels", "S1"))
    post_s1_image_paths.append(pre_label_path.split(".tif")[0].replace("pre", "*post*").replace("labels", "S1_post"))
    # print(pre_s1_image_paths, post_s1_image_paths)
    # x = input()

s2_mean_std = np.loadtxt("./dataset/palu/palu_s2_mean_std.txt")
s2_mean, s2_std = s2_mean_std[0,:], s2_mean_std[1,:]

s1_mean_std = np.loadtxt("./dataset/xBDS2/S1_mean_std.txt")
s1_mean, s1_std = s1_mean_std[0,:], s1_mean_std[1,:]

label_keys = [
    [  0,   0,   0], # 0 非建筑物区域
    [255, 255, 255], # 1 未损毁
    [  0,   0, 255], # 2 Moderate
    [  0, 255,   0], # 3 Severe
    [255,   0,   0], # 4 Destroyed
    # [255, 255,   0]  # 5 未确定的值
]

# =============================测试参数============================ #
