#!/usr/bin/env python3
"""
测试参考diffusion_res.py风格的LR到HR RDDM模型
验证：
1. 训练时直接预测x0
2. 推理时构建残差，多次迭代得到最终x0
3. 使用diffusion_res.py的alpha和beta系数表
"""

import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt
import numpy as np
from lr_to_hr_rddm import create_lr_to_hr_model, gen_coefficients

def test_coefficient_generation():
    """测试系数生成"""
    print("=== 测试系数生成 ===")
    
    timesteps = 100
    
    # 测试不同的调度
    schedules = ["decreased", "increased", "average"]
    
    plt.figure(figsize=(15, 5))
    
    for i, schedule in enumerate(schedules):
        alphas = gen_coefficients(timesteps, schedule=schedule, sum_scale=1.0)
        alphas_cumsum = alphas.cumsum(dim=0).clamp(0, 1)
        
        plt.subplot(1, 3, i + 1)
        plt.plot(alphas.numpy(), label=f'Alpha ({schedule})', alpha=0.7)
        plt.plot(alphas_cumsum.numpy(), label=f'Alpha Cumsum ({schedule})', alpha=0.7)
        plt.title(f'Alpha Schedule - {schedule}')
        plt.xlabel('Timestep')
        plt.ylabel('Value')
        plt.legend()
        plt.grid(True)
        
        print(f"{schedule} 调度:")
        print(f"  Alpha和: {alphas.sum():.6f}")
        print(f"  Alpha范围: [{alphas.min():.6f}, {alphas.max():.6f}]")
        print(f"  Alpha累积范围: [{alphas_cumsum.min():.6f}, {alphas_cumsum.max():.6f}]")
    
    plt.tight_layout()
    plt.savefig('diffusion_res_coefficients.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return True

def create_test_data(batch_size=2, channels=2, height=64, width=64):
    """创建测试数据"""
    # 创建HR特征：具有清晰结构
    hr_features = torch.zeros(batch_size, channels, height, width)
    
    # 添加一些结构化特征
    for b in range(batch_size):
        # 通道0：主要特征
        hr_features[b, 0, 15:25, 15:25] = 1.5
        hr_features[b, 0, 35:45, 35:45] = -1.2
        hr_features[b, 0, 20:30, 45:55] = 0.8
        
        # 通道1：辅助特征
        hr_features[b, 1, 10:20, 30:40] = -0.9
        hr_features[b, 1, 40:50, 10:20] = 1.1
    
    # 创建LR特征：模糊和噪声
    lr_features = hr_features.clone()
    
    # 高斯模糊
    kernel_size = 7
    sigma = 2.0
    kernel = torch.exp(-torch.arange(-(kernel_size//2), kernel_size//2 + 1)**2 / (2*sigma**2))
    kernel = kernel / kernel.sum()
    kernel_2d = kernel.unsqueeze(0) * kernel.unsqueeze(1)
    kernel_2d = kernel_2d.unsqueeze(0).unsqueeze(0).repeat(channels, 1, 1, 1)
    
    lr_features = F.conv2d(lr_features, kernel_2d, padding=kernel_size//2, groups=channels)
    
    # 添加噪声
    lr_features += torch.randn_like(lr_features) * 0.15
    
    # 降低动态范围
    lr_features = lr_features * 0.7
    
    return hr_features, lr_features

def test_direct_x0_prediction():
    """测试直接预测x0"""
    print("\n=== 测试直接预测x0 ===")
    
    model = create_lr_to_hr_model(
        dim=32,
        dim_mults=(1, 2, 4),
        channels=2,
        timesteps=50,
        sampling_timesteps=10,
        alpha_scale=1.0,
        beta_scale=1.0
    )
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建测试数据
    hr_features, lr_features = create_test_data()
    
    print(f"HR特征范围: [{hr_features.min():.3f}, {hr_features.max():.3f}]")
    print(f"LR特征范围: [{lr_features.min():.3f}, {lr_features.max():.3f}]")
    
    # 测试训练前向传播
    model.train()
    loss, pred_x0 = model(hr_features, lr_features)
    
    print(f"训练损失: {loss.item():.6f}")
    print(f"预测x0形状: {pred_x0.shape}")
    print(f"预测x0范围: [{pred_x0.min():.3f}, {pred_x0.max():.3f}]")
    
    # 验证预测x0与真实HR的相似性
    x0_error = F.mse_loss(pred_x0, hr_features)
    print(f"x0预测误差: {x0_error.item():.6f}")
    
    # 测试q_sample过程
    t = torch.randint(0, model.num_timesteps, (hr_features.shape[0],)).long()
    x_noisy = model.q_sample(hr_features, lr_features, t)
    
    print(f"噪声化结果范围: [{x_noisy.min():.3f}, {x_noisy.max():.3f}]")
    
    # 验证UNet能够从噪声化结果预测回x0
    model.eval()
    with torch.no_grad():
        recovered_x0 = model.unet(x_noisy, t, x_condition=lr_features)
        recovery_error = F.mse_loss(recovered_x0, hr_features)
        print(f"x0恢复误差: {recovery_error.item():.6f}")
    
    return True

def test_iterative_sampling():
    """测试迭代采样过程"""
    print("\n=== 测试迭代采样过程 ===")
    
    model = create_lr_to_hr_model(
        dim=32,
        dim_mults=(1, 2),
        channels=2,
        timesteps=20,
        sampling_timesteps=5,
        alpha_scale=1.0,
        beta_scale=0.5
    )
    
    hr_features, lr_features = create_test_data(batch_size=1, height=32, width=32)
    
    print(f"初始LR特征范围: [{lr_features.min():.3f}, {lr_features.max():.3f}]")
    print(f"目标HR特征范围: [{hr_features.min():.3f}, {hr_features.max():.3f}]")
    
    model.eval()
    with torch.no_grad():
        # 完整采样过程
        enhanced_features = model.enhance_features(lr_features, return_all_timesteps=False)
        
        print(f"增强后特征范围: [{enhanced_features.min():.3f}, {enhanced_features.max():.3f}]")
        
        # 计算改进效果
        original_error = F.mse_loss(lr_features, hr_features)
        enhanced_error = F.mse_loss(enhanced_features, hr_features)
        improvement = original_error - enhanced_error
        
        print(f"原始误差: {original_error.item():.6f}")
        print(f"增强后误差: {enhanced_error.item():.6f}")
        print(f"改进程度: {improvement.item():.6f}")
        
        # 逐步采样过程分析
        print(f"\n逐步采样分析:")
        device = lr_features.device
        img = lr_features + torch.randn_like(lr_features) * 0.1
        timesteps = torch.linspace(model.num_timesteps - 1, 0, model.sampling_timesteps).long()
        
        for i, t in enumerate(timesteps):
            img_before = img.clone()
            img, pred_x0 = model.p_sample(lr_features, img, t)
            
            step_change = F.mse_loss(img, img_before)
            x0_accuracy = F.mse_loss(pred_x0, hr_features)
            
            print(f"  步骤 {i+1}/{len(timesteps)} (t={t}):")
            print(f"    步骤变化: {step_change.item():.6f}")
            print(f"    x0准确性: {x0_accuracy.item():.6f}")
            print(f"    当前范围: [{img.min():.3f}, {img.max():.3f}]")
        
        success = improvement > 0
        print(f"\n采样结果: {'✓' if success else '✗'}")
        
        return success

def test_residual_construction():
    """测试残差构建过程"""
    print("\n=== 测试残差构建过程 ===")
    
    model = create_lr_to_hr_model(
        dim=16,
        dim_mults=(1, 2),
        channels=2,
        timesteps=10,
        sampling_timesteps=3,
        alpha_scale=1.0,
        beta_scale=0.3
    )
    
    hr_features, lr_features = create_test_data(batch_size=1, height=32, width=32)
    
    # 测试残差构建
    print("残差构建测试:")
    print(f"LR特征均值: {lr_features.mean():.4f}, 标准差: {lr_features.std():.4f}")
    print(f"HR特征均值: {hr_features.mean():.4f}, 标准差: {hr_features.std():.4f}")
    
    # 真实残差
    true_residual = lr_features - hr_features
    print(f"真实残差均值: {true_residual.mean():.4f}, 标准差: {true_residual.std():.4f}")
    
    model.eval()
    with torch.no_grad():
        # 在不同时间步测试残差构建
        for t_val in [0, model.num_timesteps // 2, model.num_timesteps - 1]:
            t = torch.tensor([t_val]).long()
            
            # 前向过程
            x_noisy = model.q_sample(hr_features, lr_features, t)
            
            # 预测x0
            pred_x0 = model.unet(x_noisy, t, x_condition=lr_features)
            
            # 构建残差
            constructed_residual = lr_features - pred_x0
            
            # 比较残差
            residual_error = F.mse_loss(constructed_residual, true_residual)
            x0_error = F.mse_loss(pred_x0, hr_features)
            
            print(f"\n时间步 t={t_val}:")
            print(f"  x0预测误差: {x0_error.item():.6f}")
            print(f"  残差构建误差: {residual_error.item():.6f}")
            print(f"  构建残差范围: [{constructed_residual.min():.3f}, {constructed_residual.max():.3f}]")
    
    return True

def test_coefficient_effects():
    """测试不同系数的效果"""
    print("\n=== 测试系数效果 ===")
    
    hr_features, lr_features = create_test_data(batch_size=1, height=32, width=32)
    
    configs = [
        {"alpha_scale": 0.5, "beta_scale": 0.1, "name": "保守"},
        {"alpha_scale": 1.0, "beta_scale": 0.5, "name": "中等"},
        {"alpha_scale": 1.5, "beta_scale": 1.0, "name": "激进"},
    ]
    
    for config in configs:
        print(f"\n--- {config['name']} 配置 ---")
        
        try:
            model = create_lr_to_hr_model(
                dim=16,
                dim_mults=(1, 2),
                channels=2,
                timesteps=15,
                sampling_timesteps=3,
                alpha_scale=config['alpha_scale'],
                beta_scale=config['beta_scale']
            )
            
            # 测试训练
            model.train()
            loss, pred_x0 = model(hr_features, lr_features)
            
            print(f"  训练损失: {loss.item():.6f}")
            
            # 测试推理
            model.eval()
            with torch.no_grad():
                enhanced = model.enhance_features(lr_features)
                
                original_error = F.mse_loss(lr_features, hr_features)
                enhanced_error = F.mse_loss(enhanced, hr_features)
                improvement = original_error - enhanced_error
                
                print(f"  改进程度: {improvement.item():.6f}")
                print(f"  状态: {'✓' if improvement > 0 else '✗'}")
                
        except Exception as e:
            print(f"  ✗ 失败: {str(e)}")

if __name__ == "__main__":
    print("测试diffusion_res.py风格的LR到HR RDDM模型...")
    
    success1 = test_coefficient_generation()
    success2 = test_direct_x0_prediction()
    success3 = test_iterative_sampling()
    success4 = test_residual_construction()
    test_coefficient_effects()
    
    print(f"\n=== 总体测试结果 ===")
    print(f"系数生成: {'✓' if success1 else '✗'}")
    print(f"直接x0预测: {'✓' if success2 else '✗'}")
    print(f"迭代采样: {'✓' if success3 else '✗'}")
    print(f"残差构建: {'✓' if success4 else '✗'}")
    
    if all([success1, success2, success3, success4]):
        print("✓ 所有核心功能都已正确实现！")
        print("\n关键改进:")
        print("1. ✓ 训练时直接预测x0")
        print("2. ✓ 推理时构建残差并迭代")
        print("3. ✓ 使用diffusion_res.py的系数表")
        print("4. ✓ 多次迭代得到最终x0")
    else:
        print("✗ 部分功能需要进一步优化")
