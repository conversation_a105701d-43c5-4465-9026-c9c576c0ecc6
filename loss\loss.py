# encoding: utf-8
"""
@author: ZxqYi<PERSON>ang
@file: loss.py
@time: 2021/3/26 14:33
"""
import torch
import torch.nn as nn
from torch.autograd import Variable
import cv2
import numpy as np
import torch.nn.functional as F
import matplotlib.pyplot as plt

class BCE_OhemCELoss(nn.Module):
    def __init__(self, thresh, n_min, ignore_index=255, *args, **kwargs):
        super(BCE_OhemCELoss, self).__init__()
        self.thresh = -torch.log(torch.tensor(thresh, dtype=torch.float))
        self.n_min = n_min
        self.criteria = nn.BCELoss(reduction='none')

    def forward(self, logits, labels):
        logits = torch.squeeze(torch.sigmoid(logits), dim=1)
        loss = F.binary_cross_entropy(logits.to(torch.float32), labels.to(torch.float32), reduction='none').view(-1)
        loss, _ = torch.sort(loss, descending=True)
        # print(f"now the self.n_min is: {self.n_min}")
        if loss[self.n_min] > self.thresh:
            loss = loss[loss>self.thresh]
        else:
            loss = loss[:self.n_min]
        return torch.mean(loss)

class WBCE_OhemCELoss(nn.Module):
    def __init__(self, thresh, n_min, ignore_index=255, *args, **kwargs):
        super(WBCE_OhemCELoss, self).__init__()
        self.thresh = -torch.log(torch.tensor(thresh, dtype=torch.float))
        self.n_min = n_min
        self.criteria = nn.BCELoss(reduction='none')

    def forward(self, logits, labels, weight):
        B, C, H, W = logits.size()
        logits = torch.squeeze(torch.sigmoid(logits), dim=1)
        loss = self.criteria(logits, labels).view(-1)
        # print(loss.shape)
        weight = weight.reshape(B,-1, H*W).permute(0, 2, 1).reshape(B*H*W,-1)
        loss = torch.multiply(loss, torch.squeeze(weight, dim=1))
        loss, _ = torch.sort(loss, descending=True)
        if loss[self.n_min] > self.thresh:
            loss = loss[loss>self.thresh]
        else:
            loss = loss[:self.n_min]
        return torch.mean(loss)


class one_hot_CrossEntropy(nn.Module):
    def __init__(self):
        super(one_hot_CrossEntropy,self).__init__()
    
    def forward(self, logits: torch.tensor, labels: torch.tensor, weight: torch.tensor):
        B, C, H, W = logits.size()
        logits = torch.log_softmax(logits, dim=1)
        # labels = F.one_hot(labels, num_classes=2)
        labels = F.one_hot(labels, num_classes=logits.shape[1]).permute(0, 3, 1, 2)

        # logits = logits.reshape(B, C, H*W).permute(0, 2, 1).reshape(B*H*W, C)
        # labels = labels.reshape(B, C, H*W).permute(0, 2, 1).reshape(B*H*W, C)
        # weight = weight.reshape(B,-1, H*W).permute(0, 2, 1).reshape(B*H*W,-1)
        # weight = weight.reshape(B,-1, H*W).permute(0, 2, 1).reshape(B*H*W,-1)

        loss = -torch.sum(weight*labels * logits, dim=1)
        # loss = -torch.sum(loss, dim=1)
        # print(torch.min(loss), torch.max(loss))
        return loss


class myOhemCELoss(nn.Module):
    def __init__(self, thresh, n_min, ignore_index=255, *args, **kwargs):
        super(myOhemCELoss, self).__init__()
        self.thresh = -torch.log(torch.tensor(thresh, dtype=torch.float))
        self.n_min = n_min
        self.criteria = one_hot_CrossEntropy()

    def forward(self, logits, labels, weight):
        loss = self.criteria(logits, labels, weight).view(-1)
        loss, _ = torch.sort(loss, descending=True)
        if loss[self.n_min] > self.thresh:
            loss = loss[loss>self.thresh]
        else:
            loss = loss[:self.n_min]
        return torch.mean(loss)



class OhemCELoss(nn.Module):
    def __init__(self, thresh, n_min, ignore_index=255, *args, **kwargs):
        super(OhemCELoss, self).__init__()
        self.thresh = -torch.log(torch.tensor(thresh, dtype=torch.float))#.cuda()
        self.n_min = n_min
        self.ignore_index = ignore_index
        self.criteria = nn.CrossEntropyLoss(ignore_index=ignore_index, reduction='none')
        # self.criteria = Our_CrossEntropy()

    def forward(self, logits, labels):
        N, C, H, W = logits.size()
        loss = self.criteria(logits, labels).view(-1)
        loss, _ = torch.sort(loss, descending=True)
        if loss[self.n_min] > self.thresh:
            loss = loss[loss>self.thresh]
        else:
            loss = loss[:self.n_min]
        return torch.mean(loss)
    
class MaskLoss(nn.Module):
    def __init__(self, batch_size, size) -> None:
        super().__init__()
        """
        在线的掩码损失函数
        1: 在初始阶段, torch.where 的方式难以准确识别建筑物；
        计划使用软标签的方式计算mask
        """
        self.criterion = OhemCELoss(thresh=0.7, n_min=batch_size*size*size//16, ignore_index=255)

    def forward(self, pre, lab):
        # pre_argmax = torch.argmax(F.softmax(pre, dim=1), dim=1)
        # lab_mask = torch.where(pre_argmax==lab, lab, 255)
        # loss = self.criterion(pre, lab_mask.long())
        # return loss
        # pre_max_value, _ = torch.max(F.softmax(pre, dim=1), dim=1)
        # mean_value = torch.mean(pre_max_value)
        # lab_mask = torch.where(pre_max_value>=mean_value, lab, 255)
        # # print(pre_max_value.shape, lab_mask.shape)
        loss = self.criterion(pre, lab)
        return loss
    
class BCEFocalLoss(torch.nn.Module):
    def __init__(self, gamma=2, alpha=0.25, reduction='mean'):
        super(BCEFocalLoss, self).__init__()
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = reduction

    def forward(self, predict, target):
        # pt = torch.sigmoid(predict)  # sigmoide获取概率
        B, H, W = predict.shape
        predict = predict.view(B, H*W)
        target = target.view(B, H*W)
        pt = predict
        # 在原始ce上增加动态权重因子，注意alpha的写法，下面多类时不能这样使用
        loss = - self.alpha * (1 - pt) ** self.gamma * target * torch.log(pt + 1e-8)\
               - (1 - self.alpha) * pt ** self.gamma * (1 - target + 1e-8) * torch.log(1 - pt + 1e-8)

        if self.reduction == 'mean':
            loss = torch.mean(loss)
        elif self.reduction == 'sum':
            loss = torch.sum(loss)
        return loss


class MultiCEFocalLoss(torch.nn.Module):
    def __init__(self, class_num, gamma=2, alpha=None, reduction='mean'):
        super(MultiCEFocalLoss, self).__init__()
        if alpha is None:
            self.alpha = Variable(torch.ones(class_num, 1))
        else:
            self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
        self.class_num =  class_num

    def forward(self, predict, target):
        pt = F.softmax(predict, dim=1) # softmmax获取预测概率
        class_mask = F.one_hot(target, self.class_num) #获取target的one hot编码
        ids = target.view(-1, 1)
        alpha = self.alpha[ids.data.view(-1)] # 注意，这里的alpha是给定的一个list(tensor
        #),里面的元素分别是每一个类的权重因子
        probs = (pt * class_mask).sum(1).view(-1, 1) # 利用onehot作为mask，提取对应的pt
        log_p = probs.log()
        # 同样，原始ce上增加一个动态权重衰减因子
        loss = -alpha * (torch.pow((1 - probs), self.gamma)) * log_p

        if self.reduction == 'mean':
            loss = loss.mean()
        elif self.reduction == 'sum':
            loss = loss.sum()
        return loss
    
if __name__ == "__main__":
    # loss = MaskLoss(batch_size=16, size=512)
    # loss = BCEFocalLoss()
    # loss = myOhemCELoss(thresh=0.7, n_min=512*512//16)
    # loss = one_hot_CrossEntropy()
    loss = BCE_OhemCELoss(thresh=0.7, n_min=512*512//16)
    pre = torch.rand((16, 512, 512))
    lab = torch.ones((16, 512, 512), dtype=torch.float32)
    weight = torch.rand((16, 1, 512, 512), dtype=torch.float32)
    out = loss(pre, lab)
    print(out)

