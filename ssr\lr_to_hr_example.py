#!/usr/bin/env python3
"""
LR到HR特征恢复RDDM模型使用示例
展示如何训练和使用模型进行特征恢复
"""

import torch
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import matplotlib.pyplot as plt
from lr_to_hr_rddm import create_lr_to_hr_model

class FeatureDataset(Dataset):
    """特征数据集"""
    
    def __init__(self, num_samples=1000, channels=2, height=64, width=64):
        self.num_samples = num_samples
        self.channels = channels
        self.height = height
        self.width = width
        
        # 预生成数据
        self.hr_features, self.lr_features = self._generate_data()
    
    def _generate_data(self):
        """生成模拟的HR和LR特征数据"""
        print(f"生成 {self.num_samples} 个特征样本...")
        
        hr_features = []
        lr_features = []
        
        for i in range(self.num_samples):
            # 生成HR特征：具有丰富细节的特征图
            hr = torch.randn(self.channels, self.height, self.width)
            
            # 添加结构化模式
            for c in range(self.channels):
                # 随机添加一些高频特征
                num_patterns = np.random.randint(3, 8)
                for _ in range(num_patterns):
                    x = np.random.randint(5, self.width - 15)
                    y = np.random.randint(5, self.height - 15)
                    size = np.random.randint(5, 15)
                    value = np.random.uniform(-1, 1)
                    hr[c, y:y+size, x:x+size] += value
            
            # 生成对应的LR特征：通过退化HR特征得到
            lr = self._degrade_features(hr)
            
            hr_features.append(hr)
            lr_features.append(lr)
        
        return torch.stack(hr_features), torch.stack(lr_features)
    
    def _degrade_features(self, hr_features):
        """将HR特征退化为LR特征"""
        lr = hr_features.clone()
        
        # 高斯模糊
        kernel_size = 5
        sigma = 1.2
        kernel = torch.exp(-torch.arange(-(kernel_size//2), kernel_size//2 + 1)**2 / (2*sigma**2))
        kernel = kernel / kernel.sum()
        kernel_2d = kernel.unsqueeze(0) * kernel.unsqueeze(1)
        kernel_2d = kernel_2d.unsqueeze(0).unsqueeze(0).repeat(self.channels, 1, 1, 1)
        
        lr = F.conv2d(lr.unsqueeze(0), kernel_2d, padding=kernel_size//2, groups=self.channels).squeeze(0)
        
        # 添加噪声
        lr += torch.randn_like(lr) * 0.08
        
        # 降低动态范围
        lr = lr * 0.85
        
        return lr
    
    def __len__(self):
        return self.num_samples
    
    def __getitem__(self, idx):
        return self.hr_features[idx], self.lr_features[idx]

def train_model(model, dataloader, num_epochs=10, lr=1e-4):
    """训练模型"""
    print(f"开始训练，共 {num_epochs} 个epoch...")
    
    optimizer = optim.Adam(model.parameters(), lr=lr)
    model.train()
    
    losses = []
    
    for epoch in range(num_epochs):
        epoch_losses = []
        
        for batch_idx, (hr_features, lr_features) in enumerate(dataloader):
            optimizer.zero_grad()
            
            # 前向传播
            loss, pred_hr = model(hr_features, lr_features)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            epoch_losses.append(loss.item())
            
            if batch_idx % 10 == 0:
                print(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}, Loss: {loss.item():.6f}")
        
        avg_loss = np.mean(epoch_losses)
        losses.append(avg_loss)
        print(f"Epoch {epoch+1} 平均损失: {avg_loss:.6f}")
    
    return losses

def evaluate_model(model, test_dataloader):
    """评估模型"""
    print("评估模型性能...")
    
    model.eval()
    total_loss = 0
    total_improvement = 0
    num_samples = 0
    
    with torch.no_grad():
        for hr_features, lr_features in test_dataloader:
            # 计算训练损失
            loss, pred_hr = model(hr_features, lr_features)
            total_loss += loss.item()
            
            # 计算特征增强效果
            enhanced_features = model.enhance_features(lr_features, num_steps=15)
            
            # 计算改进程度
            original_error = F.mse_loss(lr_features, hr_features)
            enhanced_error = F.mse_loss(enhanced_features, hr_features)
            improvement = original_error - enhanced_error
            
            total_improvement += improvement.item()
            num_samples += 1
    
    avg_loss = total_loss / num_samples
    avg_improvement = total_improvement / num_samples
    
    print(f"平均测试损失: {avg_loss:.6f}")
    print(f"平均改进程度: {avg_improvement:.6f}")
    
    return avg_loss, avg_improvement

def visualize_results(model, hr_features, lr_features, save_path=None):
    """可视化结果"""
    model.eval()
    
    with torch.no_grad():
        # 选择第一个样本进行可视化
        hr_sample = hr_features[0:1]
        lr_sample = lr_features[0:1]
        
        # 特征增强
        enhanced_sample = model.enhance_features(lr_sample, num_steps=20)
        
        # 转换为numpy用于可视化
        hr_np = hr_sample[0, 0].cpu().numpy()  # 第一个通道
        lr_np = lr_sample[0, 0].cpu().numpy()
        enhanced_np = enhanced_sample[0, 0].cpu().numpy()
        
        # 创建图像
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        im1 = axes[0].imshow(lr_np, cmap='viridis')
        axes[0].set_title('LR Features')
        axes[0].axis('off')
        plt.colorbar(im1, ax=axes[0])
        
        im2 = axes[1].imshow(enhanced_np, cmap='viridis')
        axes[1].set_title('Enhanced Features')
        axes[1].axis('off')
        plt.colorbar(im2, ax=axes[1])
        
        im3 = axes[2].imshow(hr_np, cmap='viridis')
        axes[2].set_title('HR Features (Target)')
        axes[2].axis('off')
        plt.colorbar(im3, ax=axes[2])
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            print(f"结果保存到: {save_path}")
        
        plt.show()
        
        # 计算并打印数值结果
        original_error = F.mse_loss(lr_sample, hr_sample).item()
        enhanced_error = F.mse_loss(enhanced_sample, hr_sample).item()
        improvement = original_error - enhanced_error
        
        print(f"可视化样本结果:")
        print(f"  原始误差: {original_error:.6f}")
        print(f"  增强后误差: {enhanced_error:.6f}")
        print(f"  改进程度: {improvement:.6f}")

def main():
    """主函数：完整的训练和评估流程"""
    print("=== LR到HR特征恢复RDDM模型训练示例 ===")
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建模型
    model = create_lr_to_hr_model(
        dim=64,
        dim_mults=(1, 2, 4),
        channels=2,
        timesteps=200,
        sampling_timesteps=25,
        alpha_scale=0.12,
        beta_scale=0.001
    ).to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建数据集
    train_dataset = FeatureDataset(num_samples=800, height=64, width=64)
    test_dataset = FeatureDataset(num_samples=200, height=64, width=64)
    
    train_dataloader = DataLoader(train_dataset, batch_size=8, shuffle=True)
    test_dataloader = DataLoader(test_dataset, batch_size=8, shuffle=False)
    
    # 训练模型
    losses = train_model(model, train_dataloader, num_epochs=15, lr=1e-4)
    
    # 评估模型
    avg_loss, avg_improvement = evaluate_model(model, test_dataloader)
    
    # 可视化结果
    hr_sample, lr_sample = test_dataset[0]
    hr_sample = hr_sample.unsqueeze(0).to(device)
    lr_sample = lr_sample.unsqueeze(0).to(device)
    
    visualize_results(model, hr_sample, lr_sample, save_path='lr_to_hr_results.png')
    
    # 保存模型
    torch.save(model.state_dict(), 'lr_to_hr_rddm_model.pth')
    print("模型已保存到: lr_to_hr_rddm_model.pth")
    
    print(f"\n=== 训练完成 ===")
    print(f"最终测试损失: {avg_loss:.6f}")
    print(f"平均改进程度: {avg_improvement:.6f}")
    
    if avg_improvement > 0:
        print("✓ 模型成功学会了LR到HR特征恢复！")
    else:
        print("✗ 模型需要进一步调优")

def load_and_use_model(model_path, lr_features):
    """加载训练好的模型并使用"""
    print("加载训练好的模型...")
    
    model = create_lr_to_hr_model(
        dim=64,
        dim_mults=(1, 2, 4),
        channels=2,
        timesteps=200,
        sampling_timesteps=25
    )
    
    model.load_state_dict(torch.load(model_path))
    model.eval()
    
    with torch.no_grad():
        enhanced_features = model.enhance_features(lr_features, num_steps=25)
    
    return enhanced_features

if __name__ == "__main__":
    main()
