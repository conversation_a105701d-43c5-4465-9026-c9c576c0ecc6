# import warnings
# warnings.filterwarnings('ignore')

# import os
# from thop import profile
# import torch
# import torch.nn as nn
# import torch.nn.functional as F
# from torch.nn.utils import spectral_norm

# from einops import rearrange
# from dcn_v2 import DCN
# from model.UANet_modify import *

# class ResidualDenseBlock(nn.Module):
#     """Residual Dense Block.

#     Used in RRDB block in ESRGAN.

#     Args:
#         num_feat (int): Channel number of intermediate features.
#         num_grow_ch (int): Channels for each growth.
#     """

#     def __init__(self, num_feat=64, num_grow_ch=32):
#         super(ResidualDenseBlock, self).__init__()
#         self.conv1 = nn.Conv2d(num_feat, num_grow_ch, 3, 1, 1)
#         self.conv2 = nn.Conv2d(num_feat + num_grow_ch, num_grow_ch, 3, 1, 1)
#         self.conv3 = nn.Conv2d(num_feat + 2 * num_grow_ch, num_grow_ch, 3, 1, 1)
#         self.conv4 = nn.Conv2d(num_feat + 3 * num_grow_ch, num_grow_ch, 3, 1, 1)
#         self.conv5 = nn.Conv2d(num_feat + 4 * num_grow_ch, num_feat, 3, 1, 1)

#         self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)

#     def forward(self, x):
#         x1 = self.lrelu(self.conv1(x))
#         x2 = self.lrelu(self.conv2(torch.cat((x, x1), 1)))
#         x3 = self.lrelu(self.conv3(torch.cat((x, x1, x2), 1)))
#         x4 = self.lrelu(self.conv4(torch.cat((x, x1, x2, x3), 1)))
#         x5 = self.conv5(torch.cat((x, x1, x2, x3, x4), 1))
#         # Empirically, we use 0.2 to scale the residual for better performance
#         return x5 * 0.2 + x

# class RRDB(nn.Module):
#     """Residual in Residual Dense Block.

#     Used in RRDB-Net in ESRGAN.

#     Args:
#         num_feat (int): Channel number of intermediate features.
#         num_grow_ch (int): Channels for each growth.
#     """

#     def __init__(self, num_feat, num_grow_ch=32):
#         super(RRDB, self).__init__()
#         self.rdb1 = ResidualDenseBlock(num_feat, num_grow_ch)
#         self.rdb2 = ResidualDenseBlock(num_feat, num_grow_ch)
#         self.rdb3 = ResidualDenseBlock(num_feat, num_grow_ch)

#     def forward(self, x):
#         out = self.rdb1(x)
#         out = self.rdb2(out)
#         out = self.rdb3(out)
#         # Empirically, we use 0.2 to scale the residual for better performance
#         return out * 0.2 + x

# def make_layer(basic_block, num_basic_block, **kwarg):
#     """Make layers by stacking the same blocks.

#     Args:
#         basic_block (nn.module): nn.module class for basic block.
#         num_basic_block (int): number of blocks.

#     Returns:
#         nn.Sequential: Stacked blocks in nn.Sequential.
#     """
#     layers = []
#     for _ in range(num_basic_block):
#         layers.append(basic_block(**kwarg))
#     return nn.Sequential(*layers)

# class RRDBNet(nn.Module):
#     """Networks consisting of Residual in Residual Dense Block, which is used in ESRGAN.

#     ESRGAN: Enhanced Super-Resolution Generative Adversarial Networks.

#     We extend ESRGAN for scale x2 and scale x1.
#     Note: This is one option for scale 1, scale 2 in RRDBNet.
#     We first employ the pixel-unshuffle (an inverse operation of pixelshuffle to reduce the spatial size
#     and enlarge the channel size before feeding inputs into the main ESRGAN architecture.

#     Args:
#         num_in_ch (int): Channel number of inputs.
#         num_out_ch (int): Channel number of outputs.
#         num_feat (int): Channel number of intermediate features.
#             Default: 64
#         num_block (int): Block number in the trunk network. Defaults: 23
#         num_grow_ch (int): Channels for each growth. Default: 32.
#     """

#     def __init__(self, num_in_ch=17, num_feat=64, num_block=23, num_grow_ch=32):
#         super(RRDBNet, self).__init__()
#         self.conv_first = nn.Conv2d(num_in_ch, num_feat, 3, 1, 1)
#         self.body = make_layer(RRDB, num_block, num_feat=num_feat, num_grow_ch=num_grow_ch)
#         self.conv_body = nn.Conv2d(num_feat, num_feat, 3, 1, 1)

#         self.conv_up1 = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
#         self.conv_up2 = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
#         self.conv_hr = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
#         self.conv_last = nn.Conv2d(num_feat, 3, 3, 1, 1)
#         self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)

#     def forward(self, x):
#         feat = self.conv_first(x)
#         body_feat = self.conv_body(self.body(feat))
#         feat = feat + body_feat
#         feat = self.lrelu(self.conv_up1(F.interpolate(feat, scale_factor=2, mode='nearest')))
#         feat = self.lrelu(self.conv_up2(F.interpolate(feat, scale_factor=2, mode='nearest')))
#         feat = self.lrelu(self.conv_hr(feat))
#         rgb_out = self.conv_last(feat)
#         return feat, rgb_out

# class DCN_Guide(nn.Module):
#     def __init__(self, in_channels, out_channels):
#         super().__init__()
#         self.dcn = DCN(in_channels, out_channels, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
#         self.relu = nn.LeakyReLU(inplace=True)
#         self.out_channels = out_channels

#     def forward(self, current_frames, referent_frames):
#         B, frame, C, H, W = referent_frames.shape
#         current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

#         assert current_frames.shape == referent_frames.shape

#         current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
#         referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")
#         current_frames_angle = current_frames1[:, -4:]
#         referent_frames_angle = referent_frames1[:, -4:]

#         fusion_frames = torch.cat((current_frames1[:, :-4], referent_frames1[:, :-4]), dim=1)
#         fusion_angle = torch.cat((current_frames_angle, referent_frames_angle), dim=1)

#         fusion_frames = self.dcn(fusion_frames, fusion_angle)
#         fusion_frames = self.relu(fusion_frames)

#         fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=self.out_channels, h=H, w=W)

#         return fusion_frames

# class Time_Fusion(nn.Module):
#     def __init__(self, decoder_channel, frame):
#         super().__init__()
#         self.conv_time_fusion = nn.Sequential(
#             nn.Conv2d(in_channels=decoder_channel, out_channels=int(decoder_channel*frame), kernel_size=(1, frame), stride=(1, 1)),
#             nn.LeakyReLU(inplace=True),
#         )
#         self.conv_spatial_fusion = nn.Sequential(
#             nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
#             nn.LeakyReLU(inplace=True),
#         )

#     def forward(self, x):
#         B, frame, C, H, W = x.shape
#         x = rearrange(x, "b t c h w -> b c (h w) t", b=B, t=frame, c=C, h=H, w=W)
#         fusion_frames = rearrange(self.conv_time_fusion(x).squeeze(-1), "b (t c) (h w) -> (b t) c h w", b=B, t=frame, c=C, h=H, w=W)
#         fusion_frames = rearrange(self.conv_spatial_fusion(fusion_frames), "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)
#         return fusion_frames

# class Super_Resolution(nn.Module):
#     def __init__(self, decoder_channel, frame):
#         super().__init__()

#         self.ts = Time_Fusion(decoder_channel, frame)
#         self.sr = nn.Sequential(           
#             nn.PixelShuffle(upscale_factor=4),
#             nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
#             nn.LeakyReLU(inplace=True),
#         )

#     def forward(self, x):
#         x = self.ts(x)
#         x = self.sr(rearrange(x, "b t c h w -> b (t c) h w"))
#         return x

# class ResBlock(nn.Module):
#     def __init__(self, decoder_channel):
#         super().__init__()
#         self.conv_path = nn.Sequential(
#             nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1, stride=1),
#             # nn.BatchNorm2d(decoder_channel),
#             nn.LeakyReLU(inplace=True),
#             nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1, stride=1),
#             # nn.BatchNorm2d(decoder_channel),
#         )
#         self.relu = nn.LeakyReLU(inplace=True)

#     def forward(self, x):
#         x = self.relu(self.conv_path(x) + x)
#         return x

# class S2_Building_Damage(nn.Module):
#     def __init__(self, s2_inchannel=13, frame=16, num_classes=2, damage_classes=2):
#         super().__init__()
#         decoder_channel = 64

#         self.s2_backbone = RRDBNet(num_in_ch=s2_inchannel, num_feat=decoder_channel, num_block=23, num_grow_ch=32)

#         self.affine_fcn = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel*2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])
#         self.offset_guide = DCN_Guide(in_channels=decoder_channel*2, out_channels=decoder_channel)
#         self.super_resolution = Super_Resolution(decoder_channel, frame=frame)
#         self.s2_last_layer = make_layer(RRDB, 1, num_feat=decoder_channel, num_grow_ch=32)


#         self.building_layer = nn.Sequential(
#             nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
#             nn.LeakyReLU(),
#             nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)
#         )

#         self.rgb_layer = nn.Sequential(
#             nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
#             nn.LeakyReLU(),
#             nn.Conv2d(in_channels=decoder_channel, out_channels=3, kernel_size=1, stride=1, padding=0)
#         )

#     def _feature_affine(self, time_serise_sentinel2):
#         B, total_frame, C, H, W = time_serise_sentinel2.shape
#         aligned_features = []
#         T15 = time_serise_sentinel2[:, -2]
#         for t in range(total_frame):
#             curr_s2 = time_serise_sentinel2[:, t]
#             feat_pair = torch.cat([curr_s2, T15], dim=1)
#             A = self.affine_fcn[t](feat_pair)
#             aligned_feat = torch.mul(curr_s2, A).squeeze(1)
#             aligned_features.append(aligned_feat)
#         aligned_features = torch.stack(aligned_features, dim=1)
#         return aligned_features
 
#     def _encoder_feature_constraint(self, inputs, device, low_decoder_features, outputs):
#         B, frame, _, h, w = low_decoder_features.shape
#         outputs["loss"] = 0
#         for frame_id in range(frame - 2):
#             frame_feature = F.normalize(low_decoder_features[:, frame_id])
#             outputs["loss"] += F.mse_loss(frame_feature, F.normalize(low_decoder_features[:, -2].detach()), reduction='mean')
#         return outputs

#     def forward(self, inputs, device, outputs, H, W):
#         s2_images_angles = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
#         B, T, C, h, w = s2_images_angles.shape
#         s2_images = s2_images_angles[:, :, :13].view(B*T, -1, h, w)
#         s2_angles = F.interpolate(s2_images_angles[:, :, 13:].view(B*T, -1, h, w), size=(h*4, w*4), mode='nearest')
#         s2_angles = rearrange(s2_angles, '(b t) c h w -> b t c h w', b=B, t=T, c=4, h=h*4, w=w*4)


#         low_encoder_features, rgb_out = self.s2_backbone(s2_images)
#         low_encoder_features_affine = self._feature_affine(low_encoder_features.view(B, T, -1, h*4, w*4))
#         if self.training:
#             outputs = self._encoder_feature_constraint(inputs, device, low_encoder_features_affine, outputs)

#         low_encoder_features_affine = torch.cat((low_encoder_features_affine, s2_angles), dim=2)
#         low_encoder_features_offset = self.offset_guide(current_frames=low_encoder_features_affine[:, -2], referent_frames=low_encoder_features_affine[:, :-1])

#         s2_superresolutionr_feature = self.super_resolution(low_encoder_features_offset)
#         s2_superresolutionr_feature = self.s2_last_layer(s2_superresolutionr_feature)

#         low_building = self.building_layer(s2_superresolutionr_feature)
#         low_all_rgb = self.rgb_layer(s2_superresolutionr_feature)

#         # print(low_outs.shape)

#         outputs["pre_low_building"] = F.interpolate(low_building, size=(H, W), mode='bilinear', align_corners=True)
#         # outputs["pre_low_last_rgb"] = F.interpolate(rgb_out.view(B, T, -1, h*4, w*4)[:, -2], size=(H, W), mode='bilinear', align_corners=True)
#         outputs["pre_all_rgb"] = F.interpolate(low_all_rgb, size=(H, W), mode='bilinear', align_corners=True)
#         outputs["pre_low_features"] = F.interpolate(s2_superresolutionr_feature, size=(H, W), mode='bilinear', align_corners=True)
#         return outputs

# class UNetDiscriminatorSN(nn.Module):
#     """Defines a U-Net discriminator with spectral normalization (SN)

#     It is used in Real-ESRGAN: Training Real-World Blind Super-Resolution with Pure Synthetic Data.

#     Arg:
#         num_in_ch (int): Channel number of inputs. Default: 3.
#         num_feat (int): Channel number of base intermediate features. Default: 64.
#         skip_connection (bool): Whether to use skip connections between U-Net. Default: True.
#     """

#     def __init__(self, num_in_ch, num_feat=64, skip_connection=True):
#         super(UNetDiscriminatorSN, self).__init__()
#         self.skip_connection = skip_connection
#         norm = spectral_norm
#         # the first convolution
#         self.conv0 = nn.Conv2d(num_in_ch, num_feat, kernel_size=3, stride=1, padding=1)
#         # downsample
#         self.conv1 = norm(nn.Conv2d(num_feat, num_feat * 2, 4, 2, 1, bias=False))
#         self.conv2 = norm(nn.Conv2d(num_feat * 2, num_feat * 4, 4, 2, 1, bias=False))
#         self.conv3 = norm(nn.Conv2d(num_feat * 4, num_feat * 8, 4, 2, 1, bias=False))
#         # upsample
#         self.conv4 = norm(nn.Conv2d(num_feat * 8, num_feat * 4, 3, 1, 1, bias=False))
#         self.conv5 = norm(nn.Conv2d(num_feat * 4, num_feat * 2, 3, 1, 1, bias=False))
#         self.conv6 = norm(nn.Conv2d(num_feat * 2, num_feat, 3, 1, 1, bias=False))
#         # extra convolutions
#         self.conv7 = norm(nn.Conv2d(num_feat, num_feat, 3, 1, 1, bias=False))
#         self.conv8 = norm(nn.Conv2d(num_feat, num_feat, 3, 1, 1, bias=False))
#         self.conv9 = nn.Conv2d(num_feat, 1, 3, 1, 1)

#     def forward(self, x):
#         # downsample
#         x0 = F.leaky_relu(self.conv0(x), negative_slope=0.2, inplace=True)
#         x1 = F.leaky_relu(self.conv1(x0), negative_slope=0.2, inplace=True)
#         x2 = F.leaky_relu(self.conv2(x1), negative_slope=0.2, inplace=True)
#         x3 = F.leaky_relu(self.conv3(x2), negative_slope=0.2, inplace=True)

#         # upsample
#         x3 = F.interpolate(x3, scale_factor=2, mode='bilinear', align_corners=False)
#         x4 = F.leaky_relu(self.conv4(x3), negative_slope=0.2, inplace=True)

#         if self.skip_connection:
#             x4 = x4 + x2
#         x4 = F.interpolate(x4, scale_factor=2, mode='bilinear', align_corners=False)
#         x5 = F.leaky_relu(self.conv5(x4), negative_slope=0.2, inplace=True)

#         if self.skip_connection:
#             x5 = x5 + x1
#         x5 = F.interpolate(x5, scale_factor=2, mode='bilinear', align_corners=False)
#         x6 = F.leaky_relu(self.conv6(x5), negative_slope=0.2, inplace=True)

#         if self.skip_connection:
#             x6 = x6 + x0

#         # extra convolutions
#         out = F.leaky_relu(self.conv7(x6), negative_slope=0.2, inplace=True)
#         out = F.leaky_relu(self.conv8(out), negative_slope=0.2, inplace=True)
#         out = self.conv9(out)

#         return out


# class LBD_S12v28(nn.Module):
#     def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
#         super().__init__()
#         self.high_net = UANet_pvt(num_classes=num_classes)
#         pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/13.pt"
#         self.high_net = self._load_weight(self.high_net, pre_train_model_path)
#         for p in self.high_net.parameters():
#             p.requires_grad = False

#         self.low_net = S2_Building_Damage(s2_inchannel=s2_inchannel, frame=frame, num_classes=num_classes, damage_classes=damage_classes)


#     def _load_weight(self, net, pre_train_model_path):
#         if pre_train_model_path is not None:
#             net.train()
#             current_model_dict = net.state_dict()
#             loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
#             new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
#             net.load_state_dict(new_state_dict, strict=False)
#         return net
 

#     def forward(self, inputs, device):
#         outputs = {}
#         # with torch.no_grad():
#         B, _, H, W = inputs[0].to(device).to(torch.float32).shape
#         outputs = self.high_net(inputs[0].to(device).to(torch.float32), device, mode="pre", outputs=outputs)
#         outputs = self.low_net(inputs, device, outputs, H, W)
#         # outputs = self.high_net(outputs["pre_all_rgb"].to(device).to(torch.float32), device, mode="srlow", outputs=outputs)
#         return outputs



# if __name__ == "__main__":
#     os.environ['CUDA_VISIBLE_DEVICES'] = '3'
#     # deep_model = UANet_Sentinel().cuda()

#     deep_model = LBD_S12v28(s2_inchannel=13, frame=16, num_classes=2, damage_classes=2).cuda()
#     bs = 1
#     hight_image = torch.rand(bs, 3, 384, 384).cuda()
#     low_images1 = torch.rand(bs, 16, 17, 24, 24).cuda()
#     low_images2 = torch.rand(bs, 1, 17, 24, 24).cuda()
#     pre_label = torch.rand(bs, 384, 384).cuda()
#     post_label = torch.rand(bs, 384, 384).cuda()
#     inputs = [hight_image, low_images1, low_images2, pre_label, post_label]
#     device = torch.device("cuda")

#     outputss = deep_model(inputs, device)

#     flops, params = profile(deep_model, inputs=(inputs, device, ))
#     print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')


import warnings
warnings.filterwarnings('ignore')

import os
from thop import profile
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn.utils import spectral_norm

from einops import rearrange
from dcn_v2 import DCN
from model.UANet_modify import *

class ResidualDenseBlock(nn.Module):
    """Residual Dense Block.

    Used in RRDB block in ESRGAN.

    Args:
        num_feat (int): Channel number of intermediate features.
        num_grow_ch (int): Channels for each growth.
    """

    def __init__(self, num_feat=64, num_grow_ch=32):
        super(ResidualDenseBlock, self).__init__()
        self.conv1 = nn.Conv2d(num_feat, num_grow_ch, 3, 1, 1)
        self.conv2 = nn.Conv2d(num_feat + num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv3 = nn.Conv2d(num_feat + 2 * num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv4 = nn.Conv2d(num_feat + 3 * num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv5 = nn.Conv2d(num_feat + 4 * num_grow_ch, num_feat, 3, 1, 1)

        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)

    def forward(self, x):
        x1 = self.lrelu(self.conv1(x))
        x2 = self.lrelu(self.conv2(torch.cat((x, x1), 1)))
        x3 = self.lrelu(self.conv3(torch.cat((x, x1, x2), 1)))
        x4 = self.lrelu(self.conv4(torch.cat((x, x1, x2, x3), 1)))
        x5 = self.conv5(torch.cat((x, x1, x2, x3, x4), 1))
        # Empirically, we use 0.2 to scale the residual for better performance
        return x5 * 0.2 + x

class RRDB(nn.Module):
    """Residual in Residual Dense Block.

    Used in RRDB-Net in ESRGAN.

    Args:
        num_feat (int): Channel number of intermediate features.
        num_grow_ch (int): Channels for each growth.
    """

    def __init__(self, num_feat, num_grow_ch=32):
        super(RRDB, self).__init__()
        self.rdb1 = ResidualDenseBlock(num_feat, num_grow_ch)
        self.rdb2 = ResidualDenseBlock(num_feat, num_grow_ch)
        self.rdb3 = ResidualDenseBlock(num_feat, num_grow_ch)

    def forward(self, x):
        out = self.rdb1(x)
        out = self.rdb2(out)
        out = self.rdb3(out)
        # Empirically, we use 0.2 to scale the residual for better performance
        return out * 0.2 + x

def make_layer(basic_block, num_basic_block, **kwarg):
    """Make layers by stacking the same blocks.

    Args:
        basic_block (nn.module): nn.module class for basic block.
        num_basic_block (int): number of blocks.

    Returns:
        nn.Sequential: Stacked blocks in nn.Sequential.
    """
    layers = []
    for _ in range(num_basic_block):
        layers.append(basic_block(**kwarg))
    return nn.Sequential(*layers)

class RRDBNet(nn.Module):
    """Networks consisting of Residual in Residual Dense Block, which is used in ESRGAN.

    ESRGAN: Enhanced Super-Resolution Generative Adversarial Networks.

    We extend ESRGAN for scale x2 and scale x1.
    Note: This is one option for scale 1, scale 2 in RRDBNet.
    We first employ the pixel-unshuffle (an inverse operation of pixelshuffle to reduce the spatial size
    and enlarge the channel size before feeding inputs into the main ESRGAN architecture.

    Args:
        num_in_ch (int): Channel number of inputs.
        num_out_ch (int): Channel number of outputs.
        num_feat (int): Channel number of intermediate features.
            Default: 64
        num_block (int): Block number in the trunk network. Defaults: 23
        num_grow_ch (int): Channels for each growth. Default: 32.
    """

    def __init__(self, num_in_ch=17, num_feat=64, num_block=23, num_grow_ch=32):
        super(RRDBNet, self).__init__()
        self.conv_first = nn.Conv2d(num_in_ch, num_feat, 3, 1, 1)
        self.body = make_layer(RRDB, num_block, num_feat=num_feat, num_grow_ch=num_grow_ch)
        self.conv_body = nn.Conv2d(num_feat, num_feat, 3, 1, 1)

        self.conv_up1 = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_up2 = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_hr = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        self.conv_last = nn.Conv2d(num_feat, 3, 3, 1, 1)
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)

    def forward(self, x):
        feat = self.conv_first(x)
        body_feat = self.conv_body(self.body(feat))
        feat = feat + body_feat
        feat = self.lrelu(self.conv_up1(F.interpolate(feat, scale_factor=2, mode='nearest')))
        feat = self.lrelu(self.conv_up2(F.interpolate(feat, scale_factor=2, mode='nearest')))
        feat = self.lrelu(self.conv_hr(feat))
        grey_out = self.conv_last(feat)
        # grey_out = torch.clamp(grey_out, 0.0, 1.0)
        return feat, grey_out

class DCN_guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.dcn = DCN(decoder_channel*2, decoder_channel, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, current_frames, referent_frames):
        B, frame, C, H, W = referent_frames.shape
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

        assert current_frames.shape == referent_frames.shape

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")

        # s2_angle = s2_angle - s2_angle[:, -1:,]
        # s2_angle = rearrange(s2_angle, "b t c h w -> (b t) c h w")

        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)

        fusion_frames = self.dcn(fusion_frames)
        fusion_frames = self.relu(fusion_frames)

        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames

class time_fusion(nn.Module):
    def __init__(self, decoder_channel, frame):
        super().__init__()
        self.conv_time_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=int(decoder_channel*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.LeakyReLU(inplace=True),
        )
        self.conv_spatial_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(inplace=True),
        )

    def forward(self, x):
        B, frame, C, H, W = x.shape
        x = rearrange(x, "b t c h w -> b c (h w) t", b=B, t=frame, c=C, h=H, w=W)
        fusion_frames = rearrange(self.conv_time_fusion(x).squeeze(-1), "b (t c) (h w) -> (b t) c h w", b=B, t=frame, c=C, h=H, w=W)
        fusion_frames = rearrange(self.conv_spatial_fusion(fusion_frames), "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)
        return fusion_frames

class SuperResolution(nn.Module):
    def __init__(self, decoder_channel, frame):
        super().__init__()

        self.ts = time_fusion(decoder_channel, frame)
        self.sr = nn.Sequential(           
            nn.PixelShuffle(upscale_factor=4),
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(inplace=True),
        )

    def forward(self, x):
        x = self.ts(x)
        x = self.sr(rearrange(x, "b t c h w -> b (t c) h w"))
        return x

class ResBlock(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.conv_path = nn.Sequential(
            nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1, stride=1),
            # nn.BatchNorm2d(decoder_channel),
            nn.LeakyReLU(inplace=True),
            nn.Conv2d(decoder_channel, decoder_channel, kernel_size=3, padding=1, stride=1),
            # nn.BatchNorm2d(decoder_channel),
        )
        self.relu = nn.LeakyReLU(inplace=True)

    def forward(self, x):
        x = self.relu(self.conv_path(x) + x)
        return x

class S2_Building_Damage(nn.Module):
    def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        decoder_channel = 64

        self.s2_backbone = RRDBNet(num_in_ch=s2_inchannel, num_feat=decoder_channel, num_block=23, num_grow_ch=32)

        self.fcn = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])
        self.offset_guide = DCN_guide(decoder_channel)

        self.sr_net = SuperResolution(decoder_channel, frame=frame)
        self.s2_last_layer = make_layer(RRDB, 1, num_feat=decoder_channel, num_grow_ch=32)

        self.building_layer = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=1, stride=1, padding=0),
            nn.LeakyReLU(),
            nn.Conv2d(in_channels=decoder_channel, out_channels=3, kernel_size=1, stride=1, padding=0)
        )

    def _feature_transfer(self, time_serise_feature):
        B, total_frame, C, H, W = time_serise_feature.shape
        # print("shape is", B, total_frame, C, H, W)
        aligned_features = []
        T15 = time_serise_feature[:, -2]
        
        for t in range(total_frame):  # 对于T1到T17
            curr_feat = time_serise_feature[:, t]  # [B, C, H, W]
            feat_pair = torch.cat([curr_feat, T15], dim=1)
            A = self.fcn[t](feat_pair)
            aligned_feat = torch.mul(curr_feat, A).squeeze(1)
            aligned_features.append(aligned_feat)
        aligned_features = torch.stack(aligned_features, dim=1) # [B, 17, C, H, W]
        return aligned_features
 
    def _encoder_feature_constraint(self, inputs, device, low_decoder_features, outputs):
        B, frame, _, h, w = low_decoder_features.shape
        outputs["loss"] = 0
        for frame_id in range(frame - 2):
            frame_feature = F.normalize(low_decoder_features[:, frame_id])
            outputs["loss"] += F.mse_loss(frame_feature, F.normalize(low_decoder_features[:, -2].detach()), reduction='mean')
        return outputs

    def forward(self, inputs, device, outputs, H, W):
        s2_images = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
        B, frame, C, h, w = s2_images.shape
        s2_images = F.interpolate(s2_images.view(B*frame, C, h, w), size=(h//2, w//2), mode='bilinear', align_corners=True)#.view(B, frame, C, h//2, w//2)

        low_encoder_features, pre_last_grey = self.s2_backbone(s2_images)
        low_encoder_features = low_encoder_features.view(B, frame, -1, h*2, w*2)
        pre_last_grey = pre_last_grey.view(B, frame, -1, h*2, w*2)
        # outputs["pre_low_features"] = F.interpolate(low_encoder_features, size=(H//4, W//4), mode='bilinear', align_corners=True)
        outputs["pre_last_rgb"] = F.interpolate(pre_last_grey[:, -2, :], size=(H, W), mode='bilinear', align_corners=True)

        low_encoder_features = self._feature_transfer(low_encoder_features)
        low_pre_features_offset = self.offset_guide(current_frames=low_encoder_features[:, -2], referent_frames=low_encoder_features[:, :-1])

        if self.training:
            outputs = self._encoder_feature_constraint(inputs, device, low_encoder_features, outputs)

        low_pre_features = self.sr_net(low_pre_features_offset)
        low_pre_features = self.s2_last_layer(low_pre_features)
        low_outs = self.building_layer(low_pre_features)
        # outputs["pre_low_features"] = F.interpolate(low_pre_features, size=(H//4, W//4), mode='bilinear', align_corners=True)
        # outputs["pre_low_building"] = F.interpolate(low_outs[:,0:2], size=(H, W), mode='bilinear', align_corners=True)
        outputs["pre_all_rgb"] = F.interpolate(low_outs, size=(H, W), mode='bilinear', align_corners=True)
        # outputs["pre_low_gray"] = torch.clamp(outputs["pre_low_gray"], 0.0, 1.0)

        return outputs

class UNetDiscriminatorSN(nn.Module):
    """Defines a U-Net discriminator with spectral normalization (SN)

    It is used in Real-ESRGAN: Training Real-World Blind Super-Resolution with Pure Synthetic Data.

    Arg:
        num_in_ch (int): Channel number of inputs. Default: 3.
        num_feat (int): Channel number of base intermediate features. Default: 64.
        skip_connection (bool): Whether to use skip connections between U-Net. Default: True.
    """

    def __init__(self, num_in_ch, num_feat=64, skip_connection=True):
        super(UNetDiscriminatorSN, self).__init__()
        self.skip_connection = skip_connection
        norm = spectral_norm
        # the first convolution
        self.conv0 = nn.Conv2d(num_in_ch, num_feat, kernel_size=3, stride=1, padding=1)
        # downsample
        self.conv1 = norm(nn.Conv2d(num_feat, num_feat * 2, 4, 2, 1, bias=False))
        self.conv2 = norm(nn.Conv2d(num_feat * 2, num_feat * 4, 4, 2, 1, bias=False))
        self.conv3 = norm(nn.Conv2d(num_feat * 4, num_feat * 8, 4, 2, 1, bias=False))
        # upsample
        self.conv4 = norm(nn.Conv2d(num_feat * 8, num_feat * 4, 3, 1, 1, bias=False))
        self.conv5 = norm(nn.Conv2d(num_feat * 4, num_feat * 2, 3, 1, 1, bias=False))
        self.conv6 = norm(nn.Conv2d(num_feat * 2, num_feat, 3, 1, 1, bias=False))
        # extra convolutions
        self.conv7 = norm(nn.Conv2d(num_feat, num_feat, 3, 1, 1, bias=False))
        self.conv8 = norm(nn.Conv2d(num_feat, num_feat, 3, 1, 1, bias=False))
        self.conv9 = nn.Conv2d(num_feat, 1, 3, 1, 1)

    def forward(self, x):
        # downsample
        x0 = F.leaky_relu(self.conv0(x), negative_slope=0.2, inplace=True)
        x1 = F.leaky_relu(self.conv1(x0), negative_slope=0.2, inplace=True)
        x2 = F.leaky_relu(self.conv2(x1), negative_slope=0.2, inplace=True)
        x3 = F.leaky_relu(self.conv3(x2), negative_slope=0.2, inplace=True)

        # upsample
        x3 = F.interpolate(x3, scale_factor=2, mode='bilinear', align_corners=False)
        x4 = F.leaky_relu(self.conv4(x3), negative_slope=0.2, inplace=True)

        if self.skip_connection:
            x4 = x4 + x2
        x4 = F.interpolate(x4, scale_factor=2, mode='bilinear', align_corners=False)
        x5 = F.leaky_relu(self.conv5(x4), negative_slope=0.2, inplace=True)

        if self.skip_connection:
            x5 = x5 + x1
        x5 = F.interpolate(x5, scale_factor=2, mode='bilinear', align_corners=False)
        x6 = F.leaky_relu(self.conv6(x5), negative_slope=0.2, inplace=True)

        if self.skip_connection:
            x6 = x6 + x0

        # extra convolutions
        out = F.leaky_relu(self.conv7(x6), negative_slope=0.2, inplace=True)
        out = F.leaky_relu(self.conv8(out), negative_slope=0.2, inplace=True)
        out = self.conv9(out)

        return out


class LBD_S12v27(nn.Module):
    def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        self.high_net = UANet_pvt(num_classes=num_classes+1)
        pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/13.pt"
        self.high_net = self._load_weight(self.high_net, pre_train_model_path)
        for p in self.high_net.parameters():
            p.requires_grad = False

        self.low_net = S2_Building_Damage(s2_inchannel=s2_inchannel, frame=frame, num_classes=num_classes, damage_classes=damage_classes)


    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
 

    def forward(self, inputs, device):
        outputs = {}
        with torch.no_grad():
            B, _, H, W = inputs[0].to(device).to(torch.float32).shape
            outputs = self.high_net(inputs[0].to(device).to(torch.float32), device, mode="pre", outputs=outputs)
        outputs = self.low_net(inputs, device, outputs, H, W)

        return outputs



if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '3'
    # deep_model = UANet_Sentinel().cuda()

    deep_model = LBD_S12v27(s2_inchannel=17, frame=16, num_classes=2, damage_classes=2).cuda()
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384).cuda()
    low_images1 = torch.rand(bs, 16, 17, 48, 48).cuda()
    low_images2 = torch.rand(bs, 1, 17, 48, 48).cuda()
    pre_label = torch.rand(bs, 384, 384).cuda()
    post_label = torch.rand(bs, 384, 384).cuda()
    inputs = [hight_image, low_images1, low_images2, pre_label, post_label]
    device = torch.device("cuda")

    outputss = deep_model(inputs, device)

    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
