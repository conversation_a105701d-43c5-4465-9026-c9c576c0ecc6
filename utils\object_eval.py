import enum
import os
import numpy as np
from PIL import Image
from tqdm import tqdm
import warnings
from glob import glob
warnings.filterwarnings("ignore")
from skimage import measure

# evel the damage F1 score using building object

def obeject_vote(building_mask, pred_damage):

    damage_cls_list = [1, 2]
    local_mask = building_mask
    labeled_local, nums = measure.label(local_mask, connectivity=2, background=0, return_num=True)
    region_idlist = np.unique(labeled_local)
    if len(region_idlist) > 1:
        dam_mask = pred_damage
        new_dam = local_mask.copy()
        for region_id in region_idlist:
            if all(local_mask[local_mask == region_id]) == 0:
                continue
            region_dam_count = [int(np.sum(dam_mask[labeled_local == region_id] == dam_cls_i)) * cls_weight for dam_cls_i, cls_weight in zip(damage_cls_list, [1., 2.5,])]
            dam_index = np.argmax(region_dam_count) + 1
            new_dam = np.where(labeled_local == region_id, dam_index, new_dam)
    else:
        new_dam = local_mask.copy()
    return new_dam.astype(np.float32)


def object_damage_eval(base_path, model_name, label_name, result_name):
    city_name = "" # Rubizhne Mariupol Haiti Nepal Yushu left right santa harvey
    test_names = glob(os.path.join(base_path, model_name, result_name, f"*{city_name}**.tif"))
    test_names.sort()
    # print(os.path.join(base_path, model_name, result_name, f"*{city_name}**.tif"))
    count = 0
    TP, FP, FN, TN = 0, 0, 0, 0
    for id, test_name in tqdm(enumerate(test_names)):
        if "_2.tif" in test_name or "disaster_2" in test_name:
            print(test_name)
            continue
        name = os.path.basename(test_name)
        lab_path = os.path.join(base_path, label_name, name)
        pre_path = os.path.join(base_path, model_name, result_name, name)
        lab = np.array(Image.open(lab_path)).astype(np.float32)

        if label_name=="post_label" and "hawaii-" in test_name:
            lab = np.where(lab>=3, 1, 0)

        # object voting by using building label and damage prediction
        # building_label = np.array(Image.open(lab_path.replace(label_name, "pre_label").replace("_post_disaster_", "_pre_disaster_"))).astype(np.float32)
        building_label = np.array(Image.open(pre_path.replace("_post_disaster_", "_pre_disaster_").replace("post_", "pre_"))).astype(np.float32)
        building_label = np.where(building_label>=0.5, 1, 0)
        pre = np.array(Image.open(pre_path))
        pre = np.where(pre>=0.5, 1, 0)
        pre = pre + building_label
        pre = obeject_vote(building_label, pre)
        pre = np.where(pre>=2, 1, 0)
        
        lab = np.where(lab>=0.5, 1, 0)
        pre = np.where(pre>=0.5, 1, 0)
        c = 1
        TP += np.logical_and(pre == c, lab == c).sum()
        FN += np.logical_and(pre != c, lab == c).sum()
        FP += np.logical_and(pre == c, lab != c).sum()
        TN += np.logical_and(pre != c, lab != c).sum()
        count += 1

    IOU = TP / (TP + FP + FN)
    Recall = TP / (TP + FN)
    Precision = TP / (TP + FP)
    F1 = 2 * Recall * Precision / (Precision + Recall)
    
    OA = (TP + TN) / (TP + FP + FN + TN)

    print(count, F1, IOU, Recall, Precision, OA)

if __name__ == "__main__":

    # hawaii
    mode_name = "lbdv21/santa_LA2hawaii/s1_santa_LA2hawaii"
    object_damage_eval(base_path="./result/hawaii", model_name=f"{mode_name}", label_name="post_label", result_name="post_s1_result")

    mode_name = "lbdv21/santa_LA2hawaii/santa_LA2hawaii"
    object_damage_eval(base_path="./result/hawaii", model_name=f"{mode_name}", label_name="post_label", result_name="post_s2_result")
