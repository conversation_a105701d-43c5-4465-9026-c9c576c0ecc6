# Building Segmentation RDDM (残差扩散去噪模型)

基于CVPR2024 RDDM工作的建筑物分割边缘优化模型，专门用于改善Sentinel-2建筑物分割结果的边缘质量。

## 功能特点

- **残差扩散学习**: 使用RDDM的残差思想学习从低质量到高质量分割的映射
- **边缘优化**: 专门针对建筑物分割边缘过于圆滑的问题
- **条件生成**: 以低质量分割为条件，生成高质量分割结果
- **多尺度支持**: 支持不同分辨率的输入数据

## 文件结构

```
ssr/
├── building_seg_rddm.py           # 主要模型实现
├── building_rddm_config.py        # 配置文件
├── sentinel_building_rddm_experiment.py  # Sentinel-2专用实验脚本
├── res_df.py                      # RDDM核心实现（已存在）
└── README_Building_RDDM.md        # 使用说明
```

## 快速开始

### 1. 环境准备

确保已安装以下依赖：
```bash
pip install torch torchvision
pip install numpy opencv-python pillow matplotlib tqdm
pip install gdal  # 可选，用于处理TIFF文件
```

### 2. 数据准备

准备您的数据，目录结构如下：
```
data/
├── low_seg/          # 低质量分割结果（Sentinel-2）
│   ├── image_001.png
│   ├── image_002.png
│   └── ...
└── high_seg/         # 高质量分割结果（参考标准）
    ├── image_001.png
    ├── image_002.png
    └── ...
```

**数据格式要求**：
- 支持PNG, JPG, TIFF格式
- 单通道灰度图像
- 像素值范围：0-255（0=背景，255=建筑物）
- low_seg和high_seg文件名需要对应

### 3. 创建示例数据（测试用）

```python
from ssr.building_seg_rddm import create_sample_data

# 创建200个示例数据对
create_sample_data('./sample_data', 200)
```

### 4. 训练模型

#### 使用默认配置训练：
```bash
cd ssr
python sentinel_building_rddm_experiment.py --mode train --data_root ./sample_data
```

#### 使用不同配置：
```bash
# 快速测试配置
python sentinel_building_rddm_experiment.py --mode train --config quick --data_root ./sample_data

# 高质量配置
python sentinel_building_rddm_experiment.py --mode train --config high_quality --data_root ./sample_data

# Sentinel-2专用配置
python sentinel_building_rddm_experiment.py --mode train --config sentinel --data_root ./your_sentinel_data
```

### 5. 推理使用

```bash
python sentinel_building_rddm_experiment.py \
    --mode inference \
    --input ./input_low_seg.png \
    --output ./output_enhanced_seg.png \
    --checkpoint ./results/building_rddm/best_model.pt
```

## 编程接口使用

### 基本使用

```python
from ssr.building_seg_rddm import BuildingRDDM, BuildingSegmentationDataset
from ssr.building_rddm_config import get_config

# 1. 创建配置
config = get_config('sentinel')

# 2. 创建模型
model = BuildingRDDM(
    image_size=config.image_size,
    channels=1,
    dim=config.dim,
    device='cuda'
)

# 3. 加载预训练模型
model.load_model('./results/building_rddm/best_model.pt')

# 4. 增强分割结果
import torch
low_seg = torch.randn(1, 1, 256, 256)  # 您的低质量分割输入
enhanced_seg = model.enhance_segmentation(low_seg)
```

### 自定义训练

```python
# 准备数据
low_seg_paths = ['path1.png', 'path2.png', ...]
high_seg_paths = ['path1.png', 'path2.png', ...]

train_dataset = BuildingSegmentationDataset(
    low_seg_paths, high_seg_paths, 
    image_size=256, mode='train'
)

# 训练
model.train_model(
    train_dataset=train_dataset,
    batch_size=8,
    num_epochs=100,
    learning_rate=1e-4
)
```

## 配置说明

### 主要配置参数

- `image_size`: 输入图像尺寸（默认256，Sentinel-2建议512）
- `dim`: 模型维度（默认64，更大值获得更好质量）
- `timesteps`: 扩散步数（默认1000）
- `sampling_timesteps`: 采样步数（默认250）
- `batch_size`: 批次大小
- `learning_rate`: 学习率

### 预定义配置

1. **quick**: 快速测试配置，较小模型，适合验证
2. **default**: 默认配置，平衡质量和速度
3. **high_quality**: 高质量配置，更大模型，更好效果
4. **sentinel**: Sentinel-2专用配置，512尺寸

## 实验结果

模型会自动保存以下内容：
- `best_model.pt`: 最佳模型权重
- `model_epoch_*.pt`: 定期保存的检查点
- `samples_epoch_*.png`: 训练过程中的样本可视化
- `experiment.log`: 训练日志

## 评估指标

模型使用以下指标评估：
- **IoU**: 交并比
- **Dice**: Dice系数
- **Precision**: 精确率
- **Recall**: 召回率

## 注意事项

1. **内存需求**: 扩散模型需要较大内存，建议使用GPU
2. **训练时间**: 完整训练可能需要数小时到数天
3. **数据质量**: 高质量参考数据对训练效果至关重要
4. **超参数调整**: 根据具体数据调整配置参数

## 故障排除

### 常见问题

1. **CUDA内存不足**：减小batch_size或image_size
2. **训练不收敛**：调整学习率或检查数据质量
3. **生成结果模糊**：增加sampling_timesteps或使用更大模型

### 调试技巧

```python
# 检查数据加载
dataset = BuildingSegmentationDataset(low_paths, high_paths)
sample = dataset[0]
print(f"Low seg shape: {sample[1].shape}, High seg shape: {sample[0].shape}")

# 可视化数据
import matplotlib.pyplot as plt
plt.figure(figsize=(10, 5))
plt.subplot(1, 2, 1)
plt.imshow(sample[1].squeeze(), cmap='gray')
plt.title('Low Quality')
plt.subplot(1, 2, 2)
plt.imshow(sample[0].squeeze(), cmap='gray')
plt.title('High Quality')
plt.show()
```

## 扩展功能

### 自定义损失函数

```python
# 在building_seg_rddm.py中修改
class CustomBuildingRDDM(BuildingRDDM):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 添加边缘损失
        self.edge_weight = 2.0
    
    def compute_edge_loss(self, pred, target):
        # 实现边缘损失计算
        pass
```

### 多尺度训练

```python
# 使用不同尺寸的数据进行训练
scales = [256, 512, 1024]
for scale in scales:
    dataset = BuildingSegmentationDataset(
        low_paths, high_paths, 
        image_size=scale
    )
    # 训练...
```

## 联系方式

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者

## 许可证

本项目基于原RDDM工作，请遵循相应的开源许可证。
