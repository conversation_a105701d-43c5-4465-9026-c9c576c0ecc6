"""
Sentinel-2 Building Segmentation RDDM Experiment
专门针对Sentinel-2建筑物分割的RDDM实验

使用方法：
1. 准备数据：low_seg (Sentinel-2分割结果) 和 high_seg (高分辨率参考)
2. 运行训练：python sentinel_building_rddm_experiment.py --mode train
3. 运行推理：python sentinel_building_rddm_experiment.py --mode inference
"""

import argparse
import torch
import torch.nn.functional as F
import numpy as np
import cv2
from PIL import Image
import os
import matplotlib.pyplot as plt
from tqdm import tqdm
import logging
from pathlib import Path

# 导入我们的模块
from building_seg_rddm import BuildingRDDM, BuildingSegmentationDataset
from building_rddm_config import get_config


class SentinelBuildingDataset(BuildingSegmentationDataset):
    """
    专门为Sentinel-2建筑物分割设计的数据集
    """
    def __init__(self, low_seg_paths, high_seg_paths, image_size=512, mode='train'):
        super().__init__(low_seg_paths, high_seg_paths, image_size, mode)
        
    def load_segmentation(self, path):
        """加载分割结果，支持多种格式"""
        if path.endswith('.tif') or path.endswith('.tiff'):
            # 使用GDAL加载TIFF文件
            try:
                from osgeo import gdal
                dataset = gdal.Open(path)
                seg = dataset.ReadAsArray()
                if len(seg.shape) == 3:
                    seg = seg[0]  # 取第一个通道
            except ImportError:
                print("GDAL not available, using PIL instead")
                seg = np.array(Image.open(path).convert('L'))
        else:
            # 使用PIL加载其他格式
            seg = np.array(Image.open(path).convert('L'))
        
        # 归一化到[0,1]
        seg = seg.astype(np.float32)
        if seg.max() > 1:
            seg = seg / 255.0
        
        # 确保是二值分割（建筑物/非建筑物）
        seg = (seg > 0.5).astype(np.float32)
            
        return seg
    
    def apply_edge_enhancement(self, low_seg, high_seg):
        """
        应用边缘增强，突出边缘差异
        """
        # 计算边缘
        low_edges = cv2.Canny((low_seg * 255).astype(np.uint8), 50, 150) / 255.0
        high_edges = cv2.Canny((high_seg * 255).astype(np.uint8), 50, 150) / 255.0
        
        # 边缘差异作为额外信息
        edge_diff = high_edges - low_edges
        
        return edge_diff
    
    def __getitem__(self, idx):
        # 加载低质量和高质量分割结果
        low_seg = self.load_segmentation(self.low_seg_paths[idx])
        high_seg = self.load_segmentation(self.high_seg_paths[idx])
        
        # 调整尺寸
        low_seg = cv2.resize(low_seg, (self.image_size, self.image_size), interpolation=cv2.INTER_NEAREST)
        high_seg = cv2.resize(high_seg, (self.image_size, self.image_size), interpolation=cv2.INTER_NEAREST)
        
        # 数据增强（仅训练时）
        if self.mode == 'train':
            # 随机翻转
            if torch.rand(1) > 0.5:
                low_seg = np.fliplr(low_seg)
                high_seg = np.fliplr(high_seg)
            if torch.rand(1) > 0.5:
                low_seg = np.flipud(low_seg)
                high_seg = np.flipud(high_seg)
            
            # 随机旋转
            if torch.rand(1) > 0.5:
                k = np.random.randint(1, 4)
                low_seg = np.rot90(low_seg, k)
                high_seg = np.rot90(high_seg, k)
            
            # 添加轻微噪声到低质量分割
            if torch.rand(1) > 0.7:
                noise = np.random.normal(0, 0.02, low_seg.shape)
                low_seg = np.clip(low_seg + noise, 0, 1)
        
        # 转换为tensor并添加通道维度
        low_seg = torch.from_numpy(low_seg).unsqueeze(0)  # [1, H, W]
        high_seg = torch.from_numpy(high_seg).unsqueeze(0)  # [1, H, W]
        
        return [high_seg, low_seg]  # [target, condition]


def setup_logging(log_file):
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )


def evaluate_model(model, test_dataset, device, save_dir=None):
    """
    评估模型性能
    """
    model.diffusion.eval()
    
    metrics = {
        'iou': [],
        'dice': [],
        'precision': [],
        'recall': []
    }
    
    test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=1, shuffle=False)
    
    with torch.no_grad():
        for i, (high_seg, low_seg) in enumerate(tqdm(test_loader, desc="Evaluating")):
            low_seg = low_seg.to(device)
            high_seg = high_seg.to(device)
            
            # 生成预测
            pred_seg = model.enhance_segmentation(low_seg)
            
            # 二值化
            pred_binary = (pred_seg > 0.5).float()
            high_binary = (high_seg > 0.5).float()
            
            # 计算指标
            intersection = (pred_binary * high_binary).sum()
            union = (pred_binary + high_binary).clamp(0, 1).sum()
            
            iou = intersection / (union + 1e-8)
            dice = 2 * intersection / (pred_binary.sum() + high_binary.sum() + 1e-8)
            
            tp = intersection
            fp = pred_binary.sum() - intersection
            fn = high_binary.sum() - intersection
            
            precision = tp / (tp + fp + 1e-8)
            recall = tp / (tp + fn + 1e-8)
            
            metrics['iou'].append(iou.item())
            metrics['dice'].append(dice.item())
            metrics['precision'].append(precision.item())
            metrics['recall'].append(recall.item())
            
            # 保存可视化结果
            if save_dir and i < 10:  # 只保存前10个样本
                fig, axes = plt.subplots(1, 4, figsize=(16, 4))
                
                axes[0].imshow(low_seg.cpu().squeeze(), cmap='gray')
                axes[0].set_title('Low Quality Input')
                axes[0].axis('off')
                
                axes[1].imshow(pred_seg.cpu().squeeze(), cmap='gray')
                axes[1].set_title('Enhanced Output')
                axes[1].axis('off')
                
                axes[2].imshow(high_seg.cpu().squeeze(), cmap='gray')
                axes[2].set_title('Ground Truth')
                axes[2].axis('off')
                
                # 差异图
                diff = torch.abs(pred_seg - high_seg).cpu().squeeze()
                axes[3].imshow(diff, cmap='hot')
                axes[3].set_title('Difference')
                axes[3].axis('off')
                
                plt.tight_layout()
                plt.savefig(os.path.join(save_dir, f'eval_sample_{i}.png'), dpi=150)
                plt.close()
    
    # 计算平均指标
    avg_metrics = {k: np.mean(v) for k, v in metrics.items()}
    
    return avg_metrics


def train_experiment(config):
    """训练实验"""
    logging.info("Starting training experiment...")
    
    # 创建目录
    config.create_directories()
    
    # 获取数据路径
    try:
        low_seg_paths, high_seg_paths = config.get_data_paths()
        logging.info(f"Found {len(low_seg_paths)} low-quality and {len(high_seg_paths)} high-quality samples")
    except FileNotFoundError as e:
        logging.error(f"Data not found: {e}")
        return
    
    # 划分数据集
    split_idx = int((1 - config.val_split) * len(low_seg_paths))
    train_low = low_seg_paths[:split_idx]
    train_high = high_seg_paths[:split_idx]
    val_low = low_seg_paths[split_idx:]
    val_high = high_seg_paths[split_idx:]
    
    # 创建数据集
    train_dataset = SentinelBuildingDataset(train_low, train_high, config.image_size, 'train')
    val_dataset = SentinelBuildingDataset(val_low, val_high, config.image_size, 'test')
    
    logging.info(f"Train samples: {len(train_dataset)}, Val samples: {len(val_dataset)}")
    
    # 创建模型
    model = BuildingRDDM(
        image_size=config.image_size,
        channels=config.channels,
        dim=config.dim,
        dim_mults=config.dim_mults,
        timesteps=config.timesteps,
        sampling_timesteps=config.sampling_timesteps,
        loss_type=config.loss_type,
        objective=config.objective,
        device=config.device
    )
    
    # 加载预训练模型（如果有）
    if config.checkpoint_path and os.path.exists(config.checkpoint_path):
        model.load_model(config.checkpoint_path)
        logging.info(f"Loaded checkpoint from {config.checkpoint_path}")
    
    # 训练模型
    model.train_model(
        train_dataset=train_dataset,
        val_dataset=val_dataset,
        batch_size=config.batch_size,
        num_epochs=config.num_epochs,
        learning_rate=config.learning_rate,
        save_every=config.save_every,
        results_folder=config.results_folder
    )
    
    # 评估最终模型
    eval_dir = os.path.join(config.results_folder, 'evaluation')
    os.makedirs(eval_dir, exist_ok=True)
    
    metrics = evaluate_model(model, val_dataset, config.device, eval_dir)
    logging.info("Final evaluation metrics:")
    for metric, value in metrics.items():
        logging.info(f"  {metric}: {value:.4f}")


def inference_experiment(config, input_path, output_path, checkpoint_path):
    """推理实验"""
    logging.info("Starting inference experiment...")
    
    # 创建模型
    model = BuildingRDDM(
        image_size=config.image_size,
        channels=config.channels,
        dim=config.dim,
        dim_mults=config.dim_mults,
        timesteps=config.timesteps,
        sampling_timesteps=config.sampling_timesteps,
        device=config.device
    )
    
    # 加载模型
    if not os.path.exists(checkpoint_path):
        logging.error(f"Checkpoint not found: {checkpoint_path}")
        return
    
    model.load_model(checkpoint_path)
    logging.info(f"Loaded model from {checkpoint_path}")
    
    # 加载输入图像
    if input_path.endswith('.tif') or input_path.endswith('.tiff'):
        try:
            from osgeo import gdal
            dataset = gdal.Open(input_path)
            low_seg = dataset.ReadAsArray()
            if len(low_seg.shape) == 3:
                low_seg = low_seg[0]
        except ImportError:
            low_seg = np.array(Image.open(input_path).convert('L'))
    else:
        low_seg = np.array(Image.open(input_path).convert('L'))
    
    # 预处理
    low_seg = low_seg.astype(np.float32)
    if low_seg.max() > 1:
        low_seg = low_seg / 255.0
    
    # 调整尺寸
    original_size = low_seg.shape
    low_seg = cv2.resize(low_seg, (config.image_size, config.image_size), interpolation=cv2.INTER_NEAREST)
    
    # 转换为tensor
    low_seg_tensor = torch.from_numpy(low_seg).unsqueeze(0).unsqueeze(0)  # [1, 1, H, W]
    
    # 推理
    enhanced_seg = model.enhance_segmentation(low_seg_tensor)
    enhanced_seg = enhanced_seg.cpu().squeeze().numpy()
    
    # 调整回原始尺寸
    enhanced_seg = cv2.resize(enhanced_seg, (original_size[1], original_size[0]), interpolation=cv2.INTER_NEAREST)
    
    # 保存结果
    enhanced_seg_uint8 = (enhanced_seg * 255).astype(np.uint8)
    Image.fromarray(enhanced_seg_uint8).save(output_path)
    
    logging.info(f"Enhanced segmentation saved to {output_path}")
    
    # 创建对比图
    comparison_path = output_path.replace('.png', '_comparison.png')
    fig, axes = plt.subplots(1, 2, figsize=(12, 6))
    
    axes[0].imshow(cv2.resize(low_seg, (original_size[1], original_size[0])), cmap='gray')
    axes[0].set_title('Original Low Quality')
    axes[0].axis('off')
    
    axes[1].imshow(enhanced_seg, cmap='gray')
    axes[1].set_title('Enhanced High Quality')
    axes[1].axis('off')
    
    plt.tight_layout()
    plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    logging.info(f"Comparison saved to {comparison_path}")


def main():
    parser = argparse.ArgumentParser(description='Sentinel-2 Building RDDM Experiment')
    parser.add_argument('--mode', choices=['train', 'inference'], required=True,
                       help='Experiment mode')
    parser.add_argument('--config', default='sentinel', 
                       choices=['default', 'quick', 'high_quality', 'sentinel'],
                       help='Configuration to use')
    parser.add_argument('--data_root', type=str, default='./data',
                       help='Root directory for data')
    parser.add_argument('--checkpoint', type=str, default=None,
                       help='Checkpoint path for inference or resume training')
    parser.add_argument('--input', type=str, default=None,
                       help='Input image path for inference')
    parser.add_argument('--output', type=str, default=None,
                       help='Output image path for inference')
    
    args = parser.parse_args()
    
    # 获取配置
    config = get_config(args.config)
    config.data_root = args.data_root
    
    if args.checkpoint:
        config.checkpoint_path = args.checkpoint
    
    # 设置日志
    log_file = os.path.join(config.results_folder, 'experiment.log')
    os.makedirs(config.results_folder, exist_ok=True)
    setup_logging(log_file)
    
    if args.mode == 'train':
        train_experiment(config)
    elif args.mode == 'inference':
        if not args.input or not args.output or not args.checkpoint:
            logging.error("For inference mode, --input, --output, and --checkpoint are required")
            return
        inference_experiment(config, args.input, args.output, args.checkpoint)


if __name__ == "__main__":
    main()
