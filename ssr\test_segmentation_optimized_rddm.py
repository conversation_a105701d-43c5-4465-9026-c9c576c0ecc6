#!/usr/bin/env python3
"""
测试针对分割任务优化的RDDM模型
专门验证[0,1]范围分割结果的处理
"""

import torch
import torch.nn.functional as F
from building_rddm_denoiser import BuildingSegmentationRDDM

def create_segmentation_data(batch_size=1, height=64, width=64):
    """创建真实的分割数据，范围[0,1]"""
    
    # 创建高质量分割：清晰的建筑物轮廓
    high_seg = torch.zeros(batch_size, 2, height, width)
    
    # 添加几个建筑物区域
    high_seg[:, 0, 15:25, 15:25] = 1.0  # 建筑物1
    high_seg[:, 0, 35:50, 20:40] = 1.0  # 建筑物2
    high_seg[:, 0, 20:30, 45:55] = 1.0  # 建筑物3
    
    # 背景 = 1 - 前景
    high_seg[:, 1] = 1.0 - high_seg[:, 0]
    
    # 创建低质量分割：模糊边界，噪声
    low_seg = high_seg.clone()
    
    # 添加高斯模糊
    kernel_size = 5
    sigma = 1.0
    kernel = torch.exp(-torch.arange(-(kernel_size//2), kernel_size//2 + 1)**2 / (2*sigma**2))
    kernel = kernel / kernel.sum()
    kernel_2d = kernel.unsqueeze(0) * kernel.unsqueeze(1)
    kernel_2d = kernel_2d.unsqueeze(0).unsqueeze(0)
    
    # 对前景和背景分别应用模糊
    low_seg[:, 0:1] = F.conv2d(low_seg[:, 0:1], kernel_2d, padding=kernel_size//2)
    low_seg[:, 1:2] = F.conv2d(low_seg[:, 1:2], kernel_2d, padding=kernel_size//2)
    
    # 添加噪声
    noise = torch.randn_like(low_seg) * 0.05
    low_seg = low_seg + noise
    
    # 重新归一化到[0,1]并确保概率和为1
    low_seg = torch.clamp(low_seg, 0, 1)
    low_seg = F.softmax(low_seg, dim=1)
    
    return high_seg, low_seg

def test_segmentation_rddm():
    """测试分割优化的RDDM"""
    print("=== 测试分割优化RDDM ===")
    
    # 创建模型
    model = BuildingSegmentationRDDM(
        unet_dim=32,
        unet_dim_mults=(1, 2, 4),
        channels=2,
        timesteps=100,
        sampling_timesteps=20,
        objective='pred_res_noise',
        sum_scale=0.05,  # 保守的alpha系数
        beta_scale=0.0005  # 很小的beta系数
    )
    
    print(f"模型参数: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建分割数据
    high_seg, low_seg = create_segmentation_data(batch_size=2, height=128, width=128)
    
    print(f"高质量分割范围: [{high_seg.min():.3f}, {high_seg.max():.3f}]")
    print(f"低质量分割范围: [{low_seg.min():.3f}, {low_seg.max():.3f}]")
    print(f"高质量前景比例: {high_seg[:, 0].mean():.3f}")
    print(f"低质量前景比例: {low_seg[:, 0].mean():.3f}")
    
    # 验证概率和
    high_sum = high_seg.sum(dim=1).mean()
    low_sum = low_seg.sum(dim=1).mean()
    print(f"高质量概率和: {high_sum:.6f}")
    print(f"低质量概率和: {low_sum:.6f}")
    
    # 测试训练前向传播
    model.train()
    loss, pred_x_start = model(high_seg, low_seg)
    
    print(f"训练损失: {loss.item():.6f}")
    print(f"预测结果形状: {pred_x_start.shape}")
    print(f"预测结果范围: [{pred_x_start.min():.3f}, {pred_x_start.max():.3f}]")
    
    # 验证预测结果的概率和
    pred_sum = pred_x_start.sum(dim=1).mean()
    print(f"预测概率和: {pred_sum:.6f}")
    
    # 计算各种误差
    l1_error = F.l1_loss(pred_x_start, high_seg)
    l2_error = F.mse_loss(pred_x_start, high_seg)
    print(f"L1误差: {l1_error:.6f}")
    print(f"L2误差: {l2_error:.6f}")
    
    return True

def test_inference_modes():
    """测试不同推理模式"""
    print("\n=== 测试推理模式 ===")
    
    model = BuildingSegmentationRDDM(
        unet_dim=16,
        unet_dim_mults=(1, 2),
        channels=2,
        timesteps=50,
        sampling_timesteps=10,
        sum_scale=0.05,
        beta_scale=0.0005
    )
    
    # 创建测试数据
    high_seg, low_seg = create_segmentation_data(batch_size=1, height=64, width=64)
    
    model.eval()
    with torch.no_grad():
        # 测试直接增强模式
        enhanced_direct = model.direct_enhance(low_seg, num_steps=10)
        
        # 计算改进效果
        original_error = F.l1_loss(low_seg, high_seg)
        enhanced_error = F.l1_loss(enhanced_direct, high_seg)
        improvement = original_error - enhanced_error
        
        print(f"直接增强模式:")
        print(f"  原始误差: {original_error:.6f}")
        print(f"  增强后误差: {enhanced_error:.6f}")
        print(f"  改进程度: {improvement:.6f}")
        print(f"  增强结果范围: [{enhanced_direct.min():.3f}, {enhanced_direct.max():.3f}]")
        print(f"  增强结果概率和: {enhanced_direct.sum(dim=1).mean():.6f}")
        
        # 验证前景比例变化
        original_fg = low_seg[:, 0].mean()
        enhanced_fg = enhanced_direct[:, 0].mean()
        target_fg = high_seg[:, 0].mean()
        
        print(f"  前景比例 - 原始: {original_fg:.3f}, 增强: {enhanced_fg:.3f}, 目标: {target_fg:.3f}")
        
        if improvement > 0:
            print("  ✓ 成功改进分割质量")
        else:
            print("  ✗ 未能改进分割质量")
    
    return improvement > 0

def test_alpha_beta_sensitivity():
    """测试alpha和beta系数的敏感性"""
    print("\n=== 测试系数敏感性 ===")
    
    # 测试不同的系数组合
    configs = [
        {"sum_scale": 0.01, "beta_scale": 0.0001, "name": "极保守"},
        {"sum_scale": 0.05, "beta_scale": 0.0005, "name": "保守"},
        {"sum_scale": 0.1, "beta_scale": 0.001, "name": "中等"},
        {"sum_scale": 0.2, "beta_scale": 0.002, "name": "激进"},
    ]
    
    high_seg, low_seg = create_segmentation_data(batch_size=1, height=64, width=64)
    
    for config in configs:
        print(f"\n--- {config['name']} (sum_scale={config['sum_scale']}, beta_scale={config['beta_scale']}) ---")
        
        try:
            model = BuildingSegmentationRDDM(
                unet_dim=8,
                unet_dim_mults=(1, 2),
                channels=2,
                timesteps=20,
                sampling_timesteps=5,
                sum_scale=config['sum_scale'],
                beta_scale=config['beta_scale']
            )
            
            # 测试训练
            model.train()
            loss, pred_x_start = model(high_seg, low_seg)
            
            print(f"  训练损失: {loss.item():.6f}")
            print(f"  预测范围: [{pred_x_start.min():.3f}, {pred_x_start.max():.3f}]")
            print(f"  概率和: {pred_x_start.sum(dim=1).mean():.6f}")
            
            # 测试推理
            model.eval()
            with torch.no_grad():
                enhanced = model.direct_enhance(low_seg, num_steps=3)
                
                original_error = F.l1_loss(low_seg, high_seg)
                enhanced_error = F.l1_loss(enhanced, high_seg)
                improvement = original_error - enhanced_error
                
                print(f"  改进程度: {improvement:.6f}")
                print(f"  状态: {'✓' if improvement > 0 else '✗'}")
                
        except Exception as e:
            print(f"  ✗ 失败: {str(e)}")

def test_edge_preservation():
    """测试边界保持效果"""
    print("\n=== 测试边界保持 ===")
    
    model = BuildingSegmentationRDDM(
        unet_dim=16,
        unet_dim_mults=(1, 2),
        channels=2,
        timesteps=30,
        sampling_timesteps=5,
        sum_scale=0.05,
        beta_scale=0.0005
    )
    
    # 创建有清晰边界的测试数据
    high_seg, low_seg = create_segmentation_data(batch_size=1, height=64, width=64)
    
    model.eval()
    with torch.no_grad():
        enhanced = model.direct_enhance(low_seg, num_steps=5)
        
        # 计算边界清晰度
        def compute_edge_sharpness(seg):
            sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32).view(1, 1, 3, 3)
            sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32).view(1, 1, 3, 3)
            
            fg = seg[:, 0:1]  # 前景通道
            grad_x = F.conv2d(fg, sobel_x, padding=1)
            grad_y = F.conv2d(fg, sobel_y, padding=1)
            edge_magnitude = torch.sqrt(grad_x**2 + grad_y**2)
            return edge_magnitude.mean().item()
        
        original_sharpness = compute_edge_sharpness(low_seg)
        enhanced_sharpness = compute_edge_sharpness(enhanced)
        target_sharpness = compute_edge_sharpness(high_seg)
        
        print(f"边界清晰度:")
        print(f"  原始: {original_sharpness:.6f}")
        print(f"  增强: {enhanced_sharpness:.6f}")
        print(f"  目标: {target_sharpness:.6f}")
        
        sharpness_improvement = enhanced_sharpness - original_sharpness
        print(f"  清晰度改进: {sharpness_improvement:.6f}")
        
        if sharpness_improvement > 0:
            print("  ✓ 成功提升边界清晰度")
        else:
            print("  ✗ 未能提升边界清晰度")
    
    return sharpness_improvement > 0

if __name__ == "__main__":
    success1 = test_segmentation_rddm()
    success2 = test_inference_modes()
    test_alpha_beta_sensitivity()
    success3 = test_edge_preservation()
    
    print(f"\n=== 总结 ===")
    print(f"基础功能测试: {'✓' if success1 else '✗'}")
    print(f"推理模式测试: {'✓' if success2 else '✗'}")
    print(f"边界保持测试: {'✓' if success3 else '✗'}")
    
    if all([success1, success2, success3]):
        print("✓ 分割优化RDDM测试全部通过！")
        print("建议使用sum_scale=0.05, beta_scale=0.0005的保守设置")
    else:
        print("✗ 部分测试失败，需要进一步调优")
