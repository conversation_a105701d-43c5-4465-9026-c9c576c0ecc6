import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.transforms as T

from thop import profile
from einops import rearrange
from dcn_v2 import DCN, DCN_offset

# from model.UANet_modify import *
import sys, os
sys.path.append("/mnt/d2/zxq/BDS12")
from model.UANet_modify import *
from ssr.archs.rrdbnet_arch import SSR_RRDBNet, RRDB
from ssr.ShiftNet import ShiftNet

def make_layer(basic_block, num_basic_block, **kwarg):
    """Make layers by stacking the same blocks.

    Args:
        basic_block (nn.module): nn.module class for basic block.
        num_basic_block (int): number of blocks.

    Returns:
        nn.Sequential: Stacked blocks in nn.Sequential.
    """
    layers = []
    for _ in range(num_basic_block):
        layers.append(basic_block(**kwarg))
    return nn.Sequential(*layers)

class DCN_guide(nn.Module):
    def __init__(self, decoder_channel):
        super().__init__()
        self.dcn = DCN_offset(decoder_channel*2, decoder_channel, kernel_size=(3, 3), stride=1, padding=1, deformable_groups=1)
        self.relu = nn.LeakyReLU(inplace=False)

    def forward(self, current_frames, referent_frames, s2_angles):
        B, frame, C, H, W = referent_frames.shape
        current_frames = current_frames.unsqueeze(1).repeat(1, frame, 1, 1, 1)

        assert current_frames.shape == referent_frames.shape

        current_frames1 = rearrange(current_frames, "b t c h w -> (b t) c h w")
        referent_frames1 = rearrange(referent_frames, "b t c h w -> (b t) c h w")
        # current_angle = rearrange(s2_angles, "b t c h w -> (b t) c h w")
        # referent_angle = rearrange(s2_angles[:, -1].unsqueeze(1).repeat(1, frame, 1, 1, 1), "b t c h w -> (b t) c h w")

        fusion_frames = torch.cat((current_frames1, referent_frames1), dim=1)
        # fusion_angle = torch.cat((current_frames1, current_angle, referent_frames1, referent_angle), dim=1)
        # print(fusion_frames.shape)
        fusion_angle = None

        fusion_frames = self.dcn(fusion_frames, fusion_angle)
        fusion_frames = self.relu(fusion_frames)

        fusion_frames = rearrange(fusion_frames, "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)

        return fusion_frames

class time_fusion(nn.Module):
    def __init__(self, decoder_channel, frame):
        super().__init__()
        self.conv_time_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=int(decoder_channel*frame), kernel_size=(1, frame), stride=(1, 1)),
            nn.LeakyReLU(inplace=False),
        )
        self.conv_spatial_fusion = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(inplace=False),
        )

    def forward(self, x):
        B, frame, C, H, W = x.shape
        x = rearrange(x, "b t c h w -> b c (h w) t", b=B, t=frame, c=C, h=H, w=W)
        fusion_frames = rearrange(self.conv_time_fusion(x).squeeze(-1), "b (t c) (h w) -> (b t) c h w", b=B, t=frame, c=C, h=H, w=W)
        fusion_frames = rearrange(self.conv_spatial_fusion(fusion_frames), "(b t) c h w -> b t c h w", b=B, t=frame, c=C, h=H, w=W)
        return fusion_frames

class SuperResolution(nn.Module):
    def __init__(self, decoder_channel, frame):
        super().__init__()

        self.ts = time_fusion(decoder_channel, frame)
        self.sr = nn.Sequential(           
            nn.PixelShuffle(upscale_factor=4),
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(inplace=False),
        )

    def forward(self, x):
        x = self.ts(x)
        x = self.sr(rearrange(x, "b t c h w -> b (t c) h w"))
        return x

class S2_Building_Damage(nn.Module):
    def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        s2_backbone = SSR_RRDBNet(num_in_ch=3, num_out_ch=3, scale=4, num_feat=64, num_block=23, num_grow_ch=32)
        self.s2_backbone = self._construct_low_backbone(s2_backbone, s2_inchannel)

        # for p in self.s2_backbone.parameters():
        #     p.requires_grad =False

        decoder_channel = 64
        self.fcn = nn.ModuleList([nn.Sequential(nn.Conv2d(decoder_channel * 2, decoder_channel, kernel_size=3, padding=1, bias=False)) for _ in range(frame+1)])
        self.offset_guide = DCN_guide(decoder_channel)

        self.sr_net = SuperResolution(decoder_channel, frame=frame)
        self.s2_last_layer = make_layer(RRDB, 3, num_feat=decoder_channel, num_grow_ch=32)

        self.building_layer = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channel, out_channels=decoder_channel, kernel_size=3, stride=1, padding=1),
            nn.LeakyReLU(),
            nn.Conv2d(in_channels=decoder_channel, out_channels=num_classes+1, kernel_size=1, stride=1, padding=0)
        )

    def _construct_low_backbone(self, net, inchannels):
        pre_train_model_path = "/mnt/d2/zxq/satals-super-resolution-experiments/ssr/pre_trains/esrgan_1S2.pth"
        net = self._load_weight(net, pre_train_model_path)
        conv1_weights = net.conv_first.weight
        conv1_weights_mean = conv1_weights.mean(dim=1, keepdim=True)
        new_conv1_weights = conv1_weights_mean.repeat(1, inchannels, 1, 1)
        net.conv_first.in_inchannel = inchannels
        net.conv_first.weight.data = new_conv1_weights        
        return net

    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            loaded_state_dict = loaded_state_dict['params_ema']
            net.load_state_dict(loaded_state_dict, strict=True)
            print("load pre_train success")
        return net
 

    def _feature_transfer(self, time_serise_feature):
        B, total_frame, C, H, W = time_serise_feature.shape
        # print("shape is", B, total_frame, C, H, W)
        aligned_features = []
        T15 = time_serise_feature[:, -2]
        
        for t in range(total_frame):  # 对于T1到T17
            curr_feat = time_serise_feature[:, t]  # [B, C, H, W]
            feat_pair = torch.cat([curr_feat, T15], dim=1)
            A = self.fcn[t](feat_pair)
            aligned_feat = torch.mul(curr_feat, A).squeeze(1)
            aligned_features.append(aligned_feat)
        aligned_features = torch.stack(aligned_features, dim=1) # [B, 17, C, H, W]
        return aligned_features
 
    def _encoder_feature_constraint(self, inputs, device, low_decoder_features, outputs):
        B, frame, _, h, w = low_decoder_features.shape
        outputs["loss"] = 0
        for frame_id in range(frame - 2):
            frame_feature = F.normalize(low_decoder_features[:, frame_id])
            outputs["loss"] += F.mse_loss(frame_feature, F.normalize(low_decoder_features[:, -2].detach()), reduction='mean')
        return outputs


    def forward(self, inputs, device, outputs = {}):
        B, _, H, W = inputs[0].to(device).to(torch.float32).shape
        shift_dx_dy = inputs[5].to(device).to(torch.long)

        s2_images = torch.cat((inputs[1].to(device).to(torch.float32), inputs[2].to(device).to(torch.float32)), dim=1)
        # s2_angles = s2_images[:, :-1, -4:]
        B, frame, C, h, w = s2_images.shape
        
        low_encoder_features, low_rgb_out = self.s2_backbone(s2_images.view(B*frame, C, h, w))# [:, :-4,]
        low_encoder_features = low_encoder_features.view(B, frame, -1, h*4, w*4)
        # print(low_encoder_features.shape, low_rgb_out.shape)
        low_rgb_out = low_rgb_out.view(B, frame, -1, h, w)
        outputs["pre_last_grey"] = F.interpolate(low_rgb_out[:, -2, :], size=(H, W), mode='bilinear', align_corners=True)

        low_encoder_features = self._feature_transfer(low_encoder_features)
        low_pre_features_offset = self.offset_guide(current_frames=low_encoder_features[:, -2], referent_frames=low_encoder_features[:, :-1], s2_angles=None)

        if self.training:
            outputs = self._encoder_feature_constraint(inputs, device, low_encoder_features, outputs)

        low_pre_features = self.sr_net(low_pre_features_offset)
        low_pre_features = self.s2_last_layer(low_pre_features)
        low_outs = self.building_layer(low_pre_features)
        low_outs = F.interpolate(low_outs, scale_factor=1.25, mode='bilinear', align_corners=True)
        # outputs["pre_low_building"] = F.interpolate(low_outs[:, :2], scale_factor=1.25, mode='bilinear', align_corners=True)
        outputs["pre_low_features"] = F.interpolate(low_pre_features, scale_factor=1.25, mode='bilinear', align_corners=True)
        # outputs["pre_low_gray"] = F.interpolate(low_outs[:, 2:], scale_factor=5, mode='bilinear', align_corners=True)
        # print(low_outs.shape, outputs["pre_low_features"].shape)
        # outputs["pre_low_building"] = low_outs[:, :2]
        # outputs["pre_low_features"] = low_pre_features
        # outputs["pre_low_gray"] = low_outs[:, 2:]
        crop_size = 384
        cropped_outs = torch.zeros((B, 3, crop_size, crop_size), dtype=low_outs.dtype, device=low_outs.device)
        cropped_feat = torch.zeros((B, 64, crop_size, crop_size), dtype=low_outs.dtype, device=low_outs.device)

        for i in range(B):
            dy, dx = shift_dx_dy[i]
            # dy, dx = int(dy), int(dx)  # 保证是整数
            cropped_outs[i] = low_outs[i, :, dy:dy+crop_size, dx:dx+crop_size]
            cropped_feat[i] = outputs["pre_low_features"][i, :, dy:dy+crop_size, dx:dx+crop_size]

        outputs["pre_low_building"] = cropped_outs[:, :2]
        outputs["pre_low_gray"] = cropped_outs[:, 2:]
        outputs["pre_low_features"] = cropped_feat
        # print(outputs["pre_low_building"].shape, outputs["pre_low_features"].shape, outputs["pre_low_gray"].shape)

        return outputs


class LRBDv1(nn.Module):
    def __init__(self, s2_inchannel=17, frame=16, num_classes=2, damage_classes=2):
        super().__init__()
        self.high_net = UANet_pvt(num_classes=num_classes)
        pre_train_model_path = "/mnt/d2/zxq/BDS12/result/xBD_512/UANet_pvt/13.pt"
        self.high_net = self._load_weight(self.high_net, pre_train_model_path)
        self.low_net = S2_Building_Damage(s2_inchannel=s2_inchannel, frame=frame, num_classes=num_classes, damage_classes=damage_classes)
        self.shift_net = ShiftNet(in_channel=3)

    def _load_weight(self, net, pre_train_model_path):
        if pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=torch.device("cuda:0"))
            new_state_dict={k:v if v.size()==current_model_dict[k].size() else current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net

    def forward(self, inputs, device):
        outputs = {}
        # with torch.no_grad():
        B, _, H, W = inputs[0].to(device).to(torch.float32).shape
        outputs = self.high_net(inputs[0].to(device).to(torch.float32), device, mode="pre", outputs=outputs)
        outputs = self.low_net(inputs, device, outputs)
        return outputs

        # shift outputs
        high_gray = T.Grayscale(num_output_channels=1)(inputs[0].to(device).to(torch.float32))
        high_gray = F.interpolate(high_gray.detach(), scale_factor=0.25, mode="bilinear")
        high_label = F.one_hot(inputs[3].to(device).to(torch.long).detach(), num_classes=2).permute(0, 3, 1, 2)
        high_label = F.interpolate(high_label.to(torch.float32), scale_factor=0.25, mode="bilinear")

        # print(high_gray.shape, high_label.shape)
        shift_dx_dy = self.shift_net.forward(torch.cat((outputs["pre_low_building"], outputs["pre_low_gray"], high_label, high_gray), dim=1))
        # print(shift_dx_dy.shape, torch.cat((outputs["pre_low_building"], outputs["pre_low_gray"], high_label, high_gray), dim=1).shape)

        shift_out = self.shift_net.transform(shift_dx_dy, torch.cat((outputs["pre_low_building"], outputs["pre_low_gray"]), dim=1))
        # outputs["pre_low_building"] = shift_out[:, :2]
        # outputs["pre_low_gray"] =shift_out[:, 2:]
        # print(f"shift_out:{shift_out.shape}")
        outputs["pre_low_building"] = F.interpolate(shift_out[:, :2], size=(H, W), mode='bilinear', align_corners=True)
        # outputs["pre_low_features"] = F.interpolate(low_pre_features, size=(H//4, W//4), mode='bilinear', align_corners=True)
        outputs["pre_low_gray"] = F.interpolate(shift_out[:, 2:], size=(H, W), mode='bilinear', align_corners=True)
        return outputs





if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '3'
    # deep_model = UANet_Sentinel().cuda()
    torch.autograd.set_detect_anomaly(True)

    deep_model = LRBDv1(s2_inchannel=3).cuda()
    bs = 1
    hight_image = torch.rand(bs, 3, 384, 384).cuda()
    low_images1 = torch.rand(bs, 16, 3, 22, 22).cuda()
    low_images2 = torch.rand(bs, 1, 3, 22, 22).cuda()
    pre_label = torch.ones(bs, 384, 384).cuda()
    post_label = torch.ones(bs, 384, 384).cuda()
    shfit_dxdy = torch.ones(bs, 2).cuda()

    inputs = [hight_image, low_images1, low_images2, pre_label, post_label, shfit_dxdy]
    device = torch.device("cuda")

    outputss = deep_model(inputs, device)

    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')