# -*- coding: utf-8 -*-
# @Author: Your name
# @Date:   2024-01-10 12:57:50
# @Last Modified by:   Your name
# @Last Modified time: 2024-06-11 13:30:28
import sys
sys.path.append("/mnt/d2/zxq/BuildingDamage")
import argparse
import os
import torch
import torch.nn as nn
from torchvision import transforms
import torch.nn.functional as F
from tqdm import tqdm
import torch.multiprocessing
import numpy as np
import cv2
from PIL import Image
import math
from osgeo import gdal
from utils.cfg import py2cfg
from pathlib import Path
import math
torch.multiprocessing.set_sharing_strategy('file_system')

def generate_grid_points(image_shape, num_points=64):
    h, w,  = image_shape
    y_coords = np.linspace(0, h - 1, int(np.sqrt(num_points)))
    x_coords = np.linspace(0, w - 1, int(np.sqrt(num_points)))
    points = np.array([(x, y) for y in y_coords for x in x_coords])
    points = torch.from_numpy(np.array(points, dtype=np.float32)).unsqueeze(0)
    return points

def binary2boxes(img):
    img = np.where(img>=1, 1, img)
    boxes = []
    for c in np.sort(np.unique(img*(-1)))*(-1):
        if c == 0:
            continue
        new_img = np.where(img==c, 255, 0)
        _, new_img = cv2.threshold(new_img.astype(np.uint8), 0, 255, cv2.THRESH_BINARY)
        img_w, img_h = new_img.shape
        num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(new_img, connectivity=4)
        for stat in stats:
            [left_x, left_y, box_w, box_h, area] = stat
            if (box_w==img_w and box_h==img_h) or (box_w==0 and box_h==0):
                continue
            boxes.append([left_x, left_y, box_w, box_h])
    if boxes is None:
        boxes = torch.empty((1, 0, 4))
    else:
        boxes = torch.from_numpy(np.array(boxes, dtype=np.float32)).unsqueeze(0)
    return boxes

def pre_to_result(out, config):
    out = np.array(out).astype(np.float32)
    result = np.zeros((out.shape[0], out.shape[1], 3)).astype(np.int8)
    for i in range(len(config.label_keys)):
        result[out==i, :] = config.label_keys[i]
    return result


def load_net(model, device, model_path):
    net = model.to(device)
    net = torch.nn.DataParallel(net)
    net.load_state_dict(torch.load(model_path, map_location=device), False)
    net.eval()    
    return net

def up_scale(out_list, split_withd):
    up_out_list = []
    for i in range(len(out_list)):
        up_out_list.append(F.interpolate(out_list[i], size=(split_withd, split_withd), mode='bilinear', align_corners=True))
    return up_out_list

def out821(out_list):
    mask1 = out_list[0] + torch.flip(out_list[1], dims=[2]) + torch.flip(out_list[2], dims=[3]) + torch.flip(out_list[3], dims=[2, 3])
    # mask2 = out_list[4] + torch.flip(out_list[5], dims=[2]) + torch.flip(out_list[6], dims=[3]) + torch.flip(out_list[7], dims=[2, 3])
    out = mask1# + torch.rot90(mask2, k=1, dims=[3, 2])
    return out

def single_net_pre(image, base_size, scale, net, device):
    pre_result = torch.zeros(1, 2, base_size, base_size).to(torch.float32).to(device)
    post_result = torch.zeros(1, 5, base_size, base_size).to(torch.float32).to(device)
    for k in scale:
        size = int(k * base_size)
        img = image
        img = F.interpolate(image, size=(size, size), mode='bilinear', align_corners=True)

        img1 = img
        img2 = torch.flip(img1, dims=[2])
        img3 = torch.flip(img1, dims=[3])
        img4 = torch.flip(img1, dims=[2, 3])

        with torch.no_grad():
            [pre_result1, post_result1] = net(img1[:,:3,:,:].to(device), img1[:,3:,:,:].to(device))
            [pre_result2, post_result2] = net(img2[:,:3,:,:].to(device), img2[:,3:,:,:].to(device))
            [pre_result3, post_result3] = net(img3[:,:3,:,:].to(device), img3[:,3:,:,:].to(device))
            [pre_result4, post_result4] = net(img4[:,:3,:,:].to(device), img4[:,3:,:,:].to(device))
            pre_result_lists = up_scale([pre_result1, pre_result2, pre_result3, pre_result4], split_withd=base_size)
            post_result_lists = up_scale([post_result1, post_result2, post_result3, post_result4], split_withd=base_size)
        pre_result += out821(pre_result_lists)
        post_result += out821(post_result_lists)
    return pre_result, post_result

def save_to_tif(pred_result, save_path, test_image_path):
    """
    根据仿射矩阵，地图投影将像素概率矩阵存为tif图像
    """
    img = pred_result
    # print(np.max(img), np.min(img))

    im_geotrans, im_prof = [], []
    tif = gdal.Open(os.path.join(test_image_path))
    im_geotrans.append(tif.GetGeoTransform())
    im_prof.append(tif.GetProjection())
    if 'uint8' in img.dtype.name:
        datatype = gdal.GDT_Byte
    elif 'uint16' in img.dtype.name:
        datatype = gdal.GDT_UInt16
    else:
        datatype = gdal.GDT_Float32
    print(img.dtype.name)
    #判读数组维数
    if len(img.shape) == 3:
        im_bands, im_height, im_width = img.shape
    else:
        im_bands, (im_height, im_width) = 1, img.shape

    #创建文件
    driver  = gdal.GetDriverByName("GTiff")                #数据类型必须有，因为要计算需要多大内存空间
    dataset = driver.Create(save_path, im_width, im_height, im_bands, datatype)
    dataset.SetGeoTransform(im_geotrans[-1])              #写入仿射变换参数
    dataset.SetProjection(im_prof[-1])                    #写入投影
    if im_bands == 1:
        dataset.GetRasterBand(1).WriteArray(img)  #写入数组数据
    else:
        for i in range(im_bands):
            dataset.GetRasterBand(i+1).WriteArray(img[i])
    del dataset
    # print('保存成功')


def load_tif_path(image_path):
    # if "target" in image_path:
    #     image_array = np.array(cv2.imread(image_path))
    # else:
    dataset = gdal.Open(image_path)
    image_array = dataset.ReadAsArray()
    return image_array

def tta_test(image, base_size, net, device):
    outputs = {}

    def up_scale(out_list, split_withd):
        up_out_list = []
        for i in range(len(out_list)):
            up_out_list.append(F.interpolate(out_list[i], size=(split_withd, split_withd), mode='bilinear', align_corners=True))
            # up_out_list.append(torch.argmax(F.interpolate(out_list[i], size=(split_withd, split_withd), mode='bilinear', align_corners=True), dim=1, keepdim=True))
        return up_out_list
    
    def out821(out_list):
        mask1 = out_list[0] + torch.flip(out_list[1], dims=[2]) + torch.flip(out_list[2], dims=[3]) + torch.flip(out_list[3], dims=[2, 3])
        # mask2 = out_list[4] + torch.flip(out_list[5], dims=[2]) + torch.flip(out_list[6], dims=[3]) + torch.flip(out_list[7], dims=[2, 3])
        # out = mask1 + torch.rot90(mask2, k=1, dims=[3, 2])
        return mask1

        # 硬投票
        p1_aligned = out_list[0]  # 原始预测，无需处理
        p2_aligned = torch.flip(out_list[1], dims=[2])
        p3_aligned = torch.flip(out_list[2], dims=[3])
        p4_aligned = torch.flip(out_list[3], dims=[2, 3])

        preds = torch.cat([p1_aligned, p2_aligned, p3_aligned, p4_aligned], dim=1)
        final_prediction, _ = torch.mode(preds, dim=1, keepdim=True)
        # print(final_prediction.shape)

        return final_prediction

    scales = [1024,]
    # scales = [1024, 1280]
    for scale in scales:
        pre_result = torch.zeros(1, 2, base_size, base_size).to(torch.float32).to(device)
        post_result = torch.zeros(1, 5, base_size, base_size).to(torch.float32).to(device)
        image1 = F.interpolate(image, size=(scale, scale), mode='bilinear', align_corners=True)
        img1 = image1
        img2 = torch.flip(image1, dims=[2])
        img3 = torch.flip(image1, dims=[3])
        img4 = torch.flip(image1, dims=[2, 3])

        img5 = torch.rot90(image1, k=1, dims=[2, 3])
        img6 = torch.flip(img5, dims=[2])
        img7 = torch.flip(img5, dims=[3])
        img8 = torch.flip(img5, dims=[2, 3])

        with torch.no_grad():
            outputs1 = net([img1[:,:3,:,:].to(device), img1[:,3:,:,:].to(device)], device)
            outputs2 = net([img2[:,:3,:,:].to(device), img2[:,3:,:,:].to(device)], device)
            outputs3 = net([img3[:,:3,:,:].to(device), img3[:,3:,:,:].to(device)], device)
            outputs4 = net([img4[:,:3,:,:].to(device), img4[:,3:,:,:].to(device)], device)
            pre_result_lists = up_scale([outputs1["pre_buildings"], outputs2["pre_buildings"], outputs3["pre_buildings"], outputs4["pre_buildings"]], split_withd=base_size)
            post_result_lists = up_scale([outputs1["damage_buildings"], outputs2["damage_buildings"], outputs3["damage_buildings"], outputs4["damage_buildings"]], split_withd=base_size)
            
            # outputs5 = net([img5[:,:3,:,:].to(device), img5[:,3:,:,:].to(device)], device)
            # outputs6 = net([img6[:,:3,:,:].to(device), img6[:,3:,:,:].to(device)], device)
            # outputs7 = net([img7[:,:3,:,:].to(device), img7[:,3:,:,:].to(device)], device)
            # outputs8 = net([img8[:,:3,:,:].to(device), img8[:,3:,:,:].to(device)], device)
            # pre_result_lists = up_scale([outputs1["pre_buildings"], outputs2["pre_buildings"], outputs3["pre_buildings"], outputs4["pre_buildings"], outputs5["pre_buildings"], outputs6["pre_buildings"], outputs7["pre_buildings"], outputs8["pre_buildings"]], split_withd=base_size)
            # post_result_lists = up_scale([outputs1["damage_buildings"], outputs2["damage_buildings"], outputs3["damage_buildings"], outputs4["damage_buildings"],outputs5["damage_buildings"], outputs6["damage_buildings"], outputs7["damage_buildings"], outputs8["damage_buildings"]], split_withd=base_size)
        
        pre_result += out821(pre_result_lists)
        post_result += out821(post_result_lists)
    outputs["pre_buildings"] = pre_result
    outputs["damage_buildings"] = post_result
    return outputs


def test(config):
    device = torch.device(config.device)
    net = load_net(config.model, device, config.model_path)
    
    for id, pre_image_path in tqdm(enumerate(config.pre_image_paths)):
        name = os.path.basename(pre_image_path)    
        # pre_image  = Image.open(pre_image_path)
        # post_image = Image.open(config.post_image_paths[id])
        # pre_label  = Image.open(config.pre_label_paths[id])
        # post_label = Image.open(config.post_label_paths[id])
            
        # pre_image.save(os.path.join(config.pre_image_save_path, name))
        # post_image.save(os.path.join(config.post_image_save_path, name))

        pre_image  = load_tif_path(pre_image_path)
        post_image = load_tif_path(config.post_image_paths[id])

        pre_image  = np.array(pre_image)
        post_image = np.array(post_image)
        # pre_label  = np.array(pre_label)
        # post_label = np.array(post_label)
        # post_label[post_label>4] = 1 # 未分类

        # pre_image  = torch.from_numpy(np.array(pre_image/255., dtype=np.float32)).permute(2, 0, 1).unsqueeze(0).to(device=device, dtype=torch.float)
        # post_image = torch.from_numpy(np.array(post_image/255., dtype=np.float32)).repeat(3, 1, 1).permute(2, 0, 1).unsqueeze(0).to(device=device, dtype=torch.float)

        pre_image  = torch.from_numpy(np.array(pre_image/255., dtype=np.float32)).unsqueeze(0).to(device=device, dtype=torch.float)
        post_image = torch.from_numpy(np.array(post_image/255., dtype=np.float32)).repeat(3, 1, 1).unsqueeze(0).to(device=device, dtype=torch.float)

        with torch.no_grad():
            b, c, h, w = pre_image.shape
            split_withd = 1024
            r           = 0
            row         = int((h-r*2)/(split_withd-r*2))
            col         = int((w-r*2)/(split_withd-r*2))
            row_over    = int((h - r * 2) % (split_withd - r * 2) + r)
            col_over    = int((w - r * 2) % (split_withd - r * 2) + r)
            pre_results = np.zeros((h, w))
            post_results= np.zeros((h, w))
            print(pre_image.shape, post_image.shape)

            for i in range(row+1):
                for j in range(col+1):
            # for i in range(row):
            #     for j in range(col):
                    if i < row and j < col:
                        x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
                        y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd         
                        
                    #-------------------------------------------------#
                    #   裁剪的图像在最后一行
                    elif i == row and j < col:
                        x1, x2 = h-split_withd, h,
                        y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd
                    
                    #-------------------------------------------------#
                    #   裁剪的图像在最后一列
                    elif i < row and j == col:
                        x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
                        y1, y2 = w-split_withd, w

                    #-------------------------------------------------#
                    #   裁剪的图像在右下角
                    elif i == row and j == col:
                        x1, x2 = h-split_withd, h
                        y1, y2 = w-split_withd, w
                        
                    img1 = pre_image[:, :, x1:x2, y1:y2]
                    img2 = post_image[:, :, x1:x2, y1:y2]
                    # box1 = torch.empty(1, 0, 4)

                    # coords = generate_grid_points((h, w), num_points=144).to(device=device, dtype=torch.float)
                    # labels = torch.ones(1, coords.shape[1]).to(device=device, dtype=torch.float)
                    # box1 = pre_label[x1:x2, y1:y2]
                    # box1 = binary2boxes(box1)
                    # print(img1.shape, img2.shape)
                    # [pre_result, post_result] = net(img1, img2, coords, coords, labels, labels, box1, box1)
                    # outputs = net([img1, img2], device)
                    outputs = tta_test(image=torch.cat((img1, img2), dim=1), base_size=1024, net=net, device=device)

                    pre_result = outputs["pre_buildings"]
                    post_result = outputs["damage_buildings"]
                    # print(pre_result.shape, post_result.shape)

                    # [pre_result, post_result] = single_net_pre(torch.cat((pre_image, post_image), dim=1), config.size, [1.0], net, device)
                    pre_result = torch.argmax(pre_result, dim=1).cpu().detach().numpy().squeeze()
                    post_result = torch.argmax(post_result, dim=1).cpu().detach().numpy().squeeze()
                    # pre_result = torch.softmax(pre_result, dim=1)[:, 1, :, :].cpu().detach().numpy().squeeze()
                    # post_result = torch.softmax(post_result, dim=1)[:, 1, :, :].cpu().detach().numpy().squeeze()
                    # print(np.max(pre_result), np.min(pre_result))

                    # ------------------------------------------------- #
                    #   开始拼接结果
                    #   第一行需要特殊考虑：行数都为 0 : split_withd-r
                    if i == 0:
                        # ------------------------------------------------- #
                        #   左上角需要特殊考虑
                        if j == 0 :
                            rx1, rx2, ry1, ry2 = 0, split_withd-r, 0, split_withd-r
                            px1, px2, py1, py2 = 0, split_withd-r, 0, split_withd-r

                        # ------------------------------------------------- #
                        #   右上角需要特殊考虑                
                        elif j == col:
                            rx1, rx2, ry1, ry2 = 0, split_withd-r, w - col_over, w
                            px1, px2, py1, py2 = 0, split_withd-r, split_withd-col_over, split_withd

                        # ------------------------------------------------- #
                        #   第一行剩下的结果
                        else:
                            rx1, rx2, ry1, ry2 = 0, split_withd-r, j*(split_withd-2*r)+r, (j+1)*(split_withd-2*r)+r
                            px1, px2, py1, py2 = 0, split_withd-r, r, split_withd-r
            
                    # ------------------------------------------------- #
                    #   最后一行需要特殊考虑，行数都为 h - row_over : h
                    elif i == row:
                        # ------------------------------------------------- #
                        #   左下角需要特殊考虑
                        if j == 0 :
                            rx1, rx2, ry1, ry2 = h - row_over, h, 0, split_withd-r
                            px1, px2, py1, py2 = split_withd-row_over, split_withd, 0, split_withd-r

                        # ------------------------------------------------- #
                        #   右下上角需要特殊考虑
                        elif j == col:
                            rx1, rx2, ry1, ry2 = h - row_over, h, w - col_over, w
                            px1, px2, py1, py2 = split_withd-row_over, split_withd, split_withd-col_over, split_withd

                        # ------------------------------------------------- #
                        #   最后一行剩下的结果
                        else:
                            rx1, rx2, ry1, ry2 = h - row_over, h, j*(split_withd-2*r)+r, (j+1)*(split_withd-2*r)+r
                            px1, px2, py1, py2 = split_withd-row_over, split_withd, r, split_withd-r

                    # ------------------------------------------------- #
                    #   既不是第一行，又不是最后一行
                    else:
                        # ------------------------------------------------- #
                        #  如果在第一列, 特殊处理
                        if j == 0:
                            rx1, rx2, ry1, ry2 = i*(split_withd-2*r)+r, (i+1)*(split_withd-2*r)+r, 0,  split_withd-r
                            px1, px2, py1, py2 = r,split_withd-r, 0,split_withd-r
            
                        # ------------------------------------------------- #
                        #  如果在最后一 列, 特殊处理
                        elif j == col:
                            rx1, rx2, ry1, ry2 = i*(split_withd-2*r)+r,(i+1)*(split_withd-2*r)+r, w-col_over,w
                            px1, px2, py1, py2 = r,split_withd-r,  split_withd-col_over,split_withd

                        # ------------------------------------------------- #
                        #  其余剩下的
                        else:
                            rx1, rx2, ry1, ry2 = i*(split_withd-2*r)+r,(i+1)*(split_withd-2*r)+r, j*(split_withd-2*r)+r,(j+1)*(split_withd-2*r)+r
                            px1, px2, py1, py2 = r, split_withd-r, r, split_withd-r
                    pre_results[rx1:rx2, ry1:ry2] = pre_result[px1:px2, py1:py2]
                    post_results[rx1:rx2, ry1:ry2] = post_result[px1:px2, py1:py2]

        # pre_results = Image.fromarray(np.array(pre_results).astype(np.uint8))
        # pre_results.save(os.path.join(config.predictions_save_path, f"test_localization_{id}_prediction.png"))

        # Image.fromarray(np.array(pre_label).astype(np.uint8)).save(os.path.join(config.targets_save_path, f"test_localization_{id}_target.png")) 
        # Image.fromarray(post_label.astype(np.uint8)).save(os.path.join(config.targets_save_path, f"test_damage_{id}_target.png"))       
        # Image.fromarray(post_results.astype(np.uint8)).save(os.path.join(config.predictions_save_path, f"test_damage_{id}_prediction.png"))

        # post_label[post_label>4] = 1 # 未分类
        # post_label  = pre_to_result(post_label, config).astype(np.uint8)
        post_rgb_results = pre_to_result(post_results, config).astype(np.uint8)
        Image.fromarray((np.array(pre_results)*255).astype(np.uint8)).save(os.path.join(config.pre_result_save_path, name))
        # Image.fromarray((np.array(pre_label)*255).astype(np.uint8)).save(os.path.join(config.pre_label_save_path, name)) 
        Image.fromarray(post_rgb_results.astype(np.uint8)).save(os.path.join(config.post_rgb_save_path, name.replace("_pre_disaster.tif", "_building_damage.png")))       
        Image.fromarray(post_results.astype(np.uint8)).save(os.path.join(config.post_result_save_path, name.replace("_pre_disaster.tif", "_building_damage.png")))        
        # save_to_tif(np.array(pre_results).astype(np.float32), save_path=os.path.join(config.pre_result_save_path, name.replace("pre", "pre")), test_image_path=pre_image_path)
        # save_to_tif(np.array(post_results).astype(np.float32), save_path=os.path.join(config.post_result_save_path, name.replace("pre", "post")), test_image_path=pre_image_path)


def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument
    # arg("-c", "--config_path", default="./config/Country.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/Pakistan2.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/Xizang.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/DF2025_SAM2_DF.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/DF_test_SAM.py", type=Path, help="Path to the config.", required=False)
    arg("--config_path", default="./config/ChangeMamba.py", type=Path, help="Path to the config.", required=False)
    parser.add_argument("--cfg", type=str, default='./changedetection/configs/vssm1/vssm_base_224.yaml', required=False)
    return parser.parse_args()

if __name__ == "__main__":
    args = get_args()
    config = py2cfg(args.config_path)
    test(config)

