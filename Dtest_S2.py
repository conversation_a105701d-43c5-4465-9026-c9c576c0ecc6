# -*- coding: utf-8 -*-
# @Author: Your name
# @Date:   2024-01-10 12:57:50
# @Last Modified by:   Your name
# @Last Modified time: 2024-06-09 20:32:09
import argparse
import os
from pathlib import Path
import torch
import torch.nn as nn
from torchvision import transforms
import torch.nn.functional as F
from tqdm import tqdm
import torch.multiprocessing
import numpy as np
from PIL import Image
from matplotlib import pyplot as plt
from utils.cfg import py2cfg
from glob import glob
from osgeo import gdal, osr
from scipy.ndimage import zoom
from datetime import datetime
torch.multiprocessing.set_sharing_strategy('file_system')
import torchvision.transforms as T

def classId2rgb(out, config):
    out = np.array(out).astype(np.float32)
    result = np.zeros((out.shape[0], out.shape[1], 3)).astype(np.int8)
    for i in range(len(config.label_keys)):
        result[out==i, :] = config.label_keys[i]
    result = Image.fromarray(result.astype(np.uint8))
    return result

def save_to_tif(pred_result, save_path, test_image_path):
    """
    pred_result: 需要保存的图像
    save_path: 保存路径
    test_image_path: 空间参考系
    """
    img = pred_result
    im_geotrans, im_prof = [], []
    tif = gdal.Open(os.path.join(test_image_path))
    im_geotrans.append(tif.GetGeoTransform())
    im_prof.append(tif.GetProjection())
    if 'uint8' in img.dtype.name:
        datatype = gdal.GDT_Byte
    elif 'uint16' in img.dtype.name:
        datatype = gdal.GDT_UInt16
    else:
        datatype = gdal.GDT_Float32
    # print(img.dtype.name)
    #判读数组维数
    if len(img.shape) == 3:
        im_bands, im_height, im_width = img.shape
    else:
        im_bands, (im_height, im_width) = 1, img.shape

    #创建文件
    driver  = gdal.GetDriverByName("GTiff")                #数据类型必须有，因为要计算需要多大内存空间
    dataset = driver.Create(save_path, im_width, im_height, im_bands, datatype)
    dataset.SetGeoTransform(im_geotrans[-1])              #写入仿射变换参数
    dataset.SetProjection(im_prof[-1])                    #写入投影
    if im_bands == 1:
        dataset.GetRasterBand(1).WriteArray(img)  #写入数组数据
    else:
        for i in range(im_bands):
            dataset.GetRasterBand(i+1).WriteArray(img[i])
    del dataset
    # print('保存成功')

def get_tenser_mean(img_tensor, val_max=None, val_min=None):
    img_tensor = F.interpolate(img_tensor, size=(384, 384), mode="bilinear")
    img_tensor = torch.mean(img_tensor, dim=1).cpu().detach().numpy().squeeze()
    if val_max==None:
        val_max, val_min = np.max(img_tensor), np.min(img_tensor)
    img_tensor = ((((img_tensor - val_min)/(val_max-val_min)))*255).astype(np.uint8)
    return img_tensor

def plot_img(datalists, save_path, name, f1=None):
    # datalists = {"pre_high": {}, "post_high": {}, "pre_s2": {}, "post_s2": {},}

    pre_high_datalists = datalists["pre_high"]
    post_high_datalists = datalists["post_high"]
    pre_s2_datalists = datalists["pre_s2"]
    post_s2_datalists = datalists["post_s2"]
    

    draw_datalists = [pre_high_datalists, pre_s2_datalists, post_s2_datalists] #  , post_high_datalists , post_s2_datalists
    # print(f1)
    f1 = [f1[0], f1[2], f1[3]]
    col = len(pre_high_datalists)
    row = len(draw_datalists)
    keys_list = list(datalists.keys())
    fig = plt.figure(figsize=(col*2, row*2)) # (w, h)
    for id, datalists in enumerate(draw_datalists): 
        # 针对灾前画图
        keys_list = list(datalists.keys())
        for keys_id in range(len(keys_list)):
            plt.subplot(row, col, id*col + keys_id+1)
            plt.axis('off')
            if datalists[keys_list[keys_id]] is None:
                continue
            if keys_list[keys_id]=="gray":
                plt.imshow(datalists[keys_list[keys_id]], cmap = plt.cm.gray) # , cmap = plt.cm.jet
            else:
                plt.imshow(datalists[keys_list[keys_id]], cmap = plt.cm.jet) # 

            if keys_list[keys_id]=="out":
                plt.title(f"{keys_list[keys_id]}:{f1[id]}")
            else:
                plt.title(keys_list[keys_id])

    plt.savefig(os.path.join(save_path, name))
    plt.cla()
    plt.close("all")

def load_net(model, device, model_path):

    print(model_path)
    net = model.to(device)
    net = torch.nn.DataParallel(net)
    if model_path is not None:
        # print(torch.load(model_path, map_location=device))
        # print("=====================================")
        net.load_state_dict(torch.load(model_path, map_location=device), True)
    net.eval()

    return net


def load_s2_tif(s2_time_series_path: str):
    # print(s2_time_series_path)
    image_paths = glob(os.path.join(s2_time_series_path + "_S2_**.tif"))
    image_paths.sort()
    # print(s2_time_series_path, len(image_paths), image_paths)
    # x = input()
    image_paths = image_paths[-16:]
    # if "S2_post" in s2_time_series_path:
    #     image_paths = image_paths[0:1]
    if "S2_post/palu" in s2_time_series_path:
        image_paths = image_paths[1:]

    s2_time_series_images = []

    for image_path in image_paths:
        dataset = gdal.Open(image_path)
        image_array = dataset.ReadAsArray()

        x_size = dataset.RasterXSize
        y_size = dataset.RasterYSize 

        s2_time_series_images.append(image_array)
    s2_time_series_images = np.array(s2_time_series_images).reshape((len(image_paths), -1, y_size, x_size))
    s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)
    return s2_time_series_images


def load_high_image_tif(image_path):
    dataset = gdal.Open(image_path)
    high_image = dataset.ReadAsArray()
    return np.array(high_image)

def sentinel22rgb(data):
    # for i in range(3):
    #     data[i, :, :] = (data[i, :, :] - np.nanmin(data[i, :, :])) / (np.nanmax(data[i, :, :]) - np.nanmin(data[i, :, :])) * 255

    # data = data/10000.
    # data = np.clip(data, 0, 0.3)*

    # data = np.nan_to_num(data, nan=0.0)
    # data = zoom(data, (1, 8, 8), order=3)
    # data = np.where(data<0, 0, data)
    # data = np.where(data>255, 0, data)

    # 归一化到 0~1，再截断在 [0, 0.3] 范围内
    data = data / 10000.0
    data = np.clip(data, 0, 0.3)
    data = (data / 0.3) * 255.0
    data = np.nan_to_num(data, nan=0.0)
    data = np.clip(data, 0, 255)
    data = zoom(data, (1, 8, 8), order=3)
    return data.astype(np.uint8)



def load_data(id, config, name, datalists):
    pre_high_image = load_high_image_tif(config.pre_high_image_paths[id])
    # post_high_image = load_high_image_tif(config.post_high_image_paths[id])
    post_high_image = pre_high_image

    pre_s2_image = load_s2_tif(config.pre_s2_image_paths[id])
    post_s2_image = load_s2_tif(config.post_s2_image_paths[id])

    pre_label = np.array(Image.open(config.pre_label_paths[id]))
    post_label = np.array(Image.open(config.post_label_paths[id]))
    if "santa-" in name or "palu-" in name or "gaza-" in name:
        pre_label = np.where((pre_label >= 1) & (pre_label <= 5), 1, 0)
        post_label = np.where(post_label>=5, 0, post_label)
    elif "palisades-" in name or "eaton-" in name:
        pre_label = np.where(pre_label==1, 1, 0)
        post_label = np.where(post_label==1, 1, 0)
    elif "hawaii-" in name or "antakya-" in name or "kahramanmara-" in name:
        pre_label = np.where(pre_label>=1, 1, 0)

    # =================================================================== #
    # 保存输入数据到文件夹
    pre_s2_rgb = sentinel22rgb(pre_s2_image[-1, [3, 2, 1], :, :])
    post_s2_rgb = sentinel22rgb(post_s2_image[-1, [3, 2, 1], :, :])

    datalists = {"pre_high": {}, "post_high": {}, "pre_s2": {}, "post_s2": {},}

    datalists["pre_high"]["image"] = Image.fromarray((np.transpose(np.array(pre_high_image), (1, 2, 0))).astype(np.uint8))
    datalists["post_high"]["image"] = Image.fromarray((np.transpose(np.array(post_high_image), (1, 2, 0))).astype(np.uint8))
    datalists["pre_s2"]["image"] =  Image.fromarray((np.transpose(np.array(pre_s2_rgb), (1, 2, 0))).astype(np.uint8))
    datalists["post_s2"]["image"] =  Image.fromarray((np.transpose(np.array(post_s2_rgb), (1, 2, 0))).astype(np.uint8))

    datalists["pre_high"]["label"] = classId2rgb(pre_label, config) 
    datalists["post_high"]["label"] = classId2rgb(post_label, config) 
    datalists["pre_s2"]["label"] = classId2rgb(pre_label, config)
    datalists["post_s2"]["label"] = classId2rgb(post_label, config)

    # 需要进一步处理灾后数据，以计算F1分数
    if "santa-" in name or "palu-" in name:
        pre_label = np.where((pre_label >= 1) & (pre_label <= 5), 1, 0)
        post_label = np.where(post_label>=5, 0, post_label)
        post_label = np.where(post_label>=3, 1, 0).astype(np.int8) # 临时放置
    elif "palisades-" in name or "eaton-" in name:
        pre_label = np.where(pre_label==1, 1, 0)
        post_label = np.where(post_label==1, 1, 0)
    elif "hawaii-" in name or "antakya-" in name or "kahramanmara-" in name:
        pre_label = np.where(pre_label>=1, 1, 0)
        post_label = np.where(post_label>=2, 1, 0)
    elif "gaza_" in name:
        print(True)
        pre_label = np.where((pre_label >= 1) & (pre_label <= 5), 1, 0)
        post_label = np.where(post_label>=2, 1, 0).astype(np.int8) # 临时放置
        datalists["pre_high"]["label"] = classId2rgb(pre_label, config) 
        datalists["pre_s2"]["label"] = classId2rgb(pre_label, config)

    pre_high_image = torch.from_numpy(np.array(pre_high_image/255., dtype=np.float32)).unsqueeze(0).to(torch.float32)
    post_high_image = torch.from_numpy(np.array(post_high_image/255., dtype=np.float32)).unsqueeze(0).to(torch.float32)
    pre_s2_image = torch.from_numpy(np.array(pre_s2_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    post_s2_image = torch.from_numpy(np.array(post_s2_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    pre_label = torch.from_numpy(np.array(pre_label, dtype=np.int8)).unsqueeze(0).to(torch.long)
    post_label = torch.from_numpy(np.array(post_label, dtype=np.int8)).unsqueeze(0).to(torch.long)

    pre_s2_image = torch.where(torch.isnan(pre_s2_image), torch.tensor(0.), pre_s2_image)
    pre_s2_image = (pre_s2_image - config.s2_mean[None, :, None, None]) / config.s2_std[None, :, None, None]

    post_s2_image = torch.where(torch.isnan(post_s2_image), torch.tensor(0.), post_s2_image)
    post_s2_image = (post_s2_image - config.s2_mean[None, :, None, None]) / config.s2_std[None, :, None, None]


    return [pre_high_image, post_high_image, pre_s2_image, post_s2_image, pre_label, post_label], datalists

def cal_f1socre(label, result):
    result = np.where(result>=0.5, 1, 0)
    c = 1
    TP = np.logical_and(result == c, label == c).sum()
    FN = np.logical_and(result != c, label == c).sum()
    FP = np.logical_and(result == c, label != c).sum()
    TN = np.logical_and(result != c, label != c).sum()
    Recall = TP / (TP + FN)
    Precision = TP / (TP + FP)
    F1 = 2 * Recall * Precision / (Precision + Recall)
    return round(F1*100., 2)



def plot_high_geature(draw_datalists, save_path):
    col = 4
    row = 3
    fig = plt.figure(figsize=(col*2, row*2))
    for id, datalists in enumerate(draw_datalists): 
        plt.subplot(row, col, id+1)
        plt.axis('off')
        # print(datalists[id].shape)
        plt.imshow(datalists, cmap = plt.cm.jet)
    plt.savefig(save_path)
    plt.cla()
    plt.close("all")

def prosess_output(id, output, config, datalists, name, pre_label, post_label):
    def output2numpy(building, features):
        building = torch.argmax(building, dim=1).cpu().detach().numpy().squeeze()
        # building = np.array(torch.softmax(building, dim=1)[:, 1].cpu().detach().numpy().squeeze()).astype(np.float32)
        # building = np.array(torch.sigmoid(building)[:, -1].cpu().detach().numpy().squeeze()).astype(np.float32)
        features = features
        return building, features
    
    pre_high_building, pre_high_features = output2numpy(output["pre_high_building"], output["pre_high_features"])
    pre_low_building, pre_low_features = output2numpy(output["pre_low_building"], output["pre_low_features"])
    post_low_damage, post_low_features = output2numpy(output["post_low_damage"], output["post_low_features"])

    datalists["pre_high"]["out"] = pre_high_building#*255
    datalists["pre_s2"]["out"] = pre_low_building#*255
    datalists["post_s2"]["out"] = post_low_damage#*255

    datalists["pre_high"]["feature"] = get_tenser_mean(pre_high_features)
    datalists["pre_s2"]["feature"] = get_tenser_mean(pre_low_features)
    datalists["post_s2"]["feature"] = get_tenser_mean(post_low_features)


    pre_high_f1 = cal_f1socre(pre_label.detach().numpy().squeeze(), pre_high_building)
    pre_s2_f1 = cal_f1socre(pre_label.detach().numpy().squeeze(), pre_low_building)
    post_s2_f1 = cal_f1socre(post_label.detach().numpy().squeeze(), post_low_damage)

    # 保存结果至文件夹
    plot_img(datalists, config.plot_save_path, name, f1=[pre_high_f1, pre_high_f1, pre_s2_f1, post_s2_f1])
    # save_to_tif(pre_high_building, save_path=os.path.join(config.pre_high_result_save_path, name), test_image_path=config.pre_high_image_paths[id])
    # save_to_tif(pre_low_building, save_path=os.path.join(config.pre_s2_result_save_path, name), test_image_path=config.pre_high_image_paths[id])
    save_to_tif(post_low_damage, save_path=os.path.join(config.post_s2_result_save_path, name.replace("_pre_", "_post_")), test_image_path=config.pre_high_image_paths[id])


def test(config):
    print(f"test datasets size: {len(config.pre_high_image_paths)}")
    device = torch.device(config.device)
    net = load_net(config.model, device, config.model_path)
    
    for id, image_high_path in tqdm(enumerate(config.pre_high_image_paths)):
        datalists = {"pre_high": {}, "post_high": {}, "pre_s2": {}, "post_s2": {},}
        name = os.path.basename(image_high_path).replace(".png", ".tif")
        [pre_high_image, post_high_image, pre_s2_image, post_s2_image, pre_label, post_label], datalists = load_data(id, config, name, datalists)
        with torch.no_grad():
            output = net([pre_high_image, pre_s2_image, post_s2_image, pre_label, post_label], device, config.osm)
        prosess_output(id, output, config, datalists, name, pre_label, post_label)


def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument

    # arg("-c", "--config_path", default="./config/gaza_s2_uabcd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/palu_s2_uabcd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/santa_s2_uabcd.py", type=Path, help="Path to the config.", required=False)

    # arg("-c", "--config_path", default="./config/santa_s2_hrsicd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/gaza_s2_hrsicd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/palu_s2_hrsicd.py", type=Path, help="Path to the config.", required=False)

    arg("-c", "--config_path", default="./config/gaza_s2_LBDv16.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/palu_s2_LBDv16.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/santa_s2_LBDv16.py", type=Path, help="Path to the config.", required=False)

    return parser.parse_args()

if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '3'
    args = get_args()
    config = py2cfg(args.config_path)
    test(config)

