import os
import re
import cv2
import imageio.v2 as imageio
import numpy as np
import random
from PIL import Image, ImageDraw
from matplotlib import pyplot as plt
from datetime import datetime, timedelta
import albumentations as albu

import torch
from torch.utils.data import Dataset
from torch.utils.data import DataLoader
import torch.nn.functional as F
import torchvision.transforms as transforms
from glob import glob
from osgeo import gdal, osr
from pyproj import Proj, Transformer
from functools import partial

class Santa_S12(Dataset):
    def __init__(self, mode):
        self.mode = mode
        file = {"train": "train", "test": "test", "hold": "hold"}
        # pre_label_paths = glob(os.path.join("/mnt/d2/zxq/BuildingDamage/dataset/xBDS2", file[self.mode], "labels", "*santa*pre**.tif"))
        pre_label_paths = glob(os.path.join("/mnt/d2/zxq/BDS12/dataset/santa", file[self.mode], "labels", "*santa*pre**.tif"))
        # if self.mode == "train":
            # pre_label_paths+= glob(os.path.join("./dataset/santa", "test", "labels", "*santa*pre**.tif"))
            # pre_label_paths+= glob(os.path.join("./dataset/santa", "hold", "labels", "*santa*pre**.tif"))
            # pre_label_paths+= glob(os.path.join("./dataset/palisades", "train", "labels", "*palisades*pre**.tif"))
            # pre_label_paths+= glob(os.path.join("./dataset/eaton", "train", "labels", "*eaton*pre**.tif"))

        # if self.mode == "test":
        #     pre_label_paths += glob(os.path.join("./dataset/santa", "test", "labels", "*santa*pre**.tif"))
        #     pre_label_paths += glob(os.path.join("./dataset/palisades", "test", "labels", "*palisades*pre**.tif"))
        #     pre_label_paths+= glob(os.path.join("./dataset/eaton", "test", "labels", "*eaton*pre**.tif"))

        if self.mode == "test":
            pre_label_paths += glob(os.path.join("./dataset/santa", "hold", "labels", "*santa*pre**.tif"))
        #     pre_label_paths = glob(os.path.join("./dataset/eaton", "test", "labels", "*eaton*pre**.tif"))
        #     pre_label_paths+= glob(os.path.join("./dataset/eaton", "train", "labels", "*eaton*pre**.tif"))

        # if self.mode == "test":
        #     pre_label_paths = glob(os.path.join("./dataset/palu", "test", "labels", "*palu*pre**.tif"))
        #     pre_label_paths+= glob(os.path.join("./dataset/palu", "hold", "labels", "*palu*pre**.tif"))
        #     pre_label_paths+= glob(os.path.join("./dataset/palu", "train", "labels", "*palu*pre**.tif"))

        #     for item, pre_label_path in enumerate(pre_label_paths):
        #         image_name = pre_label_path.split(".tif")[0]
        #         palu_cloud_names = [f"palu-tsunami_{str(id).zfill(8)}_pre" for id in [3, 4, 6, 7, 8, 12, 16, 18, 21, 32, 41, 96, 117, 120, 143, 192, 194]]
        #         # print(palu_cloud_names)
        #         if image_name.split("/labels/")[1].split("_disaster")[0] in palu_cloud_names:
        #             # print(image_name, image_name.split("/labels/")[1].split("_disaster")[0])
        #             pre_label_paths.remove(pre_label_path)
        #             continue

        # self.s2_mean_std = np.loadtxt("./dataset/santa/santa_s2_mean_std2.txt")
        self.s2_mean_std = np.loadtxt("./dataset/santa/santa_s2_mean_std3.txt")
        # self.s2_mean_std = np.loadtxt("./dataset/santa_paliasades_eaton_s2_mean_std.txt")
        self.s2_mean, self.s2_std = self.s2_mean_std[0,:], self.s2_mean_std[1,:]

        self.pre_high_image_paths = []
        self.post_high_image_paths = []
        self.pre_s2_image_paths = []
        self.post_s2_image_paths = []
        self.pre_s1_image_paths = []
        self.post_s1_image_paths = []
        self.pre_label_paths = []
        self.post_label_paths = []

        for item, pre_label_path in enumerate(pre_label_paths):
            image_name = pre_label_path.split(".tif")[0]
            self.pre_high_image_paths.append(image_name.replace("labels", "images"))
            self.post_high_image_paths.append(image_name.replace("labels", "images").replace("pre", "post"))
            self.pre_s2_image_paths.append(image_name.replace("pre", "pre").replace("labels", "S2"))
            self.post_s2_image_paths.append(image_name.replace("pre", "post").replace("labels", "S2_post"))
            self.pre_s1_image_paths.append(image_name.replace("pre", "pre").replace("labels", "S1"))
            self.post_s1_image_paths.append(image_name.replace("pre", "post").replace("labels", "S1_post"))
            self.pre_label_paths.append(pre_label_path)
            self.post_label_paths.append(pre_label_path.replace("pre", "post"))

        self.pre_high_image_paths = np.array(self.pre_high_image_paths)
        self.post_high_image_paths = np.array(self.post_high_image_paths)
        self.pre_s2_image_paths = np.array(self.pre_s2_image_paths)
        self.post_s2_image_paths = np.array(self.post_s2_image_paths)
        self.pre_label_paths = np.array(self.pre_label_paths)
        self.post_label_paths = np.array(self.post_label_paths)
        self.pre_s1_image_paths = np.array(self.pre_s1_image_paths)
        self.post_s1_image_paths = np.array(self.post_s1_image_paths)

    def __len__(self):
        return len(self.pre_label_paths)        

    def _load_high_image(self, image_path):
        # print(image_path)
        dataset = gdal.Open(os.path.join(image_path + ".tif"))
        high_image = dataset.ReadAsArray()
        high_image = torch.from_numpy(np.array(high_image).astype(dtype=np.float32)/255.)
        return high_image


    def _load_S2_image(self, image_path):
        s2_image_paths = glob(os.path.join(image_path +  "_S2**.tif"))
        s2_image_paths.sort()
        if "S2_post" in image_path:
            s2_image_paths = s2_image_paths[-1:]
        # print(s2_image_paths, image_path)

        s2_time_series_images = []
        for s2_image_path in s2_image_paths:
            dataset = gdal.Open(s2_image_path)
            image_array = dataset.ReadAsArray()
            x_size = dataset.RasterXSize
            y_size = dataset.RasterYSize
            s2_time_series_images.append(image_array)
        s2_time_series_images = np.array(s2_time_series_images).reshape((len(s2_image_paths), -1, y_size, x_size))
        s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)

        # 数据归一化
        s2_time_series_images = torch.from_numpy(np.array(s2_time_series_images, dtype=np.float32))
        s2_time_series_images = torch.where(torch.isnan(s2_time_series_images), torch.tensor(0.), s2_time_series_images)
        s2_time_series_images = (s2_time_series_images - self.s2_mean[None, :, None, None]) / self.s2_std[None, :, None, None]
        return s2_time_series_images

    def _load_S1_image(self, image_path):
        s1_image_paths = glob(os.path.join(image_path +  "_S1**.tif"))
        s1_image_paths.sort()
        # print(image_path, s1_image_paths)

        s1_time_series_images = []
        for s1_image_path in s1_image_paths:
            dataset = gdal.Open(s1_image_path)
            image_array = dataset.ReadAsArray()
            x_size = dataset.RasterXSize
            y_size = dataset.RasterYSize
            s1_time_series_images.append(image_array)
        s1_time_series_images = np.array(s1_time_series_images).reshape((len(s1_image_paths), -1, y_size, x_size))

        # 数据归一化
        s1_time_series_images = torch.from_numpy(np.array(s1_time_series_images, dtype=np.float32))
        s1_time_series_images = s1_time_series_images[:, :2] # 仅需要 vv 和 vh 的后向散射系数，其余不需要
        return s1_time_series_images

    def _load_S1_C2_image(self, image_path, mode="S1_C2"):
        image_paths = glob(os.path.join(image_path.replace("/S1/", f"/{mode}/") +  f"**{mode}**.tif"))#
        image_paths.sort()
        # print(image_path, image_paths, os.path.join(image_path.replace("/S1/", mode) +  f"**{mode}**.tif"))

        time_series_images = []
        for image_path in image_paths:
            dataset = gdal.Open(image_path)
            image_array = dataset.ReadAsArray()
            # if "20171103" in image_path and mode=="S1_CohIfg":
                # print(image_array.shape)
                # time_series_images.append(image_array[1::2])
            # else:
            x_size = dataset.RasterXSize
            y_size = dataset.RasterYSize
            time_series_images.append(image_array)
        time_series_images = np.array(time_series_images).reshape((len(image_paths), -1, y_size, x_size))

        time_series_images = torch.from_numpy(np.array(time_series_images, dtype=np.float32))
        time_series_images = torch.where(time_series_images<=-25, -25, time_series_images)
        time_series_images = torch.where(time_series_images>= 25,  25, time_series_images)
        if mode=="S1_CohIfg":
            T, C, H, W = time_series_images.shape
            for t_id in range(T):
                time_series_images[t_id] = torch.where(time_series_images[t_id]>time_series_images[-2], time_series_images[-2], time_series_images[t_id])
        return time_series_images

    def _load_label(self, label_path):
        label = gdal.Open(label_path)
        label = label.ReadAsArray()
        label = torch.from_numpy(np.array(label, dtype=np.uint8))
        label = torch.where(torch.isnan(label), torch.tensor(0.), label)
        return label
    
    def _data_augmentation(self, pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label):
        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[2])
            pre_s2_images = pre_s2_images.flip(dims=[3])
            post_s2_images = post_s2_images.flip(dims=[3])
            pre_label = pre_label.flip(dims=[1])
            post_label = post_label.flip(dims=[1])

        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[1])
            pre_s2_images = pre_s2_images.flip(dims=[2])
            post_s2_images = post_s2_images.flip(dims=[2])
            pre_label = pre_label.flip(dims=[0])
            post_label = post_label.flip(dims=[0])

        return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label

    def _pad_tensor(self, tensor, target_h, target_w):
        """将输入 tensor 填充到 (target_h, target_w)，保持中心对齐"""
        h, w = tensor.shape[-2:]
        pad_h = target_h - h
        pad_w = target_w - w
        padding = (pad_w // 2, pad_w - pad_w // 2, pad_h // 2, pad_h - pad_h // 2)
        # return F.pad(tensor, padding, mode='constant', value=0)
    
        if tensor.dim() == 4:
            return F.pad(tensor, padding, mode='constant', value=0)
        elif tensor.dim() == 3:
            return F.pad(tensor.unsqueeze(0), padding, mode='constant', value=0).squeeze(0)
        elif tensor.dim() == 2:
            return F.pad(tensor.unsqueeze(0).unsqueeze(0), padding, mode='constant', value=0).squeeze(0).squeeze(0)

    def _save_tensor(self, tensor, original_path):
        tensor_path = original_path.replace("/train/", "/train_pt/")
        tensor_path = tensor_path.replace("/test/", "/test_pt/")
        tensor_path = tensor_path.replace("/hold/", "/hold_pt/")
        tensor_path = tensor_path.split(".tif")[0]
        # print(tensor_path)
        os.makedirs(os.path.dirname(tensor_path), exist_ok=True)
        torch.save(tensor, f"{tensor_path}.pth")
    
    def __getitem__(self, item):
        pre_high_image = self._load_high_image(self.pre_high_image_paths[item])
        # post_high_image = self._load_high_image(self.post_high_image_paths[item])

        # pre_s1_images = self._load_S1_image(self.pre_s1_image_paths[item])
        # post_s1_images = self._load_S1_image(self.post_s1_image_paths[item])

        # s1_c2_images = self._load_S1_C2_image(self.pre_s1_image_paths[item])
        # pre_s1_images = torch.cat((pre_s1_images[-1:], s1_c2_images[-2:-1]), dim=1)
        # post_s1_images = torch.cat((post_s1_images[-1:], s1_c2_images[-1:]), dim=1)

        # S1_CohIfg_images = self._load_S1_C2_image(self.pre_s1_image_paths[item], mode="S1_CohIfg")
        # pre_s1_images = torch.cat((pre_s1_images, S1_CohIfg_images[-2:-1]), dim=1)
        # post_s1_images = torch.cat((post_s1_images, S1_CohIfg_images[-1:]), dim=1)

        pre_s2_images = self._load_S2_image(self.pre_s2_image_paths[item])
        post_s2_images = self._load_S2_image(self.post_s2_image_paths[item])

        pre_label = self._load_label(self.pre_label_paths[item])
        post_label = self._load_label(self.post_label_paths[item])
        if "santa" in self.pre_high_image_paths[item] or "palu" in self.pre_high_image_paths[item]:
            pre_label = torch.where(pre_label>=0.5, 1, 0)
            post_label = torch.where(post_label>=5, 1, post_label)
            post_label = torch.where(post_label>=3, 1, 0)
        elif "palisades" in self.pre_high_image_paths[item] or "eaton" in self.pre_high_image_paths[item]:
            pre_label = torch.where(pre_label==1, 1, 0)
            post_label = torch.where(post_label==1, 1, 0)

        if self.mode == "train":
            pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label = self._data_augmentation(pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label)
        # self._save_tensor(pre_high_image, self.pre_high_image_paths[item])
        # self._save_tensor(post_high_image, self.post_high_image_paths[item])
        # self._save_tensor(pre_s1_images, self.pre_s1_image_paths[item])
        # self._save_tensor(post_s1_images, self.post_s1_image_paths[item])
        # self._save_tensor(pre_s2_images, self.pre_s2_image_paths[item])
        # self._save_tensor(post_s2_images, self.post_s2_image_paths[item])
        # self._save_tensor(pre_label, self.pre_label_paths[item])
        # self._save_tensor(post_label, self.post_label_paths[item])

        return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label

def dataset_collate(batch):
    pre_high_image = torch.stack([item[0] for item in batch])
    pre_s2_images = torch.stack([item[1] for item in batch])
    post_s2_images = torch.stack([item[2] for item in batch])
    pre_label = torch.stack([item[3] for item in batch])
    post_label = torch.stack([item[4] for item in batch])
    return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label

class Santa_S12_tensor(Dataset):
    def __init__(self, mode):
        self.mode = mode
        file = {"train": "train_pt", "test": "test_pt"}
        pre_label_paths = glob(os.path.join("/mnt/d2/zxq/BDS12/dataset/santa", file[self.mode], "labels", "*santa*pre**.pth"))
        if self.mode == "test":
            pre_label_paths += glob(os.path.join("./dataset/santa", "hold_pt", "labels", "*santa*pre**.pth"))
        self.s2_mean_std = np.loadtxt("./dataset/santa/santa_s2_mean_std.txt")
        self.s2_mean, self.s2_std = self.s2_mean_std[0,:], self.s2_mean_std[1,:]

        self.pre_label_paths = []

        for item, pre_label_path in enumerate(pre_label_paths):
            self.pre_label_paths.append(pre_label_path)

        self.pre_label_paths = np.array(self.pre_label_paths)


    def __len__(self):
        return len(self.pre_label_paths)        

    def _load_tensor(self, tensor_path):
        tensor = torch.load(tensor_path)
        return tensor


    def _data_augmentation(self, pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label):
        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[2])
            pre_s2_images = pre_s2_images.flip(dims=[3])
            post_s2_images = post_s2_images.flip(dims=[3])
            pre_label = pre_label.flip(dims=[1])
            post_label = post_label.flip(dims=[1])

        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[1])
            pre_s2_images = pre_s2_images.flip(dims=[2])
            post_s2_images = post_s2_images.flip(dims=[2])
            pre_label = pre_label.flip(dims=[0])
            post_label = post_label.flip(dims=[0])

        return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label


    def __getitem__(self, item):
        pre_high_image = self._load_tensor(self.pre_label_paths[item].replace("labels", "images"))
        # post_high_image = self._load_tensor(self.pre_label_paths[item].replace("labels", "images").replace("pre", "post"))

        # pre_s1_images = self._load_tensor(self.pre_label_paths[item].replace("labels", "S1"))
        # post_s1_images = self._load_tensor(self.pre_label_paths[item].replace("labels", "S1_post").replace("pre", "post"))

        pre_s2_images = self._load_tensor(self.pre_label_paths[item].replace("labels", "S2"))
        post_s2_images = self._load_tensor(self.pre_label_paths[item].replace("labels", "S2_post").replace("pre", "post"))

        # pre_s2_images = torch.where(torch.isnan(pre_s2_images), torch.tensor(0.), pre_s2_images)
        # pre_s2_images = (pre_s2_images - self.s2_mean[None, :, None, None]) / self.s2_std[None, :, None, None]

        # post_s2_images = torch.where(torch.isnan(post_s2_images), torch.tensor(0.), post_s2_images)
        # post_s2_images = (post_s2_images - self.s2_mean[None, :, None, None]) / self.s2_std[None, :, None, None]


        pre_label = self._load_tensor(self.pre_label_paths[item])
        post_label = self._load_tensor(self.pre_label_paths[item].replace("pre", "post"))

        # pre_label = torch.where(pre_label>=0.5, 1, 0)
        # post_label = torch.where(post_label>=5, 1, post_label)
        # post_label = torch.where(post_label>=3, 1, 0)

        if self.mode == "train":
            pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label = self._data_augmentation(pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label)
        return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label


class Santa_S1_tensor(Dataset):
    def __init__(self, mode):
        self.mode = mode
        file = {"train": "train_pt", "test": "test_pt"}
        pre_label_paths = glob(os.path.join("/mnt/d2/zxq/BDS12/dataset/santa", file[self.mode], "labels", "*santa*pre**.pth"))
        if self.mode == "test":
            pre_label_paths += glob(os.path.join("./dataset/santa", "hold_pt", "labels", "*santa*pre**.pth"))
        self.s2_mean_std = np.loadtxt("./dataset/santa/santa_s2_mean_std.txt")
        self.s2_mean, self.s2_std = self.s2_mean_std[0,:], self.s2_mean_std[1,:]

        self.pre_label_paths = []

        for item, pre_label_path in enumerate(pre_label_paths):
            self.pre_label_paths.append(pre_label_path)

        self.pre_label_paths = np.array(self.pre_label_paths)


    def __len__(self):
        return len(self.pre_label_paths)        

    def _load_tensor(self, tensor_path):
        tensor = torch.load(tensor_path)
        return tensor


    def _data_augmentation(self, pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label):
        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[2])
            pre_s2_images = pre_s2_images.flip(dims=[3])
            post_s2_images = post_s2_images.flip(dims=[3])
            pre_label = pre_label.flip(dims=[1])
            post_label = post_label.flip(dims=[1])

        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[1])
            pre_s2_images = pre_s2_images.flip(dims=[2])
            post_s2_images = post_s2_images.flip(dims=[2])
            pre_label = pre_label.flip(dims=[0])
            post_label = post_label.flip(dims=[0])

        return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label


    def __getitem__(self, item):
        pre_high_image = self._load_tensor(self.pre_label_paths[item].replace("labels", "images"))
        # post_high_image = self._load_tensor(self.pre_label_paths[item].replace("labels", "images").replace("pre", "post"))

        pre_s1_images = self._load_tensor(self.pre_label_paths[item].replace("labels", "S1"))
        post_s1_images = self._load_tensor(self.pre_label_paths[item].replace("labels", "S1_post").replace("pre", "post"))
        post_s1_images = torch.cat((pre_s1_images, post_s1_images), dim=1)

        pre_s2_images = self._load_tensor(self.pre_label_paths[item].replace("labels", "S2"))
        # post_s2_images = self._load_tensor(self.pre_label_paths[item].replace("labels", "S2_post").replace("pre", "post"))

        # pre_s2_images = torch.where(torch.isnan(pre_s2_images), torch.tensor(0.), pre_s2_images)
        # pre_s2_images = (pre_s2_images - self.s2_mean[None, :, None, None]) / self.s2_std[None, :, None, None]

        # post_s2_images = torch.where(torch.isnan(post_s2_images), torch.tensor(0.), post_s2_images)
        # post_s2_images = (post_s2_images - self.s2_mean[None, :, None, None]) / self.s2_std[None, :, None, None]


        pre_label = self._load_tensor(self.pre_label_paths[item])
        post_label = self._load_tensor(self.pre_label_paths[item].replace("pre", "post"))

        # pre_label = torch.where(pre_label>=0.5, 1, 0)
        # post_label = torch.where(post_label>=5, 1, post_label)
        # post_label = torch.where(post_label>=3, 1, 0)

        if self.mode == "train":
            pre_high_image, pre_s2_images, post_s1_images, pre_label, post_label = self._data_augmentation(pre_high_image, pre_s2_images, post_s1_images, pre_label, post_label)
        return pre_high_image, pre_s2_images, post_s1_images, pre_label, post_label


if __name__ == "__main__":
    train_dataset = Santa_S1_tensor('test')
    # train_dataset = Santa_S12('test')
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=1, drop_last=False)
    print(len(train_loader.dataset))
    for batch_id, inputs in enumerate(train_loader):
        print(batch_id, inputs[0].shape, inputs[1].shape, inputs[2].shape, inputs[3].shape, inputs[4].shape)
        print(torch.max(inputs[0]), torch.min(inputs[0]), torch.max(inputs[1]), torch.min(inputs[1]))
        print(torch.max(inputs[2]), torch.min(inputs[2]), torch.max(inputs[3]), torch.min(inputs[3]))
        # print(torch.max(inputs[4]), torch.min(inputs[4]), torch.max(inputs[5]), torch.min(inputs[5]))
        print(batch_id, torch.max(inputs[-2]), torch.min(inputs[-2]), torch.max(inputs[-1]), torch.min(inputs[-1]))

