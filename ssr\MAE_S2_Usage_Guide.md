# Temporal MAE for Sentinel-2 Building Detection - Usage Guide

## Overview

This implementation provides a complete Temporal Masked Autoencoder (MAE) framework for Sentinel-2 satellite imagery time series, specifically designed for building detection and localization tasks. The framework supports temporal data input [B, T, C, H, W] with T=16 temporal frames, C=13 Sentinel-2 bands, and H=W=22 spatial dimensions.

## Key Features

- **Temporal Support**: Handles time series data [B, T=16, C=13, H=22, W=22]
- **ViT Encoder**: Uses Vision Transformer with patch_size=1 (no downsampling)
- **Temporal Embedding**: Distinguishes different time frames
- **Modular Design**: Separate components for easy modification
- **Domain Adaptation**: Pretraining on Harvey+Santa, fine-tuning on <PERSON>, testing on Santa

## Architecture Components

### 1. Core Temporal MAE Components

- **MAEEncoder_S2_Temporal**: ViT encoder with temporal masking capability
- **MAEDecoder_S2_Temporal**: Lightweight decoder for temporal patch reconstruction
- **PatchEmbed_S2_Temporal**: Converts temporal Sentinel-2 data to patch embeddings
- **MaskGenerator**: Random masking strategy for self-supervised learning

### 2. Main Models

#### MAE_Sentinel2_Temporal
Complete temporal MAE model for reconstruction pretraining on Sentinel-2 time series.

```python
from ssr.SRSRGB_S2 import MAE_Sentinel2_Temporal

# Initialize temporal MAE model for pretraining
mae_model = MAE_Sentinel2_Temporal(
    img_size=22,            # Spatial size (H=W=22)
    patch_size=1,           # Patch size=1 (no downsampling)
    in_chans=13,           # Sentinel-2 channels (13 bands)
    temporal_frames=16,     # Number of temporal frames
    embed_dim=768,         # Encoder embedding dimension
    depth=12,              # Number of encoder layers
    num_heads=12,          # Number of attention heads
    decoder_embed_dim=512, # Decoder embedding dimension
    decoder_depth=8,       # Number of decoder layers
    decoder_num_heads=16,  # Decoder attention heads
    norm_pix_loss=False    # Whether to normalize pixel loss
)

# Input: [B, T=16, C=13, H=22, W=22]
temporal_s2_data = torch.randn(batch_size, 16, 13, 22, 22)

# Forward pass for pretraining
loss, pred, mask = mae_model(temporal_s2_data, mask_ratio=0.75)
```

#### MAE_FineTune_BuildingDetection_Temporal
Fine-tuning model using pretrained temporal MAE encoder for building detection.

```python
from ssr.SRSRGB_S2 import MAE_FineTune_BuildingDetection_Temporal

# Initialize temporal fine-tuning model
finetune_model = MAE_FineTune_BuildingDetection_Temporal(
    img_size=22,
    patch_size=1,
    in_chans=13,
    temporal_frames=16,
    embed_dim=768,
    depth=12,
    num_heads=12,
    num_classes=2,         # Building/Non-building
    global_pool=False
)

# Load pretrained temporal MAE weights
finetune_model.load_pretrained_mae('path/to/temporal_mae_checkpoint.pth')

# Input: [B, T=16, C=13, H=22, W=22]
temporal_s2_data = torch.randn(batch_size, 16, 13, 22, 22)

# Forward pass for segmentation (output: [B, 2, 22, 22])
seg_output = finetune_model(temporal_s2_data, mode='segmentation')

# Forward pass for classification (output: [B, 2])
cls_output = finetune_model(temporal_s2_data, mode='classification')
```

#### MAE_S2_BuildingDetection_Integrated_Temporal
Integrated model combining both temporal pretraining and fine-tuning phases.

```python
from ssr.SRSRGB_S2 import MAE_S2_BuildingDetection_Integrated_Temporal

# Initialize integrated temporal model
integrated_model = MAE_S2_BuildingDetection_Integrated_Temporal(
    img_size=22,
    patch_size=1,
    in_chans=13,
    temporal_frames=16,
    embed_dim=768,
    depth=12,
    num_heads=12,
    num_classes=2
)

# Phase 1: Pretraining on Harvey + Santa temporal data
integrated_model.set_training_mode('pretrain')
harvey_santa_temporal = torch.randn(batch_size, 16, 13, 22, 22)
loss, pred, mask = integrated_model(harvey_santa_temporal, mask_ratio=0.75)

# Transfer weights and switch to fine-tuning
integrated_model.transfer_pretrained_weights()
integrated_model.set_training_mode('finetune')

# Phase 2: Fine-tuning on Harvey temporal data
harvey_temporal = torch.randn(batch_size, 16, 13, 22, 22)
seg_output = integrated_model(harvey_temporal, mode='segmentation')
```

## Training Workflow

### Step 1: Reconstruction Pretraining
Train MAE on combined Harvey and Santa Rosa Sentinel-2 data for self-supervised representation learning.

```python
# Pretraining loop
mae_model.train()
for batch in harvey_santa_dataloader:
    sentinel2_imgs = batch['sentinel2']  # [B, 13, 224, 224]
    
    # Forward pass with random masking
    loss, pred, mask = mae_model(sentinel2_imgs, mask_ratio=0.75)
    
    # Backward pass
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
```

### Step 2: Fine-tuning for Building Detection
Fine-tune the pretrained encoder on Harvey building detection task.

```python
# Fine-tuning loop
finetune_model.train()
for batch in harvey_building_dataloader:
    sentinel2_imgs = batch['sentinel2']  # [B, 13, 224, 224]
    building_masks = batch['building_mask']  # [B, 224, 224]
    
    # Forward pass for segmentation
    pred_masks = finetune_model(sentinel2_imgs, mode='segmentation')
    
    # Compute segmentation loss
    loss = criterion(pred_masks, building_masks)
    
    # Backward pass
    optimizer.zero_grad()
    loss.backward()
    optimizer.step()
```

### Step 3: Generalization Testing
Test the fine-tuned model on Santa Rosa data for generalization evaluation.

```python
# Evaluation on Santa Rosa
finetune_model.eval()
with torch.no_grad():
    for batch in santa_test_dataloader:
        sentinel2_imgs = batch['sentinel2']
        pred_masks = finetune_model(sentinel2_imgs, mode='segmentation')
        # Evaluate generalization performance
```

## Key Features

1. **Modular Design**: Each component is separately implemented for easy modification
2. **Flexible Architecture**: Supports different image sizes, patch sizes, and model dimensions
3. **Self-Supervised Pretraining**: Uses masking strategy for learning robust representations
4. **Multi-Modal Support**: Designed specifically for Sentinel-2's 13-band imagery
5. **Transfer Learning**: Easy weight transfer from pretraining to fine-tuning
6. **Dense Prediction**: Supports both classification and segmentation tasks

## Input Requirements

- **Sentinel-2 Images**: Shape [B, 13, H, W] where H and W are divisible by patch_size
- **Building Masks**: Shape [B, H, W] for segmentation tasks
- **Image Size**: Default 224x224, but configurable
- **Normalization**: Images should be normalized to [0, 1] range

## Output Formats

- **Pretraining**: Reconstruction loss, predicted patches, and mask
- **Segmentation**: Dense prediction maps [B, num_classes, H, W]
- **Classification**: Class logits [B, num_classes]

## Performance Considerations

- **Memory Usage**: Larger embed_dim and depth increase memory requirements
- **Mask Ratio**: Higher ratios (0.75) provide better self-supervised learning
- **Patch Size**: Smaller patches capture finer details but increase computational cost
- **Multi-Scale**: Consider using different scales for better generalization

This framework provides a complete solution for improving building detection generalization through self-supervised pretraining on multi-region Sentinel-2 data.
