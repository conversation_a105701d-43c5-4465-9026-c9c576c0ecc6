import os
import re
import cv2
import imageio.v2 as imageio
import numpy as np
import random
from PIL import Image, ImageDraw
from matplotlib import pyplot as plt
from datetime import datetime, timedelta
import albumentations as albu

import torch
from torch.utils.data import Dataset
from torch.utils.data import DataLoader
import torch.nn.functional as F
import torchvision.transforms as transforms
from glob import glob
from osgeo import gdal, osr
from pyproj import Proj, Transformer
from functools import partial

class Turkey_S2(Dataset):
    def __init__(self, mode):
        self.mode = mode

        file = {"train": "train", "test": "test"}
        pre_image_paths = glob(os.path.join("./dataset/Turkey", file[self.mode]+"2", "images", "**_pre_disaster_**.tif"))

        self.s2_mean_std = np.loadtxt("./dataset/Turkey/turkey_s2_mean_std.txt")
        self.s2_mean, self.s2_std = self.s2_mean_std[0, :], self.s2_mean_std[1, :]

        self.pre_high_image_paths = []
        self.pre_s2_image_paths = []
        self.post_s2_image_paths = []
        self.post_label_paths = []

        for item, pre_image_path in enumerate(pre_image_paths):
            image_name = pre_image_path.split(".tif")[0]
            # lab = Image.open(image_name.replace("images", "labels").replace("_pre_disaster_", "_building_damage_")+".tif")
            # lab = np.array(lab).astype(np.uint8)
            # lab = np.where(lab > 2, 1, 0)
            # if (mode == "train" and np.sum(np.array(lab)) > 0) or  mode == "test":
            # if np.sum(np.array(lab)) > 0:
            # turkey_city_code1 = [f"turkey-earthquake_{str(id).zfill(8)}_post" for id in range(512, 679+1)]
            # turkey_city_code2 = [f"turkey-earthquake_{str(id).zfill(8)}_post" for id in range(181, 351+1)]
            # turkey_city_code3 = [f"turkey-earthquake_{str(id).zfill(8)}_pre" for id in range(352, 511+1)]       
            # turkey_city_code4 = [f"turkey-earthquake_{str(id).zfill(8)}_post" for id in range(608, 858+1)]       
            # turkey_city_code5 = [f"turkey-earthquake_{str(id).zfill(8)}_post" for id in range(859, 983+1)]       
            # turkey_city_code6 = [f"turkey-earthquake_{str(id).zfill(8)}_post" for id in range(0, 180+1)]       
            # turkey_city_code7 = [f"turkey-earthquake_{str(id).zfill(8)}_pre" for id in range(984, 1113+1)]
            # turkey_city_code = turkey_city_code3# + turkey_city_code7
            
            # if os.path.basename(pre_image_path).split("_disaster")[0] not in turkey_city_code:
            #     # print(pre_image_path, os.path.basename(pre_image_path).split("_disaster")[0])
            #     continue
            self.pre_high_image_paths.append(image_name)
            self.post_label_paths.append(image_name.replace("images", "labels").replace("_pre_disaster_", "_building_damage_")+".tif")
            self.pre_s2_image_paths.append(image_name.replace("images", "S2"))
            self.post_s2_image_paths.append(image_name.replace("images", "S2_post_other_time_second"))
                    
        
        self.pre_high_image_paths = np.array(self.pre_high_image_paths)
        self.pre_s2_image_paths = np.array(self.pre_s2_image_paths)
        self.post_s2_image_paths = np.array(self.post_s2_image_paths)
        self.post_label_paths = np.array(self.post_label_paths)


    def __len__(self):
        return len(self.post_label_paths)        

    def _load_high_image(self, image_path):
        high_image = Image.open(os.path.join(image_path + ".tif"))
        high_image = torch.from_numpy(np.array(high_image).astype(dtype=np.float32)/255.).permute(2, 0, 1)
        return high_image

    def _add_lon_lat_band(self, gdal_dataset, x_size, y_size):
        image_array = gdal_dataset.ReadAsArray()
        # 获取UTM的投影信息
        source_srs = osr.SpatialReference()
        source_srs.ImportFromWkt(gdal_dataset.GetProjectionRef())

        # 创建WGS84的目标投影信息
        target_srs = osr.SpatialReference()
        target_srs.ImportFromEPSG(4326)  # WGS84坐标系

        # 增加经纬度
        geotransform = gdal_dataset.GetGeoTransform()
        # print(geotransform)
        # 创建坐标转换对象
        transformer = osr.CoordinateTransformation(source_srs, target_srs)

        lon = np.linspace(geotransform[0], geotransform[0] + geotransform[1] * x_size, x_size)
        lat = np.linspace(geotransform[3] + geotransform[5] * y_size, geotransform[3], y_size)
        # 创建坐标点的二维数组
        lon_lat_points = np.array([[l, la] for la in lat for l in lon])

        # 进行坐标转换
        transformed_points = transformer.TransformPoints(lon_lat_points)

        # 分离转换后的经纬度
        lon = np.array([point[0] for point in transformed_points]).reshape(1, x_size, y_size)
        lat = np.array([point[1] for point in transformed_points]).reshape(1, x_size, y_size)
        # print(lon.shape, lat.shape)
        # x = input()
        image_array = np.concatenate((image_array, lon, lat), axis=0)
        return image_array
    
    def _add_time_band(self, image_array, x_size, y_size, high_time_str, low_time_str):
        high_time = datetime.strptime(high_time_str, "%Y%m%d")
        low_time = datetime.strptime(low_time_str, "%Y%m%d")
        time_difference = (low_time - high_time).days
        normalized_difference = time_difference / (2*365)
        expanded_array = np.full((1, x_size, y_size), normalized_difference)
        image_array = np.concatenate((image_array, expanded_array), axis=0)
        # print(f"high_time:{high_time}, low_time:{low_time}, time_difference{time_difference}, normalized_difference:{normalized_difference}")
        # x = input()   
        return image_array

    def _load_S2_image(self, image_path, high_path=None):
        # print(os.path.join(image_path +  "_S2_**.tif"))
        s2_image_paths = glob(os.path.join(image_path +  "_S2_**.tif"))
        s2_image_paths.sort()
        s2_image_paths = s2_image_paths
        # print(s2_image_paths)
        # x = input()
        if "S2_post" in image_path:
            s2_image_paths = s2_image_paths[0:1]
            # print(s2_image_paths)

        s2_time_series_images = []
        for s2_image_path in s2_image_paths:
            dataset = gdal.Open(s2_image_path)
            image_array = dataset.ReadAsArray()
            # x_size = dataset.RasterXSize
            # y_size = dataset.RasterYSize

            # # 增加经纬度波段
            # image_array = self._add_lon_lat_band(dataset, x_size, y_size)

            # # 增加时间波段
            # low_time_str = re.search(r'_(\d{8})\.tif$', os.path.basename(s2_image_path)).group(1)
            # image_array = self._add_time_band(image_array, x_size, y_size, high_time_str, low_time_str)

            s2_time_series_images.append(image_array)
        s2_time_series_images = np.array(s2_time_series_images).reshape((len(s2_image_paths), -1, 48, 48))
        s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)

        # 数据归一化
        s2_time_series_images = torch.from_numpy(np.array(s2_time_series_images, dtype=np.float32))
        s2_time_series_images = torch.where(torch.isnan(s2_time_series_images), torch.tensor(0), s2_time_series_images)
        s2_time_series_images = (s2_time_series_images - self.s2_mean[None, :, None, None]) / self.s2_std[None, :, None, None]
        return s2_time_series_images#[:, 4:5, :, :]

    def _load_label(self, label_path):
        # print(label_path)
        label = gdal.Open(label_path)
        label = label.ReadAsArray()
        label = torch.from_numpy(np.array(label, dtype=np.float32))
        label = torch.where(torch.isnan(label), torch.tensor(0.), label)
        return label
    
    def _data_augmentation(self, pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label):
        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[2])
            pre_s2_images = pre_s2_images.flip(dims=[3])
            post_s2_images = post_s2_images.flip(dims=[3])
            pre_label = pre_label.flip(dims=[1])
            post_label = post_label.flip(dims=[1])

        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[1])
            pre_s2_images = pre_s2_images.flip(dims=[2])
            post_s2_images = post_s2_images.flip(dims=[2])
            pre_label = pre_label.flip(dims=[0])
            post_label = post_label.flip(dims=[0])

        return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label

    def __getitem__(self, item):
        # assert os.path.basename(self.pre_high_image_paths[item]).split(".pre")[0] == os.path.basename(self.post_label_paths[item]).split(".post")[0]
        pre_high_image = self._load_high_image(self.pre_high_image_paths[item])
        pre_s2_images = self._load_S2_image(self.pre_s2_image_paths[item])
        post_s2_images = self._load_S2_image(self.post_s2_image_paths[item])
        post_label = self._load_label(self.post_label_paths[item])
        # print(os.path.basename(self.pre_high_image_paths[item]).split(".pre")[0], os.path.basename(self.post_label_paths[item]).split(".post")[0])

        pre_label = torch.where(post_label.clone()>0., 1, 0)
        post_label = torch.where(post_label.clone()>1., 1, 0)

        if self.mode == "train":
            pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label = self._data_augmentation(pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label)
        return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label

def dataset_collate(batch):
    pre_high_image = torch.stack([item[0] for item in batch])
    pre_s2_images = torch.stack([item[1] for item in batch])
    post_s2_images = torch.stack([item[2] for item in batch])
    pre_label = torch.stack([item[3] for item in batch])
    post_label = torch.stack([item[4] for item in batch])
    return pre_high_image, pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label

if __name__ == "__main__":
    train_dataset = Turkey_S2('train')
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=False, num_workers=0, drop_last=False, collate_fn=dataset_collate)
    print(len(train_loader.dataset))
    for batch_id, inputs in enumerate(train_loader):
        print(batch_id, inputs[0].shape, inputs[1].shape, inputs[2].shape, inputs[3].shape, inputs[4].shape, inputs[5].shape)
        # print(torch.max(inputs[0]), torch.min(inputs[0]), torch.max(inputs[1]), torch.min(inputs[1]))
        # print(torch.max(inputs[2]), torch.min(inputs[2]), torch.max(inputs[3]), torch.min(inputs[3]))
        print(torch.max(inputs[4]), torch.min(inputs[4]), torch.max(inputs[5]), torch.min(inputs[5]))
        # print(torch.mean(inputs[2][:,:,1], ), torch.mean(inputs[2][:,:,1]), torch.mean(inputs[3][:,:,1]), torch.mean(inputs[3][:,:,1]))

