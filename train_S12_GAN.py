import warnings
warnings.filterwarnings("ignore")

from einops import rearrange
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.nn.parallel import DistributedDataParallel as DDP
import torch.distributed as dist
from torch.utils.data.distributed import DistributedSampler
import torch.multiprocessing
torch.multiprocessing.set_sharing_strategy('file_system')
import torchvision.transforms as T

import os
import datetime
from tqdm import tqdm
import argparse
import numpy as np
import random
from pathlib import Path
from utils.cfg import py2cfg
import loss.lovasz_loss as L
from loss.srloss import GANLoss, PerceptualLoss, USMSharp
from model.LBD_S12v26 import UNetDiscriminatorSN

def seed_it(seed):
    random.seed(seed)
    os.environ["PYTHONSEED"] = str(seed)
    np.random.seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.manual_seed(seed)
seed_it(1)

class train():
    def __init__(self, config):
        self.config     = config
        self.criterion3 = nn.CrossEntropyLoss(reduction='mean')
        self.num_gpus   = torch.cuda.device_count()
        self.device     = torch.device("cuda")
        self.grayscale_transform = T.Grayscale(num_output_channels=1)
        self.alpha = 0.25
        self.temperature = 1

    def DDP_setup(self, ):
        torch.distributed.init_process_group(backend='nccl')
        self.local_rank = torch.distributed.get_rank()
        torch.cuda.set_device(self.local_rank)
        self.device = torch.device("cuda", self.local_rank)

    def load_net(self, net, pre_train_model_path):
        if self.num_gpus > 1:
            if dist.get_rank() == 0:
                print(f"Let's use {torch.cuda.device_count()} GPUs to train and val in DDP mode")
            net = DDP(net, device_ids=[self.local_rank], output_device=self.local_rank, find_unused_parameters=True)
            net = torch.nn.SyncBatchNorm.convert_sync_batchnorm(net)
        if pre_train_model_path is not None:
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(pre_train_model_path, map_location=self.device)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)

        return net

    def load_dataset(self, ):
        # ----------------------------------------- #
        # 加载数据集
        # ----------------------------------------- #
        train_sampler = DistributedSampler(self.config.train_dataset, drop_last=True)
        val_sampler   = DistributedSampler(self.config.val_dataset, drop_last=True)
        train_loader  = DataLoader(self.config.train_dataset, batch_size=config.batch_size, shuffle=False, sampler=train_sampler, num_workers=0, pin_memory=True, drop_last=True, collate_fn=config.collate_fn, persistent_workers=False)
        val_loader    = DataLoader(self.config.val_dataset, batch_size=config.batch_size, shuffle=False, sampler=val_sampler, num_workers=0, pin_memory=True, drop_last=True, collate_fn=config.collate_fn, persistent_workers=False)
        
        if dist.get_rank() == 0:
            print(f"train dataset:{len(train_loader.dataset):4d}, test dataset:{len(val_loader.dataset):4d}")
        return train_loader, val_loader
    
    def _cal_loss(self, pre, lab):
        loss = config.criterion1(pre, lab) + 0.75*L.lovasz_softmax(F.softmax(pre, dim=1).squeeze(dim=1), lab, ignore=255)
        return loss
    
    def _cal_distillation_loss(self, student_logits, teacher_logits):
        distillation_loss = nn.KLDivLoss()(F.log_softmax(student_logits / self.temperature, dim=1), F.softmax(teacher_logits / self.temperature, dim=1))
        return distillation_loss
    
    def net_forward(self, inputs, net, mode, optimizer):
        config.criterion1 = config.criterion1.to(self.device)
        pre_label = inputs[3].to(torch.long).to(self.device)
        post_label = inputs[4].to(torch.long).to(self.device)
        # label_gray = self.grayscale_transform(inputs[0].to(torch.float32).to(self.device))
        rgb_usm_label = self.net_usm(inputs[0].to(torch.float32).to(self.device))

        if mode=="train": 
            # 第一步: 优化生成器
            for p in self.net_d.parameters():
                p.requires_grad = False
            outputs = net(inputs, self.device)
            high_out = outputs["pre_high_building"]
            # loss = self._cal_loss(high_out, pre_label)# + nn.L1Loss()(outputs["pre_high_gray"], label_gray_usm)
            # loss = loss + self._cal_loss(outputs["pre_high_building3"], pre_label)
            # loss = loss + self._cal_loss(outputs["pre_high_building4"], pre_label)
            # loss = loss + self._cal_loss(outputs["pre_high_building5"], pre_label)


            pre_out = outputs["pre_low_building"]
            low_rgb = outputs["low_all_rgb_out"]
            loss = (1-self.alpha)*self._cal_loss(pre_out, pre_label) + self.alpha*self._cal_distillation_loss(pre_out, high_out)
            # loss = nn.L1Loss()(low_rgb, rgb_usm_label) + self.cri_perceptual(low_rgb, rgb_usm_label)
            # loss = loss + nn.L1Loss()(low_rgb, rgb_usm_label)# + nn.L1Loss()(outputs["low_last_rgb_out"], F.interpolate(rgb_usm_label, size=(96, 96), mode='bilinear', align_corners=True))
            # loss = self._cal_distillation_loss(pre_out, high_out)

            # 辅助分支
            # pre_label = pre_label.unsqueeze(1)
            # pre_label = F.interpolate(pre_label.to(torch.float32), size=(96, 96), mode='bilinear', align_corners=True)
            # pre_label = torch.where(pre_label>=0.5, 1, 0).detach().to(torch.float32)
            # loss = loss + nn.MSELoss()(outputs["pre_high_features"], outputs["pre_high_features"].detach()*pre_label)
            outputs["pre_high_features"] = F.interpolate(outputs["pre_high_features"], size=(384, 384), mode='bilinear', align_corners=True)
            loss = loss + nn.MSELoss()(outputs["pre_low_features"], outputs["pre_high_features"].detach())
            loss = loss + outputs["loss"]


            # GAN 分支
            # fake_g_pred = self.net_d(torch.cat((low_rgb, outputs["pre_last_rgb"]), dim=0))
            # fake_g_pred = self.net_d(torch.cat((F.softmax(pre_out, dim=1), inputs[0].to(torch.float32).to(self.device)), dim=1))
            # l_g_gan = self.cri_gan(fake_g_pred, True, is_disc=False)
            # loss = loss + l_g_gan

            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            # 第二步: 优化GAN网络
            # for p in self.net_d.parameters():
            #     p.requires_grad = True

            # self.optimizer_d.zero_grad()
            # # real
            # # real_d_pred = self.net_d(rgb_usm_label.detach().clone()) 
            # # torch.unsqueeze
            # real_d_pred = self.net_d(torch.cat((1-pre_label.unsqueeze(1).to(torch.float32), pre_label.unsqueeze(1).to(torch.float32), inputs[0].to(torch.float32).to(self.device)), dim=1).detach().clone())
            # l_d_real = self.cri_gan(real_d_pred, True, is_disc=True)
            # l_d_real.backward()
            # # fake
            # # fake_d_pred = self.net_d(torch.cat((low_rgb, outputs["pre_last_rgb"]), dim=0).detach().clone())
            # fake_d_pred = self.net_d(torch.cat((F.softmax(pre_out, dim=1), inputs[0].to(torch.float32).to(self.device)), dim=1).detach().clone())
            # l_d_fake = self.cri_gan(fake_d_pred, False, is_disc=True)
            # l_d_fake.backward()
            # self.optimizer_d.step()

        else:
            with torch.no_grad():
                outputs = net(inputs, self.device)
                high_out = outputs["pre_high_building"]
                pre_out = outputs["pre_low_building"]
                # post_out = outputs["post_low_damage"]
                loss = self._cal_loss(high_out, pre_label)
                low_rgb = outputs["low_all_rgb_out"]
                # loss = nn.L1Loss()(low_rgb, rgb_usm_label)
        # return high_out, pre_out, pre_out, loss
        # return post_out, post_out, post_out, loss
        # return high_out, high_out, high_out, loss
        # return high_out, high_out, low_rgb, loss
        return high_out, pre_out, low_rgb, loss


    def get_confusion_matrix(self, pre, lab, confusion_matrix, num_class):
        pre = torch.argmax(F.softmax(pre, dim=1), dim=1)

        mask = (lab >= 0) & (lab < num_class)
        confusion_matrix = confusion_matrix + torch.bincount(num_class * lab[mask].to(torch.int16) + pre[mask], minlength=num_class ** 2).reshape(num_class, num_class)
        return confusion_matrix

    def cal_f1(self, confusion_matrix):
        precision = torch.diag(confusion_matrix) / confusion_matrix.sum(axis = 0)
        recall    = torch.diag(confusion_matrix) / confusion_matrix.sum(axis = 1)
        f1score   = 2 * precision * recall / (precision + recall)
        return torch.mean(f1score[1:]).item()*100.

    def cal_psnr(self, low_rgb, high_rgb):
        """Calculate PSNR between two images or batches.
        Args:
            low_rgb  (Tensor): predicted image, shape [B,C,H,W] or [C,H,W]
            high_rgb (Tensor): ground truth, same shape
        Returns:
            PSNR value (float)
        """
        if low_rgb.dim() == 3:
            low_rgb = low_rgb.unsqueeze(0)
            high_rgb = high_rgb.unsqueeze(0)
        low_rgb = torch.clamp(low_rgb, 0, 1)
        mse = F.mse_loss(low_rgb, high_rgb, reduction='none')
        mse = mse.mean(dim=[1, 2, 3])  # per-image MSE
        psnr = -10.0 * torch.log10(mse)
        return psnr.mean().item()

    def train_val_function(self, epoch, mode, data_loader, net, optimizer):
        total_loss = 0
        high_confusion_matrix = torch.zeros((config.output_building, config.output_building))
        pre_confusion_matrix = torch.zeros((config.output_building, config.output_building))
        post_confusion_matrix = torch.zeros((config.output_damage, config.output_damage))
        low_psnr = 0
        low_last_psnr = 0

        if mode=="train":
            data_loader.sampler.set_epoch(epoch)
        if dist.get_rank() == 0:
            total = int(len(data_loader.dataset)/config.batch_size/self.num_gpus)
            pbar = tqdm(total=total, desc=f"Epoch {epoch+1}/{config.epochs}", postfix=dict, mininterval=0.3, ncols=150)
        batch_id = 0
        for batch_id, inputs in enumerate(data_loader):
            high_out, pre_out, post_out, loss = self.net_forward(inputs, net, mode, optimizer)
            total_loss += float(loss.item())

            high_confusion_matrix = self.get_confusion_matrix(high_out, inputs[3].to(torch.long).to(self.device), high_confusion_matrix.to(self.device), config.output_building)
            pre_confusion_matrix = self.get_confusion_matrix(pre_out, inputs[3].to(torch.long).to(self.device), pre_confusion_matrix.to(self.device), config.output_building)
            # post_confusion_matrix = self.get_confusion_matrix(post_out, inputs[4].to(torch.long).to(self.device), post_confusion_matrix.to(self.device), config.output_damage)

            low_psnr += self.cal_psnr(post_out, inputs[0].to(torch.float).to(self.device))
            # low_last_psnr += self.cal_psnr(pre_out, inputs[0].to(torch.float).to(self.device))

            if dist.get_rank() == 0:
                pbar.set_postfix(**{'total_loss':total_loss/float(batch_id+1), 'high_f1':self.cal_f1(high_confusion_matrix), 'pre_f1':self.cal_f1(pre_confusion_matrix), "low_psnr":low_psnr/float(batch_id+1)})
                pbar.update(1)
            
        if dist.get_rank() == 0:
            pbar.close()
        with open(os.path.join(self.config.save_path, mode+".txt"), "a+") as f:
            total_loss = total_loss/float(batch_id+1)
            high_f1 = self.cal_f1(high_confusion_matrix)
            pre_f1 = self.cal_f1(pre_confusion_matrix)
            low_psnr = low_psnr/float(batch_id+1)
            low_last_psnr = low_last_psnr/float(batch_id+1)
            f.write(f"{epoch+1}/{config.epochs}, loss:{total_loss}, high_f1:{high_f1}, pre_f1:{pre_f1}, low_psnr:{low_psnr}\n")
        f.close()

    def __call__(self,):
        # torch.backends.cudnn.enabled = True
        # torch.backends.cudnn.benchmark = True
        self.DDP_setup()
        start_time = datetime.datetime.now()
        net = self.load_net(config.model.cuda(), self.config.pre_train_model_path)

        # ----------------------------------------- #
        # 加载数据集
        # ----------------------------------------- #            
        optimizer = optim.AdamW(filter(lambda p: p.requires_grad, net.parameters()), lr=config.lr, weight_decay=0.0005)
        train_loader, val_loader = self.load_dataset()

        # 加载感知损失
        self.cri_perceptual = PerceptualLoss(loss_weight=1.0, use_input_norm=True, use_range_norm=False).to(self.device)
        # 加载 GAN 网络
        self.net_d = UNetDiscriminatorSN(num_in_ch=5, num_feat=64, skip_connection=True).to(self.device)
        pretrain_d_path = "/mnt/d2/zxq/BDS12/model/PTH/RealESRGAN_x4plus_netD.pth"
        if pretrain_d_path is not None:
            current_model_dict = self.net_d.state_dict()
            loaded_state_dict = torch.load(pretrain_d_path, map_location=self.device)['params']
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            self.net_d.load_state_dict(new_state_dict, strict=False)
        self.net_d = self.load_net(self.net_d, None)
        self.net_d.train()

        self.cri_gan = GANLoss('vanilla', loss_weight=0.1).to(self.device)
        self.optimizer_d = torch.optim.Adam(params=self.net_d.parameters(), lr=1e-4, betas=(0.9, 0.99), weight_decay=0)

        # 加载锐化网络
        self.net_usm = USMSharp().to(self.device)
        # self.net_usm = self.load_net(self.net_usm, None)

        for epoch in range(config.epochs):
            net.train()
            self.train_val_function(epoch, "train", train_loader, net, optimizer)

            if (epoch+1)%1==0 or (config.epochs - epoch)<5:
                torch.save(net.state_dict(), os.path.join(str(self.config.save_path), str(epoch+1) + ".pt"))
            torch.cuda.empty_cache()

            net.eval()
            self.train_val_function(epoch, "val", val_loader, net, optimizer)

        # ----------------------------------------- #
        # 计算训练时间
        # ----------------------------------------- #            
        end_time   = datetime.datetime.now()
        total_time = end_time - start_time
        
        # ----------------------------------------- #
        # 保存训练文件
        # ----------------------------------------- #
        with open(os.path.join(self.config.save_path, "time.txt"), "a+") as f:
            f.write(f"start_time:{start_time}, end_time:{end_time}, total_time:{total_time}")
        f.close()

        # 保存鉴别器参数
        torch.save(self.net_d.state_dict(), os.path.join(str(self.config.save_path), "discrimination_" + str(epoch+1) + ".pt"))

def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument
    arg("--config_path", default="./config/S2_HRNet.py", type=Path, help="Path to the config.", required=False)
    # parser.add_argument("--cfg", type=str, default='./changedetection/configs/vssm1/vssm_base_224.yaml', required=False)
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()
    config = py2cfg(args.config_path)
    train_function = train(config)
    train_function()
