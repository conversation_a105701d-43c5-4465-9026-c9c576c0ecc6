#!/usr/bin/env python3
"""
测试极小维度的RDDM模型
"""

import torch
import torch.nn as nn
from building_rddm_denoiser import BuildingSegmentationRDDM

def test_tiny_model():
    """测试极小维度的模型"""
    print("=== 测试极小维度RDDM模型 ===")
    
    # 测试不同的极小维度
    test_dims = [2, 4, 8, 16]
    
    for dim in test_dims:
        print(f"\n--- 测试 unet_dim={dim} ---")
        
        try:
            # 创建极小模型
            model = BuildingSegmentationRDDM(
                unet_dim=dim,
                unet_dim_mults=(1, 2),  # 只用两层
                channels=2,
                timesteps=100,  # 很少的时间步
                sampling_timesteps=10,  # 很少的采样步
                objective='pred_res_noise'
            )
            
            # 计算参数数量
            total_params = sum(p.numel() for p in model.parameters())
            trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
            
            print(f"✓ 模型创建成功")
            print(f"  总参数: {total_params:,}")
            print(f"  可训练参数: {trainable_params:,}")
            
            # 测试前向传播
            batch_size = 1
            height, width = 64, 64  # 很小的图像
            
            # 模拟数据
            high_seg = torch.randn(batch_size, 2, height, width)
            low_seg = torch.randn(batch_size, 2, height, width)
            
            # 应用softmax确保是有效的分割概率
            high_seg = torch.softmax(high_seg, dim=1)
            low_seg = torch.softmax(low_seg, dim=1)
            
            # 测试训练前向传播
            model.train()
            loss, pred_x_start = model(high_seg, low_seg)
            
            print(f"  训练损失: {loss.item():.6f}")
            print(f"  输出形状: {pred_x_start.shape}")
            print(f"  输出范围: [{pred_x_start.min():.3f}, {pred_x_start.max():.3f}]")
            
            # 测试推理
            model.eval()
            with torch.no_grad():
                enhanced = model.direct_enhance(low_seg, num_steps=5)
                print(f"  增强结果形状: {enhanced.shape}")
                print(f"  增强结果范围: [{enhanced.min():.3f}, {enhanced.max():.3f}]")
            
            print(f"✓ dim={dim} 测试通过")
            
        except Exception as e:
            print(f"✗ dim={dim} 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()

def test_memory_usage():
    """测试内存使用情况"""
    print("\n=== 内存使用测试 ===")
    
    # 测试不同batch size和图像尺寸
    configs = [
        (1, 64, 64, 4),    # 很小
        (1, 128, 128, 8),  # 小
        (2, 64, 64, 8),    # 小batch
        (1, 256, 256, 16), # 中等
    ]
    
    for batch_size, height, width, dim in configs:
        print(f"\n--- 测试 batch={batch_size}, size={height}x{width}, dim={dim} ---")
        
        try:
            model = BuildingSegmentationRDDM(
                unet_dim=dim,
                unet_dim_mults=(1, 2),
                channels=2,
                timesteps=50,
                sampling_timesteps=5,
                objective='pred_res_noise'
            )
            
            # 模拟数据
            high_seg = torch.softmax(torch.randn(batch_size, 2, height, width), dim=1)
            low_seg = torch.softmax(torch.randn(batch_size, 2, height, width), dim=1)
            
            # 测试前向传播
            model.train()
            loss, pred_x_start = model(high_seg, low_seg)
            
            # 计算内存使用（粗略估计）
            model_size = sum(p.numel() * 4 for p in model.parameters()) / (1024**2)  # MB
            data_size = (high_seg.numel() + low_seg.numel() + pred_x_start.numel()) * 4 / (1024**2)  # MB
            
            print(f"✓ 测试成功")
            print(f"  模型大小: {model_size:.2f} MB")
            print(f"  数据大小: {data_size:.2f} MB")
            print(f"  损失: {loss.item():.6f}")
            
        except Exception as e:
            print(f"✗ 测试失败: {str(e)}")

if __name__ == "__main__":
    test_tiny_model()
    test_memory_usage()
    print("\n=== 所有测试完成 ===")
