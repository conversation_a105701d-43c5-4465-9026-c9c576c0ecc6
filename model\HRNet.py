from thop import profile
import torch
import torch.nn as nn
import timm
import numpy as np
import torch.nn.functional as F


class residual_up_decoder(nn.Module):
    def __init__(self, decoder_channnel):
        super(residual_up_decoder, self).__init__()
        self.conv_path = nn.Sequential(
            nn.BatchNorm2d(decoder_channnel),
            nn.ReLU(),
            nn.Conv2d(decoder_channnel, decoder_channnel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channnel),
            nn.ReLU(),
            nn.Conv2d(decoder_channnel, decoder_channnel, kernel_size=3, padding=1),
            nn.BatchNorm2d(decoder_channnel),
            nn.ReLU(),
        )

        self.res = nn.Conv2d(decoder_channnel, decoder_channnel, kernel_size=1)
        self.deconv = nn.Sequential(
            nn.ConvTranspose2d(decoder_channnel, decoder_channnel, kernel_size=4, stride=2, padding=1),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
            )

    def forward(self, x):
        x = self.conv_path(x) + self.res(x)
        x = self.deconv(x)
        return x

class HRNet(nn.Module):
    def __init__(self, pretrained=False, s2_inchannel=18, num_classes=3):
        super(HRNet, self).__init__()
        self.rgb_backbone = timm.create_model('hrnet_w48', pretrained=pretrained, features_only=True, out_indices=(1, 2, 3, 4))
        self.pre_train_model_path = "./net/hrnetv2_w48_imagenet_pretrained.pth"
        # self.rgb_backbone = self._load_weight(self.rgb_backbone)
        last_inp_channels = np.int32(np.sum(self.rgb_backbone.feature_info.channels()))
        decoder_channnel = self.rgb_backbone.feature_info.channels()[0]

        self.rgb_decoder_layer = nn.Sequential(
            nn.Conv2d(in_channels=last_inp_channels, out_channels=decoder_channnel, kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
        )
        self.class_layer1 = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channnel, out_channels=decoder_channnel, kernel_size=3, stride=1, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=decoder_channnel, out_channels=num_classes, kernel_size=1, stride=1, padding=0),
        )

    def _load_weight(self, net):
        if self.pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.pre_train_model_path)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
    
    def decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x

    def forward(self, inputs, device):
        output = {}
        high_features = self.rgb_backbone(inputs[0].to(device).to(torch.float32))
        high_features = self.decoder(high_features, self.rgb_decoder_layer)
        high_outs = self.class_layer1(high_features)
        high_outs = F.interpolate(high_outs, size=(384, 384), mode='bilinear', align_corners=True)
        output["high_building"] = high_outs[:, :2]
        output["high_gray"] = high_outs[:, 2:]
        return output, high_features


class HRNet_single(nn.Module):
    def __init__(self, pretrained=False, s2_inchannel=18, num_classes=3):
        super(HRNet_single, self).__init__()
        self.num_classes = num_classes
        self.rgb_backbone = timm.create_model('hrnet_w48', pretrained=pretrained, features_only=True, out_indices=(1, 2, 3, 4))
        self.pre_train_model_path = "./net/hrnetv2_w48_imagenet_pretrained.pth"
        last_inp_channels = np.int32(np.sum(self.rgb_backbone.feature_info.channels()))
        decoder_channnel = self.rgb_backbone.feature_info.channels()[0]

        self.rgb_decoder_layer = nn.Sequential(
            nn.Conv2d(in_channels=last_inp_channels, out_channels=decoder_channnel, kernel_size=1, stride=1, padding=0),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=decoder_channnel, out_channels=decoder_channnel, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
        )
        self.class_layer1 = nn.Conv2d(in_channels=decoder_channnel, out_channels=num_classes, kernel_size=1, stride=1, padding=0)

    def _load_weight(self, net):
        if self.pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.pre_train_model_path)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net
    
    def decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x

    def forward(self, images, device, mode="pre", output={}):
        B, C, H, W = images.shape
        high_features = self.rgb_backbone(images.to(device).to(torch.float32))
        high_features = self.decoder(high_features, self.rgb_decoder_layer)
        high_outs = self.class_layer1(high_features)
        high_outs = F.interpolate(high_outs, size=(H, W), mode='bilinear', align_corners=True)
        output[f"{mode}_high_building"] = high_outs[:, :self.num_classes-1]
        output[f"{mode}_high_gray"] = high_outs[:, self.num_classes-1:]
        output[f"{mode}_high_features"] = high_features
        return output

class HRNet_S2(nn.Module):
    def __init__(self, pretrained, s2_inchannel=16, num_classes=3):
        super(HRNet_S2, self).__init__()

        self.high_hrnet = HRNet()
        self.pre_train_model_path = "./result/UKRS2/S2_HRNet/2high/7.pt"
        self.high_hrnet = self._load_weight(self.high_hrnet)
        self.setup()

        self.low_hrnet = HRNet()
        self.pre_train_model_path = "./result/UKRS2/S2_HRNet/2high/7.pt"
        self.high_hrnet = self._load_weight(self.high_hrnet)
        self.s2_backbone = self.low_hrnet.rgb_backbone
        self.s2_backbone.conv1.stride = 1
        self.s2_backbone.conv2.stride = 1
        conv1_weights = self.s2_backbone.conv1.weight
        # print(conv1_weights_mean.shape)
        conv1_weights_mean = conv1_weights.mean(dim=1, keepdim=True)
        new_conv1_weights = conv1_weights_mean.repeat(1, s2_inchannel, 1, 1)
        self.s2_backbone.conv1.in_inchannel = s2_inchannel
        self.s2_backbone.conv1.weight.data = new_conv1_weights

        decoder_channnel = self.s2_backbone.feature_info.channels()[0]

        self.s2_decoder_layer = self.low_hrnet.rgb_decoder_layer
        self.s2_class_layer = self.low_hrnet.class_layer1

        # self.s2_class_layer = nn.Sequential(
        #     nn.Conv2d(in_channels=decoder_channnel, out_channels=decoder_channnel, kernel_size=3, stride=1, padding=1),
        #     nn.ReLU(inplace=True),
        #     nn.Conv2d(in_channels=decoder_channnel, out_channels=num_classes, kernel_size=1, stride=1, padding=0),
        # )

        self.s2_demage_layer = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channnel, out_channels=decoder_channnel, kernel_size=1, stride=1, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels=decoder_channnel, out_channels=1, kernel_size=3, stride=1, padding=0),
        )

        self.up3 = residual_up_decoder(decoder_channnel)
        self.up2 = residual_up_decoder(decoder_channnel)
        self.up1 = residual_up_decoder(decoder_channnel)

        self.con_cat = nn.Sequential(
            nn.Conv2d(in_channels=decoder_channnel*2, out_channels=decoder_channnel, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(decoder_channnel, momentum= 0.1),
            nn.ReLU(inplace=True),
        )

        self.dup3 = residual_up_decoder(decoder_channnel)
        self.dup2 = residual_up_decoder(decoder_channnel)
        self.dup1 = residual_up_decoder(decoder_channnel)

    def setup(self):
        self.high_hrnet.train()
        for name, param in self.high_hrnet.named_parameters():
            param.requires_grad = False

    def _load_weight(self, net):
        if self.pre_train_model_path is not None:
            net.train()
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.pre_train_model_path)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net

    def decoder(self, feature_list, layer):
        x0_h, x0_w = feature_list[0].size(2), feature_list[0].size(3)
        x1 = F.interpolate(feature_list[1], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x2 = F.interpolate(feature_list[2], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x3 = F.interpolate(feature_list[3], size=(x0_h, x0_w), mode='bilinear', align_corners=True)
        x = torch.cat([feature_list[0], x1, x2, x3], 1)
        x = layer(x)
        return x

    def a_loss(self, T_feature, S_feature):
        loss = torch.norm((F.normalize(T_feature, p=2, dim=1) - F.normalize(S_feature, p=2, dim=1)), dim=1)
        loss = torch.mean(loss)
        return loss

    def forward(self, inputs, device):
        output = {}
        with torch.no_grad():
            output, high_features = self.high_hrnet(inputs, device)

        s2_time_serise = inputs[1].to(device).to(torch.float32)
        s2_time_serise = torch.cat((inputs[1].to(device).to(torch.float32), inputs[4].to(device).to(torch.float32)), dim=1)
        s2_time_serise = s2_time_serise[:, :, :16, :, :]
        B, frame, C, H, W = s2_time_serise.shape
        s2_time_serise = s2_time_serise.view(B*frame, C, H, W)
        low_features = self.s2_backbone(s2_time_serise)
        low_features0 = self.decoder(low_features, self.s2_decoder_layer).view(B, frame, -1, H, W)
        low_features1 = torch.mean(low_features0[:, :-1, :, :, :], dim=1, keepdim=False)
        low_features = self.up3(low_features1)
        low_features = self.up2(low_features)
        low_features = self.up1(low_features)
        low_outs = self.s2_class_layer(low_features)
        low_outs = F.interpolate(low_outs, size=(384, 384), mode='bilinear', align_corners=True)
        output["low_building"] = low_outs[:, :2]
        output["low_gray"] = low_outs[:, 2:3]

        damage_feature = low_features0[:, -1, :, :, :]
        damage_feature = self.con_cat(torch.cat((damage_feature, low_features1), dim=1))
        damage_feature = self.dup3(damage_feature)
        damage_feature = self.dup2(damage_feature)
        damage_feature = self.dup1(damage_feature)
        heat_map = self.s2_demage_layer(damage_feature)
        heat_map = F.interpolate(heat_map, size=(384, 384), mode='bilinear', align_corners=True)
        output["heat_map"] = heat_map
        return output


if __name__ == "__main__":
    deep_model = HRNet_single(pretrained=False, num_classes=3).cuda()
    
    hight_image = torch.rand(1, 3, 384, 384).cuda()
    low_images1 = torch.rand(1, 23, 18, 48, 48).cuda()
    low_images2 = torch.rand(1, 1, 18, 48, 48).cuda()
    inputs = [hight_image, low_images1, hight_image, hight_image, low_images2,]
    device = torch.device("cuda")

    outputs = deep_model(inputs, device)
    # for output in outputs:
    #     print(output.shape)

    flops, params = profile(deep_model, inputs=(inputs, device, ))
    print(f'FLOPs: {flops / 1e9} G, Params: {params / 1e6} M')
