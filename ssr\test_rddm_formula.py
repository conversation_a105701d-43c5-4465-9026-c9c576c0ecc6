#!/usr/bin/env python3
"""
测试修正后的RDDM公式
验证残差定义的正确性
"""

import torch
import torch.nn as nn
from building_rddm_denoiser import BuildingSegmentationRDDM

def test_rddm_formula():
    """测试RDDM公式的正确性"""
    print("=== 测试RDDM公式正确性 ===")
    
    # 创建简单模型
    model = BuildingSegmentationRDDM(
        unet_dim=16,
        unet_dim_mults=(1, 2),
        channels=2,
        timesteps=100,
        sampling_timesteps=10,
        objective='pred_res_noise'
    )
    
    # 模拟数据
    batch_size = 1
    height, width = 64, 64
    
    # 创建有明显差异的高质量和低质量分割
    high_seg = torch.zeros(batch_size, 2, height, width)
    high_seg[:, 0, 20:40, 20:40] = 1.0  # 前景区域
    high_seg[:, 1] = 1.0 - high_seg[:, 0]  # 背景区域
    
    # 低质量分割：添加噪声和模糊
    low_seg = high_seg.clone()
    low_seg += torch.randn_like(low_seg) * 0.1  # 添加噪声
    low_seg = torch.softmax(low_seg, dim=1)  # 重新归一化
    
    print(f"高质量分割范围: [{high_seg.min():.3f}, {high_seg.max():.3f}]")
    print(f"低质量分割范围: [{low_seg.min():.3f}, {low_seg.max():.3f}]")
    
    # 测试前向过程
    t = torch.randint(0, model.num_timesteps, (batch_size,))
    
    # 计算残差（按照原始RDDM: x_res = x_input - x_start）
    x_res_true = low_seg - high_seg
    print(f"真实残差范围: [{x_res_true.min():.3f}, {x_res_true.max():.3f}]")
    
    # 测试q_sample
    x_noisy, x_res_computed, noise = model.q_sample(high_seg, low_seg, t)
    
    print(f"计算残差范围: [{x_res_computed.min():.3f}, {x_res_computed.max():.3f}]")
    print(f"噪声分割范围: [{x_noisy.min():.3f}, {x_noisy.max():.3f}]")
    
    # 验证残差计算是否正确
    residual_diff = torch.abs(x_res_computed - x_res_true).mean()
    print(f"残差计算误差: {residual_diff:.6f}")
    
    # 测试完整的训练前向传播
    model.train()
    loss, pred_x_start = model(high_seg, low_seg)
    
    print(f"训练损失: {loss.item():.6f}")
    print(f"预测结果形状: {pred_x_start.shape}")
    print(f"预测结果范围: [{pred_x_start.min():.3f}, {pred_x_start.max():.3f}]")
    
    # 验证预测结果是否合理
    pred_error = torch.abs(pred_x_start - high_seg).mean()
    print(f"预测误差: {pred_error:.6f}")
    
    return True

def test_residual_direction():
    """测试残差方向的影响"""
    print("\n=== 测试残差方向影响 ===")
    
    # 创建两个模型来比较不同的残差定义
    model = BuildingSegmentationRDDM(
        unet_dim=8,
        unet_dim_mults=(1, 2),
        channels=2,
        timesteps=50,
        sampling_timesteps=5,
        objective='pred_res_noise'
    )
    
    # 创建测试数据
    batch_size = 1
    height, width = 32, 32
    
    # 高质量分割：清晰的前景/背景
    high_seg = torch.zeros(batch_size, 2, height, width)
    high_seg[:, 0, 10:20, 10:20] = 1.0
    high_seg[:, 1] = 1.0 - high_seg[:, 0]
    
    # 低质量分割：模糊的边界
    low_seg = high_seg.clone()
    # 添加高斯模糊效果
    kernel = torch.ones(1, 1, 3, 3) / 9.0
    low_seg[:, 0:1] = nn.functional.conv2d(low_seg[:, 0:1], kernel, padding=1)
    low_seg[:, 1:2] = nn.functional.conv2d(low_seg[:, 1:2], kernel, padding=1)
    low_seg = torch.softmax(low_seg, dim=1)
    
    print(f"高质量前景均值: {high_seg[:, 0].mean():.3f}")
    print(f"低质量前景均值: {low_seg[:, 0].mean():.3f}")
    
    # 测试当前实现（x_res = x_condition - x_start）
    t = torch.randint(0, model.num_timesteps, (batch_size,))
    x_noisy, x_res, noise = model.q_sample(high_seg, low_seg, t)
    
    print(f"残差均值: {x_res.mean():.6f}")
    print(f"残差标准差: {x_res.std():.6f}")
    
    # 分析残差的含义
    if x_res.mean() > 0:
        print("残差为正：表示从高质量到低质量的退化")
    else:
        print("残差为负：表示从低质量到高质量的改进")
    
    # 测试训练
    model.train()
    loss, pred_x_start = model(high_seg, low_seg)
    
    print(f"训练损失: {loss.item():.6f}")
    
    # 测试推理
    model.eval()
    with torch.no_grad():
        enhanced = model.direct_enhance(low_seg, num_steps=3)
        
        # 计算改进程度
        original_error = torch.abs(low_seg - high_seg).mean()
        enhanced_error = torch.abs(enhanced - high_seg).mean()
        improvement = original_error - enhanced_error
        
        print(f"原始误差: {original_error:.6f}")
        print(f"增强后误差: {enhanced_error:.6f}")
        print(f"改进程度: {improvement:.6f}")
        
        if improvement > 0:
            print("✓ 模型成功改进了分割质量")
        else:
            print("✗ 模型未能改进分割质量")
    
    return True

def compare_formula_consistency():
    """比较公式一致性"""
    print("\n=== 验证公式一致性 ===")
    
    model = BuildingSegmentationRDDM(
        unet_dim=4,
        unet_dim_mults=(1,),
        channels=2,
        timesteps=10,
        sampling_timesteps=3,
        objective='pred_res_noise'
    )
    
    # 简单测试数据
    high_seg = torch.tensor([[[[1.0, 0.0], [0.0, 1.0]], [[0.0, 1.0], [1.0, 0.0]]]])
    low_seg = torch.tensor([[[[0.8, 0.2], [0.2, 0.8]], [[0.2, 0.8], [0.8, 0.2]]]])
    
    print("测试数据:")
    print(f"高质量: {high_seg.flatten()}")
    print(f"低质量: {low_seg.flatten()}")
    
    # 手动计算残差
    manual_residual = low_seg - high_seg
    print(f"手动残差: {manual_residual.flatten()}")
    
    # 模型计算残差
    t = torch.tensor([5])
    x_noisy, model_residual, noise = model.q_sample(high_seg, low_seg, t)
    print(f"模型残差: {model_residual.flatten()}")
    
    # 验证一致性
    residual_match = torch.allclose(manual_residual, model_residual, atol=1e-6)
    print(f"残差计算一致性: {'✓' if residual_match else '✗'}")
    
    # 验证前向过程
    alpha = model.alphas_cumsum[t]
    beta = model.betas_cumsum[t]
    
    manual_noisy = high_seg + alpha * manual_residual + beta * noise
    print(f"手动前向: {manual_noisy.flatten()[:4]}")
    print(f"模型前向: {x_noisy.flatten()[:4]}")
    
    forward_match = torch.allclose(manual_noisy, x_noisy, atol=1e-6)
    print(f"前向过程一致性: {'✓' if forward_match else '✗'}")
    
    return residual_match and forward_match

if __name__ == "__main__":
    success1 = test_rddm_formula()
    success2 = test_residual_direction()
    success3 = compare_formula_consistency()
    
    print(f"\n=== 测试结果 ===")
    print(f"RDDM公式测试: {'✓' if success1 else '✗'}")
    print(f"残差方向测试: {'✓' if success2 else '✗'}")
    print(f"公式一致性测试: {'✓' if success3 else '✗'}")
    
    if all([success1, success2, success3]):
        print("✓ 所有测试通过！RDDM公式修正成功")
    else:
        print("✗ 部分测试失败，需要进一步检查")
