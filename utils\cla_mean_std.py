# -*- coding: utf-8 -*-
from __future__ import division
import numpy as np
from glob import glob
from osgeo import gdal
from tqdm import tqdm

class cal_mean_std():
    def __init__(self, h_avg=0, h_std=0, n=0):
        self.mean = h_avg
        self.std = h_std
        self.n = n

    def cal_first_data(self, data, tif_path):
        _, h, w = data.shape
        mean = np.nanmean(data, axis=(1,2))
        std = np.nanstd(data, axis=(1,2))
        self.mean = mean
        self.std = std
        self.n = np.count_nonzero(~np.isnan(data), axis=(1, 2))

    def incre_in_data(self, data, tif_path):
        # data = np.where(data==Nan, 1e-6, data)

        data = np.nan_to_num(data, nan=0)
        new_mean = np.nanmean(data, axis=(1,2))
        new_std = np.nanstd(data, axis=(1,2))
        new_n = np.count_nonzero(~np.isnan(data), axis=(1, 2))
        new_n = np.where(new_n==0, 1, new_n)
        # if np.isnan(data).any():
        #     print(tif_path)
        #     print(new_n)
        #     x = input()

        incre_mean = (self.n*self.mean+new_n*new_mean) / (self.n+new_n)
        incre_std = np.sqrt((self.n*(self.std**2+(incre_mean-self.mean)**2)+new_n*(new_std**2+(incre_mean-new_mean)**2))/(self.n+new_n))
        self.avg = incre_mean
        self.std = incre_std
        self.n += new_n
        # print(incre_mean, incre_std, new_n, self.n)

def load_tif(tif_path):
    dataset = gdal.Open(tif_path)
    image_array = dataset.ReadAsArray()
    image_array = np.array(image_array).astype(np.float32)
    return image_array

def remove_outliers_percentile(data, low=1, high=99):
    lower = np.percentile(data, low)
    upper = np.percentile(data, high)
    return data[(data >= lower) & (data <= upper)]


if __name__ == "__main__":
    # c = incre_std_avg()
    # c.incre_in_value(0.02)
    # c.incre_in_list([0.5, 0.2, 0.3])
    # tif_paths = glob("/mnt/d2/zxq/BuildingDamage/dataset/xBDS2/**/S2/*santa*.tif")
    # tif_paths+= glob("/mnt/d2/zxq/BuildingDamage/dataset/xBDS2/**/S2_post/*santa*.tif")
    # tif_paths = glob("/mnt/d2/zxq/BuildingDamage/dataset/palu/**/S2**/**.tif")
    # tif_paths = glob("./dataset/palisades/**/S2/*palisades*.tif")
    # tif_paths += glob("./dataset/palisades/**/S2_post/*palisades*.tif")
    # tif_paths = glob("/mnt/d2/zxq/BuildingDamage/dataset/Turkey/**2/S2/**.tif")
    # tif_paths+= glob("/mnt/d2/zxq/BuildingDamage/dataset/Turkey/**2/S2_post_other_time/*624*.tif")
    # tif_paths+= glob("/mnt/d2/zxq/BuildingDamage/dataset/Turkey/**2/S2_post_other_time_second/**.tif")

    # tif_paths = glob("/mnt/d2/zxq/BuildingDamage/dataset/Gaza/**/S1_buffer_resample_clip/S1/**.tif")

    # tif_paths = glob("./dataset/eaton/**/S2/*eaton*.tif")
    # tif_paths += glob("./dataset/eaton/**/S2_post/*eaton*.tif")
    # tif_paths += glob("./dataset/santa/**/S2/*santa*.tif")
    # tif_paths += glob("./dataset/santa/**/S2_post/*santa*.tif")
    # tif_paths += glob("./dataset/palisades/**/S2/*palisades*.tif")
    # tif_paths += glob("./dataset/palisades/**/S2_post/*palisades*.tif")
    # tif_paths += glob("./dataset/palu/**/S2/*palu*.tif")
    # tif_paths += glob("./dataset/palu/**/S2_post/*palu*.tif")



    # mean_std = cal_mean_std()
    # print(len(tif_paths))
    # for id, tif_path in enumerate(tqdm(tif_paths)):
    #     data = load_tif(tif_path)
    #     data = np.delete(data, 13, axis=0)
    #     # print(data.shape)
    #     if id == 0:
    #         mean_std.cal_first_data(data, tif_path)
    #     else:
    #         mean_std.incre_in_data(data, tif_path)
    #         # break
    #         # print(mean_std.mean, mean_std.std)

    #         # x = input()
    # out = np.stack((mean_std.mean, mean_std.std), axis=0)
    # print(out, out.shape)
    # np.savetxt("./eaton_s2_mean_std.txt", out, fmt='%6f', delimiter=' ')

    # tif_paths = glob("./dataset/santa/**/S2/*santa*.tif")
    # tif_paths+= glob("./dataset/santa/**/S2_post/*santa*.tif")

    # tif_paths = glob("./dataset/gaza/**/S2/*gaza*.tif")
    # tif_paths+= glob("./dataset/gaza/**/S2_post/*gaza*.tif")

    tif_paths = glob("./dataset/palu/**/S2/*palu*.tif")
    tif_paths+= glob("./dataset/palu/**/S2_post/*palu*.tif")
    tif_paths+= glob("./dataset/turkey/**/S2/**.tif")
    tif_paths+= glob("./dataset/turkey/**/S2_post/**.tif")
    all_data = []
    for id, tif_path in enumerate(tqdm(tif_paths)):
        s2 = load_tif(tif_path)
        s2 = np.delete(s2, 13, axis=0)
        s2 = np.nan_to_num(s2)
        # print(s2.shape)
        all_data.append(s2.reshape(s2.shape[0], -1))
    all_data = np.concatenate(all_data, axis=1)
    print(all_data.shape)

    clean_pixels = []
    for i in range(all_data.shape[0]):
        band_data = all_data[i]
        band_data_clean = remove_outliers_percentile(band_data, 1, 99)  # 或 zscore
        # print(band_data_clean.shape)
        clean_pixels.append(band_data_clean)

    # 计算 mean 和 std
    out = []
    for i, data in enumerate(clean_pixels):
        out.append([np.mean(data), np.std(data)])
        print(f"Band {i+1}: mean={np.mean(data):.2f}, std={np.std(data):.2f}")
    
    out = np.array(out)
    out = np.transpose(out, (1, 0))
    np.savetxt("./earthquake_s2_mean_std.txt", out, fmt='%6f', delimiter=' ')
