import numpy as np
from collections import defaultdict

# 初始化字典来存储每个epoch的数据
epoch_data = defaultdict(list)

# 读取文件并提取数据
model_name = "santa/lbdv26/santa_harvey_s2_0.5_gan_2_hawaii"
model_name = "santa/ESRGAN/sr_harveylagazaturky2santa"
# model_name = "palisades/lbdv15/1_S2"
# model_name = "eaton/lbdv15/1_S2"
# model_name = "xBD_512/UANet_pvt_s2"
# model_name = "gaza/lbdv16/1_S1"
# model_name = "gaza/lbdv16/2_S2"

# model_name = "gaza/uabcd/2_S2"
# model_name = "gaza/hrsicd/1_S1"

# model_name = "santa/uabcd/2_S2"
# model_name = "santa/hrsicd/1_S1"


# model_name = "palu/hrsicd/1_S1"
# model_name = "palu/uabcd/2_S2"
# model_name = "palu/lbdv16/1_S1"
# model_name = "palu/lbdv21/palu_antakya_ka_S2"


with open(f'/mnt/d2/zxq/BDS12/result/{model_name}/val.txt', 'r') as file:
    for line in file:
        # 拆分每一行的数据
        parts = line.strip().split(', ')
        
        # 提取epoch信息
        epoch = parts[0].split('/')[0]  # 获取epoch的序号（如"2"）
        
        # 获取每个指标的值
        loss = float(parts[1].split(':')[1])
        high_f1 = float(parts[2].split(':')[1])
        pre_f1 = float(parts[3].split(':')[1])
        damage1_miou = float(parts[4].split(':')[1])
        
        # 将数据添加到对应的epoch中
        epoch_data[epoch].append((loss, high_f1, pre_f1, damage1_miou))

# 初始化统计信息
avg_loss = []
avg_high_f1 = []
avg_pre_f1 = []
avg_miou = []

# 计算每个epoch的平均值
for epoch, values in epoch_data.items():
    epoch_losses = np.array([val[0] for val in values])
    epoch_high_f1s = np.array([val[1] for val in values])
    epoch_pre_f1s = np.array([val[2] for val in values])
    epoch_mious = np.array([val[3] for val in values])
    
    # 计算当前epoch的平均值
    avg_loss.append(np.mean(epoch_losses))
    avg_high_f1.append(np.mean(epoch_high_f1s))
    avg_pre_f1.append(np.mean(epoch_pre_f1s))
    avg_miou.append(np.mean(epoch_mious))


# 输出每个epoch的平均值到新文件
print(f'/mnt/d2/zxq/BDS12/result/{model_name}/epoch_averages.txt')
with open(f'/mnt/d2/zxq/BDS12/result/{model_name}/epoch_averages.txt', 'w') as output_file:
    for i, epoch in enumerate(epoch_data.keys()):
        output_file.write(f"{epoch}/100, loss:{avg_loss[i]:.4f}, high_f1:{avg_high_f1[i]:.4f}, pre_f1:{avg_pre_f1[i]:.4f}, damage_f1:{avg_miou[i]:.4f}\n")

# 计算整个训练过程的总体平均值
# overall_avg_loss = np.max(avg_loss)
# overall_avg_f1 = np.max(avg_pre_f1)
# overall_avg_miou = np.max(avg_miou)

# 输出总体平均值
print(f"damaged max f1 {np.nanargmax(avg_miou)+1}: {np.nanmax(avg_miou):.4f}")
