"""
Building RDDM 测试脚本
用于验证模型实现的正确性
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import os
from PIL import Image

# 导入我们的模块
from building_seg_rddm import BuildingRDDM, BuildingSegmentationDataset, create_sample_data
from building_rddm_config import get_config


def test_model_creation():
    """测试模型创建"""
    print("Testing model creation...")
    
    config = get_config('quick')
    model = BuildingRDDM(
        image_size=config.image_size,
        channels=config.channels,
        dim=config.dim,
        device='cpu'  # 使用CPU进行测试
    )
    
    print(f"✓ Model created successfully")
    print(f"  - Image size: {config.image_size}")
    print(f"  - Channels: {config.channels}")
    print(f"  - Dim: {config.dim}")
    
    return model


def test_data_loading():
    """测试数据加载"""
    print("\nTesting data loading...")
    
    # 创建测试数据
    test_data_dir = './test_data'
    create_sample_data(test_data_dir, 10)
    
    # 获取数据路径
    low_seg_paths = sorted([os.path.join(test_data_dir, 'low_seg', f) 
                           for f in os.listdir(os.path.join(test_data_dir, 'low_seg'))])
    high_seg_paths = sorted([os.path.join(test_data_dir, 'high_seg', f) 
                            for f in os.listdir(os.path.join(test_data_dir, 'high_seg'))])
    
    # 创建数据集
    dataset = BuildingSegmentationDataset(
        low_seg_paths, high_seg_paths, 
        image_size=256, mode='train'
    )
    
    print(f"✓ Dataset created successfully")
    print(f"  - Dataset size: {len(dataset)}")
    
    # 测试数据加载
    sample = dataset[0]
    high_seg, low_seg = sample
    
    print(f"  - High seg shape: {high_seg.shape}")
    print(f"  - Low seg shape: {low_seg.shape}")
    print(f"  - High seg range: [{high_seg.min():.3f}, {high_seg.max():.3f}]")
    print(f"  - Low seg range: [{low_seg.min():.3f}, {low_seg.max():.3f}]")
    
    return dataset, sample


def test_forward_pass(model, sample):
    """测试前向传播"""
    print("\nTesting forward pass...")
    
    high_seg, low_seg = sample
    batch = [high_seg.unsqueeze(0), low_seg.unsqueeze(0)]  # 添加batch维度
    
    try:
        # 测试训练模式的前向传播
        model.diffusion.train()
        loss = model.diffusion(batch)
        print(f"✓ Training forward pass successful")
        print(f"  - Loss: {loss.item():.6f}")
        
        # 测试推理模式
        model.diffusion.eval()
        with torch.no_grad():
            enhanced = model.enhance_segmentation(low_seg.unsqueeze(0))
            print(f"✓ Inference forward pass successful")
            print(f"  - Enhanced shape: {enhanced.shape}")
            print(f"  - Enhanced range: [{enhanced.min():.3f}, {enhanced.max():.3f}]")
        
        return enhanced
        
    except Exception as e:
        print(f"✗ Forward pass failed: {e}")
        return None


def test_training_step(model, dataset):
    """测试训练步骤"""
    print("\nTesting training step...")
    
    # 创建数据加载器
    dataloader = torch.utils.data.DataLoader(dataset, batch_size=2, shuffle=True)
    
    # 创建优化器
    optimizer = torch.optim.Adam(model.diffusion.parameters(), lr=1e-4)
    
    try:
        model.diffusion.train()
        
        for i, batch in enumerate(dataloader):
            if i >= 2:  # 只测试2个batch
                break
                
            optimizer.zero_grad()
            loss = model.diffusion(batch)
            loss.backward()
            optimizer.step()
            
            print(f"  - Batch {i+1}: Loss = {loss.item():.6f}")
        
        print("✓ Training step successful")
        
    except Exception as e:
        print(f"✗ Training step failed: {e}")


def test_sampling(model, sample):
    """测试采样过程"""
    print("\nTesting sampling...")
    
    high_seg, low_seg = sample
    
    try:
        model.diffusion.eval()
        with torch.no_grad():
            # 测试完整采样过程
            enhanced_list = model.diffusion.sample(
                x_input=low_seg.unsqueeze(0), 
                batch_size=1, 
                last=False  # 返回所有中间步骤
            )
            
            print(f"✓ Sampling successful")
            print(f"  - Number of steps: {len(enhanced_list)}")
            print(f"  - Final result shape: {enhanced_list[-1].shape}")
            
            return enhanced_list
            
    except Exception as e:
        print(f"✗ Sampling failed: {e}")
        return None


def visualize_results(sample, enhanced, enhanced_list=None, save_path='./test_results.png'):
    """可视化结果"""
    print(f"\nVisualizing results...")
    
    high_seg, low_seg = sample
    
    if enhanced_list and len(enhanced_list) > 1:
        # 显示采样过程
        num_steps = min(5, len(enhanced_list))
        fig, axes = plt.subplots(2, num_steps + 2, figsize=(4*(num_steps+2), 8))
        
        # 第一行：输入和目标
        axes[0, 0].imshow(low_seg.squeeze(), cmap='gray')
        axes[0, 0].set_title('Low Quality Input')
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(high_seg.squeeze(), cmap='gray')
        axes[0, 1].set_title('Ground Truth')
        axes[0, 1].axis('off')
        
        # 显示采样步骤
        step_indices = np.linspace(0, len(enhanced_list)-1, num_steps, dtype=int)
        for i, step_idx in enumerate(step_indices):
            axes[0, i+2].imshow(enhanced_list[step_idx].squeeze(), cmap='gray')
            axes[0, i+2].set_title(f'Step {step_idx}')
            axes[0, i+2].axis('off')
        
        # 第二行：差异图
        for i in range(num_steps + 2):
            if i < 2:
                axes[1, i].axis('off')
            else:
                step_idx = step_indices[i-2]
                diff = torch.abs(enhanced_list[step_idx] - high_seg.unsqueeze(0))
                axes[1, i].imshow(diff.squeeze(), cmap='hot')
                axes[1, i].set_title(f'Diff Step {step_idx}')
                axes[1, i].axis('off')
    else:
        # 简单对比
        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
        
        axes[0].imshow(low_seg.squeeze(), cmap='gray')
        axes[0].set_title('Low Quality Input')
        axes[0].axis('off')
        
        if enhanced is not None:
            axes[1].imshow(enhanced.squeeze(), cmap='gray')
            axes[1].set_title('Enhanced Output')
            axes[1].axis('off')
            
            # 差异图
            diff = torch.abs(enhanced - high_seg.unsqueeze(0))
            axes[3].imshow(diff.squeeze(), cmap='hot')
            axes[3].set_title('Difference')
            axes[3].axis('off')
        
        axes[2].imshow(high_seg.squeeze(), cmap='gray')
        axes[2].set_title('Ground Truth')
        axes[2].axis('off')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✓ Results saved to {save_path}")


def test_config_loading():
    """测试配置加载"""
    print("\nTesting configuration loading...")
    
    configs = ['default', 'quick', 'high_quality', 'sentinel']
    
    for config_name in configs:
        config = get_config(config_name)
        print(f"✓ {config_name} config loaded:")
        print(f"  - Image size: {config.image_size}")
        print(f"  - Batch size: {config.batch_size}")
        print(f"  - Timesteps: {config.timesteps}")


def run_all_tests():
    """运行所有测试"""
    print("=" * 60)
    print("Building RDDM - Running All Tests")
    print("=" * 60)
    
    try:
        # 1. 测试配置
        test_config_loading()
        
        # 2. 测试模型创建
        model = test_model_creation()
        
        # 3. 测试数据加载
        dataset, sample = test_data_loading()
        
        # 4. 测试前向传播
        enhanced = test_forward_pass(model, sample)
        
        # 5. 测试训练步骤
        test_training_step(model, dataset)
        
        # 6. 测试采样
        enhanced_list = test_sampling(model, sample)
        
        # 7. 可视化结果
        visualize_results(sample, enhanced, enhanced_list)
        
        print("\n" + "=" * 60)
        print("✓ All tests completed successfully!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 运行测试
    run_all_tests()
