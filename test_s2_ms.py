import argparse
import os
import re
import torch
import torch.nn as nn
from torchvision import transforms
import torch.nn.functional as F
from tqdm import tqdm
import torch.multiprocessing
import numpy as np
import cv2
from PIL import Image
from skimage.segmentation import slic, mark_boundaries
from matplotlib import pyplot as plt
from utils.cfg import py2cfg
from pathlib import Path
from glob import glob
from osgeo import gdal, osr
from pyproj import Proj, Transformer
from scipy.ndimage import zoom
from datetime import datetime
torch.multiprocessing.set_sharing_strategy('file_system')
from skimage import measure

def classId2rgb(out, config):
    out = np.array(out).astype(np.float32)
    result = np.zeros((out.shape[0], out.shape[1], 3)).astype(np.int8)
    for i in range(len(config.label_keys)):
        result[out==i, :] = config.label_keys[i]
    # result = np.transpose(result, (2, 0, 1))
    # print(result.shape)
    result = Image.fromarray(result.astype(np.uint8))
    return result

def save_to_tif(pred_result, save_path, test_image_path):
    """
    pred_result: 需要保存的图像
    save_path: 保存路径
    test_image_path: 空间参考系
    """
    img = pred_result
    # print(np.max(img), np.min(img))

    im_geotrans, im_prof = [], []
    tif = gdal.Open(os.path.join(test_image_path))
    im_geotrans.append(tif.GetGeoTransform())
    im_prof.append(tif.GetProjection())
    if 'uint8' in img.dtype.name:
        datatype = gdal.GDT_Byte
    elif 'uint16' in img.dtype.name:
        datatype = gdal.GDT_UInt16
    else:
        datatype = gdal.GDT_Float32
    # print(img.dtype.name)
    #判读数组维数
    if len(img.shape) == 3:
        im_bands, im_height, im_width = img.shape
    else:
        im_bands, (im_height, im_width) = 1, img.shape

    #创建文件
    driver  = gdal.GetDriverByName("GTiff")                #数据类型必须有，因为要计算需要多大内存空间
    dataset = driver.Create(save_path, im_width, im_height, im_bands, datatype)
    dataset.SetGeoTransform(im_geotrans[-1])              #写入仿射变换参数
    dataset.SetProjection(im_prof[-1])                    #写入投影
    if im_bands == 1:
        dataset.GetRasterBand(1).WriteArray(img)  #写入数组数据
    else:
        for i in range(im_bands):
            dataset.GetRasterBand(i+1).WriteArray(img[i])
    del dataset
    # print('保存成功')

def load_net(model, device, model_path):
    print(model_path)
    net = model.to(device)
    net = torch.nn.DataParallel(net)
    net.load_state_dict(torch.load(model_path, map_location=device), True)
    net.eval()
    # print(net)
    return net

def load_s2_tif(s2_time_series_path: str):
    image_paths = glob(os.path.join(s2_time_series_path + "S2**.tif"))
    image_paths.sort()
    # print(s2_time_series_path, image_paths)

    image_paths = image_paths[-16:]
    s2_time_series_images = []
    for image_path in image_paths:
        dataset = gdal.Open(image_path)
        image_array = dataset.ReadAsArray()
        s2_time_series_images.append(image_array)
        x_size = dataset.RasterXSize
        y_size = dataset.RasterYSize
    s2_time_series_images = np.array(s2_time_series_images).reshape((len(image_paths), -1, y_size, x_size))
    s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)

    if "S2_post" in s2_time_series_path and "xizang" in s2_time_series_path:
        s2_time_series_images = s2_time_series_images[:, 0:3]
    elif "xizang" in s2_time_series_path:
        s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)[:, 1:4]
    return s2_time_series_images

def load_high_image_tif(path):
    dataset = gdal.Open(path)
    high_image = dataset.ReadAsArray()
    high_image = np.array(high_image)
    return high_image

def load_data(id, config, datalists):
    pre_high_image = load_high_image_tif(config.pre_high_image_paths[id])
    # print(config.pre_label_paths[id])
    pre_label = load_high_image_tif(config.pre_label_paths[id])
    C, H, W = pre_high_image.shape
    pre_s2_image = load_s2_tif(config.pre_s2_image_paths[id])
    post_s2_image = load_s2_tif(config.post_s2_image_paths[id])

    print(pre_s2_image.shape, pre_label.shape)


    pre_high_image = torch.from_numpy(np.array(pre_high_image/255., dtype=np.float32)).unsqueeze(0).to(torch.float32)
    pre_label = torch.from_numpy(np.array(pre_label, dtype=np.float32)).unsqueeze(0).to(torch.float32)

    pre_s2_image = torch.from_numpy(np.array(pre_s2_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    post_s2_image = torch.from_numpy(np.array(post_s2_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)

    pre_label = torch.where(torch.isnan(pre_label), torch.tensor(0.), pre_label)
    pre_label = torch.where(pre_label==1, torch.tensor(1.), torch.tensor(0.),)

    pre_s2_image = torch.where(torch.isnan(pre_s2_image), torch.tensor(0.), pre_s2_image)
    pre_s2_image = (pre_s2_image - config.s2_mean[None, :, None, None]) / config.s2_std[None, :, None, None]

    post_s2_image = torch.where(torch.isnan(post_s2_image), torch.tensor(0.), post_s2_image)
    post_s2_image = (post_s2_image - config.s2_mean[None, :, None, None]) / config.s2_std[None, :, None, None]

    return [pre_high_image, pre_s2_image, post_s2_image, pre_label]

def prosess_output(id, output, config, datalists, name, pre_label, post_label):
    def output2numpy(building, gray, features):
        building = torch.argmax(building, dim=1).cpu().detach().numpy().squeeze()
        gray = torch.sigmoid(gray).cpu().detach().numpy().squeeze()
        features = features
        return building, gray, features
    
    pre_high_building, pre_high_gray, pre_high_features = output2numpy(output["pre_high_building"], output["pre_high_gray"], output["pre_high_features"])
    # post_high_building, post_high_gray, post_high_features = output2numpy(output["post_high_building"], output["post_high_gray"], output["post_high_features"])
    pre_low_building, pre_low_gray, pre_low_features = output2numpy(output["pre_low_building"], output["pre_low_gray"], output["pre_low_features"])
    post_low_damage, post_low_gray, post_low_features = output2numpy(output["post_low_damage"], output["pre_low_gray"], output["post_low_features"])
    # post_low_damage, post_low_gray, post_low_features = output2numpy(output["pre_low_building"], output["pre_low_gray"], output["pre_low_features"])

    datalists["pre_high"]["out"] = pre_high_building*255
    # datalists["post_high"]["out"] = post_high_building*255
    datalists["pre_s2"]["out"] = pre_low_building*255
    datalists["post_s2"]["out"] = post_low_damage*255

    datalists["pre_high"]["gray"] = Image.fromarray(np.array(pre_high_gray*255).astype(np.int8)).convert("RGB")
    # datalists["post_high"]["gray"] = Image.fromarray(np.array(post_high_gray*255).astype(np.int8)).convert("RGB")
    datalists["pre_s2"]["gray"] = Image.fromarray(np.array(pre_low_gray*255).astype(np.int8)).convert("RGB")
    datalists["post_s2"]["gray"] = Image.fromarray(np.array(post_low_gray*255).astype(np.int8)).convert("RGB")


    # 保存结果至文件夹
    save_to_tif(pre_high_building, save_path=os.path.join(config.pre_high_label_save_path, name), test_image_path=config.pre_label_paths[id])
    # save_to_tif(post_high_building, save_path=os.path.join(config.post_high_label_save_path, name.replace("pre", "post")), test_image_path=config.pre_label_paths[id])
    save_to_tif(pre_low_building, save_path=os.path.join(config.pre_s2_label_save_path, name), test_image_path=config.pre_label_paths[id])
    save_to_tif(post_low_damage, save_path=os.path.join(config.post_s2_label_save_path, name.replace("pre", "post")), test_image_path=config.pre_label_paths[id])


def get_coordinate_from_big_image(i, j, row, col, split_withd, r, h, w):
    if i < row and j < col:
        x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
        y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd         

    #-------------------------------------------------#
    #   裁剪的图像在最后一行
    elif i == row and j < col:
        x1, x2 = h-split_withd, h,
        y1, y2 = j*(split_withd-2*r), j*(split_withd-2*r)+split_withd
    
    #-------------------------------------------------#
    #   裁剪的图像在最后一列
    elif i < row and j == col:
        x1, x2 = i*(split_withd-2*r), i*(split_withd-2*r)+split_withd
        y1, y2 = w-split_withd, w

    #-------------------------------------------------#
    #   裁剪的图像在右下角
    elif i == row and j == col:
        x1, x2 = h-split_withd, h
        y1, y2 = w-split_withd, w

    return [x1, x2, y1, y2]

def restore_coordinate_from_ij2image(i, j, row, col, split_withd, r, h, w, row_over, col_over):
    # ------------------------------------------------- #
    #   开始拼接结果
    #   第一行需要特殊考虑：行数都为 0 : split_withd-r
    if i == 0:
        # ------------------------------------------------- #
        #   左上角需要特殊考虑
        if j == 0 :
            rx1, rx2, ry1, ry2 = 0, split_withd-r, 0, split_withd-r
            px1, px2, py1, py2 = 0, split_withd-r, 0, split_withd-r

        # ------------------------------------------------- #
        #   右上角需要特殊考虑                
        elif j == col:
            rx1, rx2, ry1, ry2 = 0, split_withd-r, w - col_over, w
            px1, px2, py1, py2 = 0, split_withd-r, split_withd-col_over, split_withd

        # ------------------------------------------------- #
        #   第一行剩下的结果
        else:
            rx1, rx2, ry1, ry2 = 0, split_withd-r, j*(split_withd-2*r)+r, (j+1)*(split_withd-2*r)+r
            px1, px2, py1, py2 = 0, split_withd-r, r, split_withd-r

    # ------------------------------------------------- #
    #   最后一行需要特殊考虑，行数都为 h - row_over : h
    elif i == row:
        # ------------------------------------------------- #
        #   左下角需要特殊考虑
        if j == 0 :
            rx1, rx2, ry1, ry2 = h - row_over, h, 0, split_withd-r
            px1, px2, py1, py2 = split_withd-row_over, split_withd, 0, split_withd-r

        # ------------------------------------------------- #
        #   右下上角需要特殊考虑
        elif j == col:
            rx1, rx2, ry1, ry2 = h - row_over, h, w - col_over, w
            px1, px2, py1, py2 = split_withd-row_over, split_withd, split_withd-col_over, split_withd

        # ------------------------------------------------- #
        #   最后一行剩下的结果
        else:
            rx1, rx2, ry1, ry2 = h - row_over, h, j*(split_withd-2*r)+r, (j+1)*(split_withd-2*r)+r
            px1, px2, py1, py2 = split_withd-row_over, split_withd, r, split_withd-r

    # ------------------------------------------------- #
    #   既不是第一行，又不是最后一行
    else:
        # ------------------------------------------------- #
        #  如果在第一列, 特殊处理
        if j == 0:
            rx1, rx2, ry1, ry2 = i*(split_withd-2*r)+r, (i+1)*(split_withd-2*r)+r, 0,  split_withd-r
            px1, px2, py1, py2 = r,split_withd-r, 0,split_withd-r

        # ------------------------------------------------- #
        #  如果在最后一 列, 特殊处理
        elif j == col:
            rx1, rx2, ry1, ry2 = i*(split_withd-2*r)+r,(i+1)*(split_withd-2*r)+r, w-col_over,w
            px1, px2, py1, py2 = r,split_withd-r,  split_withd-col_over,split_withd

        # ------------------------------------------------- #
        #  其余剩下的
        else:
            rx1, rx2, ry1, ry2 = i*(split_withd-2*r)+r,(i+1)*(split_withd-2*r)+r, j*(split_withd-2*r)+r,(j+1)*(split_withd-2*r)+r
            px1, px2, py1, py2 = r, split_withd-r, r, split_withd-r

    return [rx1, rx2, ry1, ry2], [px1, px2, py1, py2]


def object_vote(loc, dam):
    damage_cls_list = [1, 2]
    local_mask = loc
    labeled_local, nums = measure.label(local_mask, connectivity=2, background=0, return_num=True)
    region_idlist = np.unique(labeled_local)
    if len(region_idlist) > 1:
        dam_mask = dam
        new_dam = local_mask.copy()
        for region_id in region_idlist:
            if all(local_mask[local_mask == region_id]) == 0:
                continue
            region_dam_count = [int(np.sum(dam_mask[labeled_local == region_id] == dam_cls_i)) * cls_weight for dam_cls_i, cls_weight in zip(damage_cls_list, [1., 5.])]
            dam_index = np.argmax(region_dam_count) + 1
            new_dam = np.where(labeled_local == region_id, dam_index, new_dam)
    else:
        new_dam = local_mask.copy()
    return new_dam.astype(np.uint8)

def test(config):
    device = torch.device(config.device)
    net = load_net(config.model, device, config.model_path)
    print(f"test datasets size: {len(config.pre_high_image_paths)}")
    
    for id, image_high_path in tqdm(enumerate(config.pre_high_image_paths)):
        datalists = {"pre_high": {}, "post_high": {}, "pre_s2": {}, "post_s2": {},}
        name = os.path.basename(image_high_path).replace(".png", ".tif")
        [pre_high_images, pre_s2_images, post_s2_images, pre_labels] = load_data(id, config, datalists)
        print(pre_high_images.shape, pre_s2_images.shape, post_s2_images.shape)
        # x = input()
        b, c, h, w = pre_high_images.shape
        split_withd = 384
        r           = 0
        row         = int((h-r*2)/(split_withd-r*2))
        col         = int((w-r*2)/(split_withd-r*2))
        row_over    = int((h - r * 2) % (split_withd - r * 2) + r)
        col_over    = int((w - r * 2) % (split_withd - r * 2) + r)
        pre_results = np.zeros((h, w))
        post_results= np.zeros((h, w))

        for i in range(row+1):
            for j in range(col+1):
                [x1, x2, y1, y2] = get_coordinate_from_big_image(i, j, row, col, split_withd, r, h, w)
                pre_high_image = pre_high_images[:, :, x1:x2, y1:y2]
                pre_label = pre_labels[:, x1:x2, y1:y2]

                pre_s2_image = pre_s2_images[:, :, :, x1//8:x2//8, y1//8:y2//8]
                post_s2_image = post_s2_images[:, :, :, x1//8:x2//8, y1//8:y2//8]

                with torch.no_grad():
                    print(i,j,pre_high_image.shape, pre_s2_image.shape, pre_label.shape)
                    outputs = net([pre_high_image, pre_s2_image, post_s2_image, pre_label, pre_label], device)
                    pre_result = outputs["pre_low_building"]
                    post_result = outputs["post_low_damage"]
                    # print(i,j,pre_result.shape, post_result.shape)
                    pre_result = torch.argmax(pre_result, dim=1).cpu().detach().numpy().squeeze()
                    # post_result = torch.softmax(post_result, dim=1)[:, 1, :, :].cpu().detach().numpy().squeeze()
                    post_result = torch.argmax(post_result, dim=1).cpu().detach().numpy().squeeze()

                    # post_result = pre_result + post_result
                    # post_result = object_vote(loc=pre_label.cpu().detach().numpy().squeeze(), dam=post_result)
                    # post_result = object_vote(loc=pre_result, dam=post_result)

                [rx1, rx2, ry1, ry2], [px1, px2, py1, py2] = restore_coordinate_from_ij2image(i, j, row, col, split_withd, r, h, w, row_over, col_over)
                pre_results[rx1:rx2, ry1:ry2] = pre_result[px1:px2, py1:py2]
                post_results[rx1:rx2, ry1:ry2] = post_result[px1:px2, py1:py2]
        save_to_tif(np.array(pre_results).astype(np.float32), save_path=os.path.join(config.pre_result_save_path, name.replace("pre", "pre")), test_image_path=config.pre_high_image_paths[id])
        save_to_tif(np.array(post_results).astype(np.float32), save_path=os.path.join(config.post_result_save_path, name.replace("pre", "post")), test_image_path=config.pre_high_image_paths[id])

        
def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument
    arg("-c", "--config_path", default="./config/all_s2_LBDv11.py", type=Path, help="Path to the config.", required=False)
    return parser.parse_args()

if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '0'
    args = get_args()
    config = py2cfg(args.config_path)
    test(config)

