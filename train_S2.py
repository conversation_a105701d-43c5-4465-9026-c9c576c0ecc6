import warnings
warnings.filterwarnings("ignore")

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.nn.parallel import DistributedDataParallel as DDP
import torch.distributed as dist
from torch.utils.data.distributed import DistributedSampler
import torch.multiprocessing
torch.multiprocessing.set_sharing_strategy('file_system')
import torchvision.transforms as T

import os
import datetime
from tqdm import tqdm
import argparse
import numpy as np
import random
from pathlib import Path
from PIL import Image
from utils.cfg import py2cfg
from glob import glob
import time
import loss.lovasz_loss as L


def seed_it(seed):
    random.seed(seed)
    os.environ["PYTHONSEED"] = str(seed)
    np.random.seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.manual_seed(seed)
seed_it(3407)

class train():
    def __init__(self, config):
        self.config     = config
        self.criterion3 = nn.CrossEntropyLoss(reduction='mean')
        self.num_gpus   = torch.cuda.device_count()
        self.device     = torch.device("cuda")
        self.grayscale_transform = T.Grayscale(num_output_channels=1)
        self.alpha = 0.5

    def DDP_setup(self, ):
        torch.distributed.init_process_group(backend='nccl')
        self.local_rank = torch.distributed.get_rank()
        torch.cuda.set_device(self.local_rank)
        self.device = torch.device("cuda", self.local_rank)

    def load_net(self,):
        net = config.model.cuda()
        if self.num_gpus > 1:
            if dist.get_rank() == 0:
                print(f"Let's use {torch.cuda.device_count()} GPUs to train and val in DDP mode")
            net = DDP(net, device_ids=[self.local_rank], output_device=self.local_rank, find_unused_parameters=True)
            net = torch.nn.SyncBatchNorm.convert_sync_batchnorm(net)
            
        if self.config.pre_train_model_path is not None:
            current_model_dict = net.state_dict()
            loaded_state_dict = torch.load(self.config.pre_train_model_path, map_location=self.device)
            new_state_dict={k:v if v.size()==current_model_dict[k].size()  else  current_model_dict[k] for k,v in zip(current_model_dict.keys(), loaded_state_dict.values())}
            net.load_state_dict(new_state_dict, strict=False)
        return net

    def load_dataset(self, ):
        # ----------------------------------------- #
        # 加载数据集
        # ----------------------------------------- #
        train_sampler = DistributedSampler(self.config.train_dataset, drop_last=True)
        val_sampler   = DistributedSampler(self.config.val_dataset, drop_last=True) # len([self.local_rank])
        
        train_loader  = DataLoader(self.config.train_dataset, batch_size=config.batch_size, shuffle=False, sampler=train_sampler, num_workers=1, pin_memory=False, collate_fn=self.config.collate_fn, drop_last=False)
        val_loader    = DataLoader(self.config.val_dataset, batch_size=config.batch_size, shuffle=False, sampler=val_sampler, num_workers=1, pin_memory=False, collate_fn=self.config.collate_fn, drop_last=False)
        
        if dist.get_rank() == 0:
            print(f"共有{len(train_loader.dataset):4d}个数据参与训练, {len(val_loader.dataset)  :4d}个数据参与验证")
        return train_loader, val_loader
    
    def _cal_loss(self, pre, lab):
        # pre = torch.squeeze(pre, dim=1)
        loss = config.criterion1(pre, lab) + 0.75 * L.lovasz_softmax(F.softmax(pre, dim=1).squeeze(dim=1), lab, ignore=255)
        return loss
    
    def _cal_kl_loss(self, s_out, t_out):
        temperature = 1.9
        t_out = F.softmax(t_out/temperature, dim=1)
        s_out = F.log_softmax(s_out/temperature, dim=1)
        kl_loss = F.kl_div(s_out, t_out, reduction = 'mean') * (temperature**2)
        return kl_loss

    def net_forward(self, epoch, inputs, net, mode, optimizer):
        config.criterion1 = config.criterion1.to(self.device)
        pre_label = inputs[-2].to(torch.long).to(self.device)
        post_label = inputs[-1].to(torch.long).to(self.device)
        # label_gray = self.grayscale_transform(inputs[0].to(torch.float32).to(self.device))
        if mode=="train": 
            outputs = net(inputs, self.device)
            high_out = outputs["pre_high_building"]
            loss = self._cal_loss(high_out, pre_label)# + config.criterion2(torch.sigmoid(outputs["pre_high_gray"]), label_gray)

            pre_out = outputs["pre_low_building"]

            # kl_loss = self._cal_kl_loss(pre_out, high_out)
            loss = loss + self._cal_loss(pre_out, pre_label)
            # loss = kl_loss * (1 - self.alpha) + ce_loss * self.alpha
            # loss = ce_loss# + config.criterion2(torch.sigmoid(outputs["pre_low_gray"]), label_gray)# + outputs["loss"]

            post_out = outputs["post_low_damage"]
            loss = loss + self._cal_loss(post_out, post_label) #

            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

        else:
            with torch.no_grad():
                outputs = net(inputs, self.device)
                high_out = outputs["pre_high_building"]
                # loss = self._cal_loss(high_out, pre_label)# .repeat(16, 1, 1)

                pre_out = outputs["pre_low_building"]
                # loss = self._cal_loss(pre_out, pre_label)

                post_out = outputs["post_low_damage"]
                loss = self._cal_loss(post_out, post_label) # loss + 

                # kl_loss = self._cal_kl_loss(pre_out, pre_out)
                # loss = self._cal_loss(pre_out, pre_label)
        # return high_out, high_out, loss
        # return pre_out, pre_out, loss
        return pre_out, post_out, loss
        # return high_out, post_out, loss
        # return post_out, post_out, loss

    def get_confusion_matrix(self, pre, lab, confusion_matrix, num_class):
        pre = torch.argmax(F.softmax(pre, dim=1), dim=1)
        # pre = F.sigmoid(pre).squeeze(dim=1)
        # pre = torch.where(pre>=0.5, 1, 0)
        mask = (lab >= 0) & (lab < num_class)
        confusion_matrix = confusion_matrix + torch.bincount(num_class * lab[mask].to(torch.int16) + pre[mask], minlength=num_class ** 2).reshape(num_class, num_class)
        return confusion_matrix

    def cal_f1(self, confusion_matrix):
        precision = torch.diag(confusion_matrix) / confusion_matrix.sum(axis = 0)
        recall    = torch.diag(confusion_matrix) / confusion_matrix.sum(axis = 1)
        f1score   = 2 * precision * recall / (precision + recall)
        return torch.mean(f1score[-1:]).item()*100.

    def train_val_function(self, epoch, data_loader, net, mode, optimizer):
        total_loss = 0
        pre_confusion_matrix = torch.zeros((config.output_building, config.output_building))
        post_confusion_matrix = torch.zeros((config.output_damaged, config.output_damaged))

        if mode=="train":
            data_loader.sampler.set_epoch(epoch)
        if dist.get_rank() == 0:
            total=int(len(data_loader.dataset)/config.batch_size/self.num_gpus)
            pbar = tqdm(total=total, desc=f"Epoch {epoch+1}/{config.epochs}", postfix=dict, mininterval=0.3, ncols=150)
        batch_id = 0
        for batch_id, inputs in enumerate(data_loader):
            pre_out, post_out, loss = self.net_forward(epoch, inputs, net, mode, optimizer)
            total_loss += float(loss.item())

            pre_confusion_matrix  = self.get_confusion_matrix(pre_out, inputs[-2].to(torch.long).to(self.device), pre_confusion_matrix.to(self.device), config.output_building)
            post_confusion_matrix = self.get_confusion_matrix(post_out, inputs[-1].to(torch.long).to(self.device), post_confusion_matrix.to(self.device), config.output_damaged)

            if dist.get_rank() == 0:
                pbar.set_postfix(**{'total_loss':total_loss/float(batch_id+1), 'pre_f1':self.cal_f1(pre_confusion_matrix),'post_f1':self.cal_f1(post_confusion_matrix)})
                pbar.update(1)
            
        if dist.get_rank() == 0:
            pbar.close()
        with open(os.path.join(self.config.save_path, mode+".txt"), "a+") as f:
            total_loss = total_loss/float(batch_id+1)
            pre_f1 = self.cal_f1(pre_confusion_matrix)
            post_f1 = self.cal_f1(post_confusion_matrix)
            f.write(f"{epoch+1}/{config.epochs}, loss:{total_loss}, pre_f1:{pre_f1}, post_f1:{post_f1}\n")
        f.close()

    def __call__(self,):
        # torch.backends.cudnn.enabled = True
        # torch.backends.cudnn.benchmark = True
        self.DDP_setup()
        start_time = datetime.datetime.now()
        net = self.load_net()

        # ----------------------------------------- #
        # 加载数据集
        # ----------------------------------------- #            
        optimizer = optim.AdamW(filter(lambda p: p.requires_grad, net.parameters()), lr=config.lr, weight_decay=0.0005)
        train_loader, val_loader = self.load_dataset()

        for epoch in range(config.epochs):
            net.train()
            self.train_val_function(epoch, train_loader, net, "train", optimizer)

            net.eval()
            self.train_val_function(epoch, val_loader, net, "val", optimizer)

            if (epoch+1)%1==0 or (config.epochs - epoch)<5:
                torch.save(net.state_dict(), os.path.join(str(self.config.save_path), str(epoch+1) + ".pt"))
            torch.cuda.empty_cache()
                
        # ----------------------------------------- #
        # 计算训练时间
        # ----------------------------------------- #            
        end_time   = datetime.datetime.now()
        total_time = end_time - start_time
        
        # ----------------------------------------- #
        # 保存训练文件
        # ----------------------------------------- #
        with open(os.path.join(self.config.save_path, "time.txt"), "a+") as f:
            f.write(f"start_time:{start_time}, end_time:{end_time}, total_time:{total_time}")
        f.close()

def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument
    arg("--config_path", default="./config/S2_HRNet.py", type=Path, help="Path to the config.", required=False)
    # parser.add_argument("--cfg", type=str, default='./changedetection/configs/vssm1/vssm_base_224.yaml', required=False)
    return parser.parse_args()


if __name__ == "__main__":
    args = get_args()
    config = py2cfg(args.config_path)
    train_function = train(config)
    train_function()
