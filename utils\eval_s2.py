import enum
import os
import numpy as np
from PIL import Image
from tqdm import tqdm
import warnings
from glob import glob
warnings.filterwarnings("ignore")

def safe_divide(a, b):
    return a / b if b != 0 else 0

def binary_eval(base_path, model_name, mode, resolution):
    city_name = "" # Rubizhne Mariupol Haiti Nepal Yushu left right santa harvey
    test_names = glob(os.path.join(base_path, model_name, resolution, f"*{city_name}**.tif"))
    test_names.sort()
    # print(len(test_names), test_names)
    count = 0
    TP, FP, FN, TN = 0, 0, 0, 0
    labs, pres = [], []
    for id, test_name in tqdm(enumerate(test_names)):
        # if "_2.tif" in test_name or "disaster_2" in test_name:
        #     print(test_name)
        #     continue
        name = os.path.basename(test_name)
        lab_path = os.path.join(base_path, mode, name)
        # print(lab_path)
        pre_path = os.path.join(base_path, model_name, resolution, name)
        lab = np.array(Image.open(lab_path)).astype(np.float32)
        if mode=="post_label" and ("/santa/" in test_name or "/palu/" in test_name):
            lab = np.where(lab>=3, 1, 0)
        # if mode=="post_label" and ("/palisades/" in test_name or "/eaton/" in test_name):
            # print(True)
            # lab = np.where(lab>=1, 1, 0)
            # lab = np.where(lab>=5, 0, lab)
            # lab = np.where(lab>=0.1, 1, 0)
            # lab = np.where(lab==1, 1, 0)
            # print(np.max(lab), np.min(lab))
        # if mode=="post_label" and ("/gaza/" in test_name):
        #     lab = np.where(lab>=2, 1, 0)

        # if mode=="post_label" and ("kahramanmara-" in test_name): #  
        #     lab = np.where((lab >= 2) & (lab <= 5), 1, 0)#.astype(np.int8)
            # print(True)

        # palu_cloud_names = [f"palu-tsunami_{str(id).zfill(8)}_post" for id in [3, 4, 6, 7, 8, 12, 16, 18, 21, 32, 41, 96, 117, 120, 143, 192, 194]]
        # if name.split("_disaster")[0] in palu_cloud_names:
        #     continue
        # if "/palu-" in test_name or "/antakya-" in test_name: #   islahiye  or "/kahramanmara-" in test_name antakya
        #     continue


        if mode=="post_label" and "hawaii-" in test_name:
            lab = np.where(lab>=3, 1, 0)

        lab = np.where(lab>=0.5, 1, 0)
        pre = np.array(Image.open(pre_path))
        pre = np.where(pre>=0.5, 1, 0)
        c = 1
        TP += np.logical_and(pre == c, lab == c).sum()
        FN += np.logical_and(pre != c, lab == c).sum()
        FP += np.logical_and(pre == c, lab != c).sum()
        TN += np.logical_and(pre != c, lab != c).sum()
        count += 1

    IOU = TP / (TP + FP + FN)
    Recall = TP / (TP + FN)
    Precision = TP / (TP + FP)
    F1 = 2 * Recall * Precision / (Precision + Recall)
    
    OA = (TP + TN) / (TP + FP + FN + TN)
    # FPR = safe_divide(FP, FP + TN) # 错误地把负类判断为正类的概率

    # kappa = cohen_kappa_score(np.array(labs).reshape(-1, 1), np.array(pres).reshape(-1, 1))
    print(count, F1, IOU, Recall, Precision, OA)

if __name__ == "__main__":

    # mode_name = "lbdv3/3_S1"
    # mode_name = "lbdv14/2_S2"

    # mode_name = "lbdv15/3_S2/santa2palisades"
    # mode_name = "lbdv20/santa2palisades_pvtdiff_pseudo/santa2palisades_pvtdiff_pseudo"
    # mode_name = "lbdv15/santa2palisades/santa2palisades"

    # mode_name = "uabcd/2_S2"
    # mode_name = "uabcd/1_S1"
    # mode_name = "lbdv16/1_S1"

    # mode_name = "hrsicd/1_S1"
    # mode_name = "hrsicd/2_S2"

    # mode_name = "lbdv16/1_S1"
    # mode_name = "lbdv16/2_S2"

    # mode_name = "lbdv21/palu_antakya_ka_S2"
    # mode_name = "lbdv21/santa_LA2hawaii/s1_santa_LA2hawaii"
    # mode_name = "lbdv21/santa_LA_hawaii2palu"

    # mode_name = "lbdv26/santa_s2_0.5_clip_grey"
    # mode_name = "lbdv22/s2_santa_to_hawaii_psuedo/s2_santa_to_hawaii_psuedo"

    mode_name = "lbdv26/santa_harvey_s2_0.5_gan_2_hawaii"
    mode_name = "ESRGAN/sr_harveyla2santa"

    # 测试santa-S2数据
    # binary_eval(base_path="./result/santa", model_name="hrnet/1_S2", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/santa", model_name="hrnet/1_S2", mode="pre_label", resolution="pre_s2_result")
    # binary_eval(base_path="./result/santa", model_name="hrnet/1_S2", mode="post_label", resolution="post_s2_result")

    # binary_eval(base_path="./result/santa", model_name=f"{mode_name}", mode="pre_label", resolution="pre_high_result")
    binary_eval(base_path="./result/santa", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s2_result")
    # binary_eval(base_path="./result/santa", model_name=f"{mode_name}", mode="post_label", resolution="post_s2_result")


    # 测试santa-S1数据
    # binary_eval(base_path="./result/santa", model_name=f"{mode_name}", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/santa", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s1_result")
    # binary_eval(base_path="./result/santa", model_name=f"{mode_name}", mode="post_label", resolution="post_s1_result")


    # 测试palisades-S2数据
    # binary_eval(base_path="./result/palisades", model_name=f"{mode_name}", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/palisades", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s2_result")
    # binary_eval(base_path="./result/palisades", model_name=f"{mode_name}", mode="post_label", resolution="post_s2_result")


    # 测试palisades-S1数据
    # binary_eval(base_path="./result/palisades", model_name="hrnet/2_S1", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/palisades", model_name="hrnet/2_S1", mode="pre_label", resolution="pre_s2_result")
    # binary_eval(base_path="./result/palisades", model_name="hrnet/2_S1", mode="post_label", resolution="post_s2_result")


    # 测试eaton-S2数据
    # binary_eval(base_path="./result/eaton", model_name=f"{mode_name}", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/eaton", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s2_result")
    # binary_eval(base_path="./result/eaton", model_name=f"{mode_name}", mode="post_label", resolution="post_s2_result")

    # 测试eaton-S1数据
    # binary_eval(base_path="./result/eaton", model_name="hrnet/2_S1", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/eaton", model_name="hrnet/2_S1", mode="pre_label", resolution="pre_s1_result")
    # binary_eval(base_path="./result/eaton", model_name="hrnet/2_S1", mode="post_label", resolution="post_s1_result")
    
    # Gaza数据
    # binary_eval(base_path="./result/palisades", model_name=f"{mode_name}", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/gaza", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s2_result")
    # binary_eval(base_path="./result/gaza", model_name=f"{mode_name}", mode="post_label", resolution="post_s2_result")

    # binary_eval(base_path="./result/palisades", model_name=f"{mode_name}", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/gaza", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s1_result")
    # binary_eval(base_path="./result/gaza", model_name=f"{mode_name}", mode="post_label", resolution="post_s1_result")


    # palu
    # binary_eval(base_path="./result/palu", model_name=f"{mode_name}", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/palu", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s2_result")
    # binary_eval(base_path="./result/palu", model_name=f"{mode_name}", mode="post_label", resolution="post_s2_result")


    # binary_eval(base_path="./result/palu", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s1_result")
    # binary_eval(base_path="./result/palu", model_name=f"{mode_name}", mode="post_label", resolution="post_s1_result")


    # hawaii
    # binary_eval(base_path="./result/hawaii", model_name=f"{mode_name}", mode="pre_label", resolution="pre_high_result")
    # binary_eval(base_path="./result/hawaii", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s2_result")
    # binary_eval(base_path="./result/hawaii", model_name=f"{mode_name}", mode="post_label", resolution="post_s2_result")

    # binary_eval(base_path="./result/hawaii", model_name=f"{mode_name}", mode="pre_label", resolution="pre_s1_result")
    # binary_eval(base_path="./result/hawaii", model_name=f"{mode_name}", mode="post_label", resolution="post_s1_result")
