import os
import numpy as np
from osgeo import gdal
import matplotlib.pyplot as plt
from tqdm import tqdm
from glob import glob
from scipy.stats import norm

def load_high_image(image_path):
    dataset = gdal.Open(image_path)
    return dataset.ReadAsArray()

def load_label(label_path):
    label = gdal.Open(label_path).ReadAsArray()
    return np.where(label > 0, 1, 0).astype(np.uint8)

def load_S2_image(image_path):
    s2_image_paths = glob(os.path.join(image_path +  "_S2**.tif"))
    s2_image_paths.sort()
    if "S2_post" in image_path:
        s2_image_paths = s2_image_paths[-1:]
    # print(s2_image_paths, image_path)

    s2_time_series_images = []
    for s2_image_path in s2_image_paths:
        dataset = gdal.Open(s2_image_path)
        image_array = dataset.ReadAsArray()
        x_size = dataset.RasterXSize
        y_size = dataset.RasterYSize
        s2_time_series_images.append(image_array)
    s2_time_series_images = np.array(s2_time_series_images).reshape((len(s2_image_paths), -1, y_size, x_size))
    s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)
    s2_time_series_images = np.nan_to_num(s2_time_series_images)
    # s2_time_series_images = np.where(s2_time_series_images == None, 0, s2_time_series_images)

    return s2_time_series_images


def collect_masked_pixels(image_dir):
    image_lists = glob(os.path.join(image_dir, "hold/images/*pre*.tif"))
    image_lists+= glob(os.path.join(image_dir, "test/images/*pre*.tif"))
    image_lists+= glob(os.path.join(image_dir, "train/images/*pre*.tif"))

    image_lists.sort()
    all_masked_pixels = [[], [], []]  # R, G, B

    for image_list in tqdm(image_lists, desc=f"Processing {os.path.basename(image_dir)}"):
        image_path = image_list
        label_path = image_list.replace("/images/", "/labels/")

        if not os.path.exists(label_path):
            continue

        rgb = load_high_image(image_path)
        mask = load_label(label_path)
        # print(image_path, rgb.shape, mask.shape)

        for i in range(3):
            masked = rgb[i][mask == 1]
            if masked.size > 0:
                all_masked_pixels[i].append(masked)

    return [np.concatenate(pix) for pix in all_masked_pixels]

def get_high_label_boxplot():
    # 路径设置
    santa_image_dir = "/mnt/d2/zxq/BDS12/dataset/santa/"
    palis_image_dir = "/mnt/d2/zxq/BDS12/dataset/palisades/"
    eaton_image_dir = "/mnt/d2/zxq/BDS12/dataset/eaton/"

    # 提取数据
    santa_pixels = collect_masked_pixels(santa_image_dir)
    palis_pixels = collect_masked_pixels(palis_image_dir)
    eaton_pixels = collect_masked_pixels(eaton_image_dir)

    # 绘制箱线图
    plt.figure(figsize=(10, 6))

    # 定位每个箱线图的位置
    positions = [1, 2, 3, 5, 6, 7, 9, 10, 11]  # 1-3: Red, 5-7: Green, 9-11: Blue
    data = [santa_pixels[0], palis_pixels[0], eaton_pixels[0],
            santa_pixels[1], palis_pixels[1], eaton_pixels[1],
            santa_pixels[2], palis_pixels[2], eaton_pixels[2]]

    colors = ['skyblue', 'orange', 'lightgreen'] * 3

    plt.figure(figsize=(12, 6))
    box = plt.boxplot(data, positions=positions, widths=0.6, patch_artist=True)

    for patch, color in zip(box['boxes'], colors):
        patch.set_facecolor(color)

    plt.xticks([2, 6, 10], ['Red', 'Green', 'Blue'])
    plt.legend([box["boxes"][0], box["boxes"][1], box["boxes"][2]],
            ['Santa', 'Palisades', 'Eaton'], loc='upper right')
    plt.title("Comparison of RGB Pixel Distributions in Building Regions")
    plt.ylabel("Pixel Intensity")
    plt.grid(True)
    plt.tight_layout()
    plt.show()
    plt.savefig("./boxplot.png")

def get_s2_label_boxplot(image_dir):
    image_lists = glob(os.path.join(image_dir, "hold/images/*pre*.tif"))
    image_lists+= glob(os.path.join(image_dir, "test/images/*pre*.tif"))
    image_lists+= glob(os.path.join(image_dir, "train/images/*pre*.tif"))

    image_lists.sort()
    all_masked_pixels = [[] for _ in range(17)]  # RGB波段

    for image_list in tqdm(image_lists, desc=f"Processing {os.path.basename(image_dir)}"):
        image_name = image_list.split(".tif")[0]
        pre_s2 = load_S2_image(image_name.replace("/images/", "/S2/"))
        post_s2 = load_S2_image(image_name.replace("/images/", "/S2_post/").replace("pre", "post"))

        mask = load_label(image_list.replace("/images/", "/labels/"))
        mask = np.resize(mask, new_shape=(48, 48))
        # print(image_list, pre_s2.shape, post_s2.shape, mask.shape, np.max(pre_s2[:, 4,]), np.max(pre_s2[:, 8,]))

        # mask = np.expand_dims(mask, axis=0).repeat(16, axis=0)
        # for i in range(17):
        #     # if i==4 or i== 8:
        #     #     print(i,pre_s2[:, i][mask == 1], np.max(pre_s2[:, i][mask == 1]), np.min(pre_s2[:, i][mask == 1]))
                
        #     masked = pre_s2[:, i][mask == 1]
        #     if masked.size > 0:
        #         all_masked_pixels[i].append(masked)
        # break

        for i in range(17):
            masked = post_s2[0, i][mask == 1]
            if masked.size > 0:
                all_masked_pixels[i].append(masked)
    return [np.concatenate(pix) for pix in all_masked_pixels]
    # data = np.nan_to_num(data)
    # return data
    # return

santa_pixels = get_s2_label_boxplot("/mnt/d2/zxq/BDS12/dataset/santa/")
palis_pixels = get_s2_label_boxplot("/mnt/d2/zxq/BDS12/dataset/palisades/")
eaton_pixels = get_s2_label_boxplot("/mnt/d2/zxq/BDS12/dataset/eaton/")


plt.figure(figsize=(16, 10))

# 绘制每个波段的箱线图
# for i in range(2):
#     plt.subplot(4, 5, i+1)  # 4行5列的子图布局
#     data = palis_pixels[i]  # 当前波段的像素数据
#     print(data.shape, np.max(data), np.min(data))
#     plt.boxplot(data, patch_artist=True, boxprops=dict(facecolor='skyblue'))
#     plt.title(f'Band {i+1}')
#     plt.ylabel('Pixel Intensity')

# plt.tight_layout()
# plt.show()
# plt.savefig("./boxplot_all_bands_palisades2.png")


# 创建箱线图
# plt.figure(figsize=(16, 10))

# # 绘制每个波段的箱线图
# for i in range(17):
#     plt.subplot(4, 5, i+1)  # 4行5列的子图布局
    
#     # 合并三组数据 (santa, palisades, eaton)
#     data = [santa_pixels[i], palis_pixels[i], eaton_pixels[i]]
    
#     # 绘制箱线图
#     box = plt.boxplot(data, patch_artist=True, boxprops=dict(facecolor='skyblue'))

#     # 设置不同颜色
#     colors = ['skyblue', 'orange', 'lightgreen']
#     for j, patch in enumerate(box['boxes']):
#         patch.set_facecolor(colors[j])

#     plt.title(f'Band {i+1}')
#     plt.ylabel('Pixel Intensity')

# plt.tight_layout()
# plt.show()
# plt.savefig("./boxplot_all_bands_combined_pre.png")

# 正态分布
# for i in range(17):  # 假设你有17个波段
#     plt.subplot(4, 5, i+1)  # 4行5列的子图布局
#     data = santa_pixels[i]  # 当前波段的像素数据
    
#     # 绘制当前波段的直方图，density=True 使得直方图的面积为1
#     plt.hist(data, bins=50, density=True, alpha=0.6, color='skyblue', label='Pixel Distribution')
    
#     # 拟合正态分布
#     mu, std = norm.fit(data)  # 计算数据的均值和标准差
#     xmin, xmax = plt.xlim()  # 获取当前x轴的范围
#     x = np.linspace(xmin, xmax, 100)
#     p = norm.pdf(x, mu, std)  # 计算正态分布的概率密度函数
#     plt.plot(x, p, 'k', linewidth=2, label=f'Fit: $\mu={mu:.2f}, \sigma={std:.2f}$')
    
#     # 设置标题、标签
#     plt.title(f'Band {i+1}')
#     plt.xlabel('Pixel Intensity')
#     plt.ylabel('Density')
#     plt.legend()

# # 调整布局
# plt.tight_layout()

# # 显示和保存图像
# plt.show()
# plt.savefig("./santa_normal_distribution_bands.png")

# 创建一个图形，大小为16x10
plt.figure(figsize=(32, 20))

# 遍历每个波段并绘制直方图和正态分布拟合曲线
for i in range(17):  # 假设你有17个波段
    plt.subplot(4, 5, i+1)  # 4行5列的子图布局
    
    # 绘制每个数据集的正态分布
    data_santa = santa_pixels[i]  # Santa 数据集当前波段的像素数据
    data_palis = palis_pixels[i]  # Palisades 数据集当前波段的像素数据
    data_eaton = eaton_pixels[i]  # Eaton 数据集当前波段的像素数据
    
    # 绘制 Santa 数据集的直方图
    plt.hist(data_santa, bins=50, density=True, alpha=0.6, color='skyblue', label='Santa')
    
    # 绘制 Palisades 数据集的直方图
    plt.hist(data_palis, bins=50, density=True, alpha=0.6, color='orange', label='Palisades')
    
    # 绘制 Eaton 数据集的直方图
    plt.hist(data_eaton, bins=50, density=True, alpha=0.6, color='lightgreen', label='Eaton')

    # 为 Santa 数据集拟合正态分布
    mu_santa, std_santa = norm.fit(data_santa)
    xmin, xmax = plt.xlim()
    x = np.linspace(xmin, xmax, 100)
    p_santa = norm.pdf(x, mu_santa, std_santa)
    plt.plot(x, p_santa, 'b', linewidth=2, label=f'Santa Fit: $\mu={mu_santa:.2f}, \sigma={std_santa:.2f}$')

    # 为 Palisades 数据集拟合正态分布
    mu_palis, std_palis = norm.fit(data_palis)
    p_palis = norm.pdf(x, mu_palis, std_palis)
    plt.plot(x, p_palis, 'orange', linewidth=2, label=f'Palisades Fit: $\mu={mu_palis:.2f}, \sigma={std_palis:.2f}$')

    # 为 Eaton 数据集拟合正态分布
    mu_eaton, std_eaton = norm.fit(data_eaton)
    p_eaton = norm.pdf(x, mu_eaton, std_eaton)
    plt.plot(x, p_eaton, 'g', linewidth=2, label=f'Eaton Fit: $\mu={mu_eaton:.2f}, \sigma={std_eaton:.2f}$')

    # 设置标题、标签
    plt.title(f'Band {i+1}')
    plt.xlabel('Pixel Intensity')
    plt.ylabel('Density')
    plt.legend()

# 调整布局
plt.tight_layout()

# 显示和保存图像
plt.show()
plt.savefig("./normal_distribution_all_bands_Post.png")