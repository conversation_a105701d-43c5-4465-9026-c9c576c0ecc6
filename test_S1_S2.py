import argparse
import os
import re
import torch
import torch.nn as nn
from torchvision import transforms
import torch.nn.functional as F
from tqdm import tqdm
import torch.multiprocessing
import numpy as np
import cv2
from PIL import Image
from skimage.segmentation import slic, mark_boundaries
from matplotlib import pyplot as plt
from utils.cfg import py2cfg
from pathlib import Path
from glob import glob
from osgeo import gdal, osr
from pyproj import Proj, Transformer
from scipy.ndimage import zoom
from datetime import datetime
torch.multiprocessing.set_sharing_strategy('file_system')

def classId2rgb(out, config):
    out = np.array(out).astype(np.float32)
    result = np.zeros((out.shape[0], out.shape[1], 3)).astype(np.int8)
    for i in range(len(config.label_keys)):
        result[out==i, :] = config.label_keys[i]
    # result = np.transpose(result, (2, 0, 1))
    # print(result.shape)
    result = Image.fromarray(result.astype(np.uint8))
    return result

def save_to_tif(pred_result, save_path, test_image_path):
    """
    pred_result: 需要保存的图像
    save_path: 保存路径
    test_image_path: 空间参考系
    """
    img = pred_result
    # print(np.max(img), np.min(img))

    im_geotrans, im_prof = [], []
    tif = gdal.Open(os.path.join(test_image_path))
    im_geotrans.append(tif.GetGeoTransform())
    im_prof.append(tif.GetProjection())
    if 'uint8' in img.dtype.name:
        datatype = gdal.GDT_Byte
    elif 'uint16' in img.dtype.name:
        datatype = gdal.GDT_UInt16
    else:
        datatype = gdal.GDT_Float32
    # print(img.dtype.name)
    #判读数组维数
    if len(img.shape) == 3:
        im_bands, im_height, im_width = img.shape
    else:
        im_bands, (im_height, im_width) = 1, img.shape

    #创建文件
    driver  = gdal.GetDriverByName("GTiff")                #数据类型必须有，因为要计算需要多大内存空间
    dataset = driver.Create(save_path, im_width, im_height, im_bands, datatype)
    dataset.SetGeoTransform(im_geotrans[-1])              #写入仿射变换参数
    dataset.SetProjection(im_prof[-1])                    #写入投影
    if im_bands == 1:
        dataset.GetRasterBand(1).WriteArray(img)  #写入数组数据
    else:
        for i in range(im_bands):
            dataset.GetRasterBand(i+1).WriteArray(img[i])
    del dataset
    # print('保存成功')

def get_tenser_mean(img_tensor, val_max=None, val_min=None):
    img_tensor = F.interpolate(img_tensor, size=(384, 384), mode="bilinear")
    img_tensor = torch.mean(img_tensor, dim=1).cpu().detach().numpy().squeeze()
    # if val_max==None:
    #     val_max, val_min = np.max(img_tensor), np.min(img_tensor)
    # img_tensor = ((((img_tensor - val_min)/(val_max-val_min)))*255).astype(np.uint8)
    return img_tensor

def plot_img(datalists, save_path, name, f1=None):

    pre_high_datalists = datalists["pre_high"]
    pre_s2_datalists = datalists["pre_s2"]
    post_s2_datalists = datalists["post_s2"]

    col = len(pre_high_datalists)
    row = 4
    keys_list = list(datalists.keys())
    fig = plt.figure(figsize=(8, 8))
    for id, datalists in enumerate([pre_high_datalists, pre_s2_datalists, post_s2_datalists]): # 
        # 针对灾前画图
        keys_list = list(datalists.keys())
        for keys_id in range(len(keys_list)):
            plt.subplot(row, col, id*col + keys_id+1)
            plt.axis('off')
            if datalists[keys_list[keys_id]] is None:
                continue
            plt.imshow(datalists[keys_list[keys_id]], cmap = plt.cm.jet)
            if keys_list[keys_id]=="out":
                plt.title(f"{keys_list[keys_id]}:{f1[id]}")
            else:
                plt.title(keys_list[keys_id])

    plt.savefig(os.path.join(save_path, name))
    plt.cla()
    plt.close("all")


def load_net(model, device, model_path):
    print(model_path)
    net = model.to(device)
    net = torch.nn.DataParallel(net)
    net.load_state_dict(torch.load(model_path, map_location=device), True)
    net.eval()
    # print(net)  
    return net

def add_lon_lat_band(gdal_dataset, x_size, y_size):
    image_array = gdal_dataset.ReadAsArray()
    # 获取UTM的投影信息
    source_srs = osr.SpatialReference()
    source_srs.ImportFromWkt(gdal_dataset.GetProjectionRef())

    # 创建WGS84的目标投影信息
    target_srs = osr.SpatialReference()
    target_srs.ImportFromEPSG(4326)  # WGS84坐标系

    # 增加经纬度
    geotransform = gdal_dataset.GetGeoTransform()
    # print(geotransform)
    # 创建坐标转换对象
    transformer = osr.CoordinateTransformation(source_srs, target_srs)

    lon = np.linspace(geotransform[0], geotransform[0] + geotransform[1] * x_size, x_size)
    lat = np.linspace(geotransform[3] + geotransform[5] * y_size, geotransform[3], y_size)
    # 创建坐标点的二维数组
    lon_lat_points = np.array([[l, la] for la in lat for l in lon])

    # 进行坐标转换
    transformed_points = transformer.TransformPoints(lon_lat_points)

    # 分离转换后的经纬度
    lon = np.array([point[0] for point in transformed_points]).reshape(1, x_size, y_size)
    lat = np.array([point[1] for point in transformed_points]).reshape(1, x_size, y_size)
    # print(lon.shape, lat.shape)
    # x = input()
    image_array = np.concatenate((image_array, lon, lat), axis=0)
    return image_array

def add_time_band(image_array, x_size, y_size, high_time_str, low_time_str):
    high_time = datetime.strptime(high_time_str, "%Y%m%d")
    low_time = datetime.strptime(low_time_str, "%Y%m%d")
    time_difference = (low_time - high_time).days
    normalized_difference = time_difference / (2*365)
    expanded_array = np.full((1, x_size, y_size), normalized_difference)
    image_array = np.concatenate((image_array, expanded_array), axis=0)
    # print(f"high_time:{high_time}, low_time:{low_time}, time_difference{time_difference}, normalized_difference:{normalized_difference}")
    # x = input()   
    return image_array

def load_s2_tif(s2_time_series_path: str):
    # return torch.zeros((1, 1, 1, 48, 48))
    image_paths = glob(os.path.join(s2_time_series_path + "_S2_**.tif"))
    image_paths.sort()
    image_paths = image_paths[-16:]
    s2_time_series_images = []
    for image_path in image_paths:
        dataset = gdal.Open(image_path)
        image_array = dataset.ReadAsArray()
        x_size = dataset.RasterXSize
        y_size = dataset.RasterYSize 
        s2_time_series_images.append(image_array)
    s2_time_series_images = np.array(s2_time_series_images).reshape((len(image_paths), -1, y_size, x_size))
    s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)
    return s2_time_series_images

def load_s1_tif(s1_time_series_path: str):
    image_paths = glob(os.path.join(s1_time_series_path + "_S1_**.tif"))
    image_paths.sort()
    s1_time_series_images = []
    for image_path in image_paths:
        dataset = gdal.Open(image_path)
        image_array = dataset.ReadAsArray()
        s1_time_series_images.append(image_array)
    s1_time_series_images = np.array(s1_time_series_images).reshape((len(image_paths), -1, 48, 48))
    # print(image_paths, len(image_paths), s1_time_series_images.shape)
    return s1_time_series_images[-1:, :2, :, :]
    # return s1_time_series_images#[-1:, :2, :, :]
    # return s1_time_series_images[:, :2, :, :]

def load_S1_C2_image(image_path, mode="S1_C2"):
    image_paths = glob(os.path.join(image_path.replace("/S1/", f"/{mode}/") +  f"_{mode}_**.tif"))
    image_paths.sort()
    image_paths = image_paths[-2:]

    time_series_images = []
    for image_path in image_paths:
        dataset = gdal.Open(image_path)
        image_array = dataset.ReadAsArray()

        time_series_images.append(image_array)
    time_series_images = np.array(time_series_images).reshape((len(image_paths), -1, 48, 48))

    # 数据归一化
    time_series_images = torch.from_numpy(np.array(time_series_images, dtype=np.float32))
    time_series_images = torch.where(time_series_images<=-25, -25, time_series_images)
    time_series_images = torch.where(time_series_images>= 25,  25, time_series_images)
    if mode=="S1_CohIfg":
        T, C, H, W = time_series_images.shape
        for t_id in range(T):
            time_series_images[t_id] = torch.where(time_series_images[t_id]>time_series_images[-2], time_series_images[-2], time_series_images[t_id])
            # print("Ok")
    return time_series_images

def load_high_image_tif(path):
    dataset = gdal.Open(path)
    high_image = dataset.ReadAsArray()
    high_image = np.array(high_image)
    return high_image

def sentinel22rgb(data):
    for i in range(3):
        data[i, :, :] = (data[i, :, :] - np.nanmin(data[i, :, :])) / (np.nanmax(data[i, :, :]) - np.nanmin(data[i, :, :])) * 255
    data = np.nan_to_num(data, nan=0.0)
    data = zoom(data, (1, 8, 8), order=3)
    data = np.where(data<0, 0, data)
    data = np.where(data>255, 0, data)
    return data

def sentinel12gray(data):
    data = (data - np.nanmin(data)) / (np.nanmax(data) - np.nanmin(data)) * 255
    data = np.nan_to_num(data, nan=0.0)
    data = np.where(data<0, 0, data)
    data = np.where(data>255, 0, data)
    return data

def load_data(id, config, name, datalists):
    pre_high_image = load_high_image_tif(config.pre_high_image_paths[id])
    # post_high_image = load_high_image_tif(config.post_high_image_paths[id])

    pre_s2_image = load_s2_tif(config.pre_s2_image_paths[id])
    post_s2_image = load_s2_tif(config.post_s2_image_paths[id])

    if "gaza" in config.pre_s2_image_paths[id]:
        pre_post_s1_images = load_s1_tif(config.pre_s1_image_paths[id])
        pre_s1_image = pre_post_s1_images[-2:-1]
        post_s1_image = pre_post_s1_images[-1:]
    else:
        pre_s1_image = load_s1_tif(config.pre_s1_image_paths[id])
        post_s1_image = load_s1_tif(config.post_s1_image_paths[id])

    pre_label = np.array(Image.open(config.pre_label_paths[id]))
    post_label = np.array(Image.open(config.post_label_paths[id]))

    if "gaza" in config.pre_s2_image_paths[id]:
        pre_label = np.where(pre_label>=1, 1, 0)
        post_label = np.where(post_label>=2, 1, 0).astype(np.float32)
    else:
        pre_label = np.where(pre_label>=1, 1, 0)
        post_label = np.where(post_label>=5, 0, post_label).astype(np.float32)

    # print(np.array(pre_high_image).shape, pre_s2_image.shape, post_s2_image.shape, np.array(pre_s1_image).shape, np.array(post_s1_image).shape, np.array(pre_label).shape, np.array(post_label).shape)
    # print(np.max(post_label), np.min(post_label))

    # save_to_tif(pre_high_image.astype(np.uint8), os.path.join(config.pre_high_image_save_path, name), config.pre_high_image_paths[id])
    # save_to_tif(post_high_image.astype(np.uint8), os.path.join(config.post_high_image_save_path, name.replace("pre", "post")), config.post_high_image_paths[id])

    Image.fromarray((np.array(pre_label*255)).astype(np.uint8)).save(os.path.join(config.pre_label_save_path, name))
    Image.fromarray((np.array(post_label)).astype(np.float32)).save(os.path.join(config.post_label_save_path, name.replace("pre", "post")))


    # =================================================================== #
    # 保存输入数据到文件夹
    pre_s1_gray = sentinel12gray(pre_s1_image[-1, 0, :, :])
    post_s1_gray = sentinel12gray(post_s1_image[-1, 0, :, :])

    datalists = {"pre_high": {}, "post_high": {}, "pre_s2": {}, "post_s2": {},}
    datalists["pre_high"]["image"] = Image.fromarray((np.transpose(np.array(pre_high_image), (1, 2, 0))).astype(np.uint8))
    datalists["pre_s2"]["image"] =  Image.fromarray(pre_s1_gray.astype(np.uint8)).convert('L').convert("RGB") #Image.fromarray((np.transpose(np.array(pre_s2_rgb), (1, 2, 0))).astype(np.uint8))
    datalists["post_s2"]["image"] =  Image.fromarray(post_s1_gray.astype(np.uint8)).convert('L').convert("RGB") #Image.fromarray((np.transpose(np.array(post_s2_rgb), (1, 2, 0))).astype(np.uint8))

    datalists["pre_high"]["label"] = pre_label*255
    datalists["pre_s2"]["label"] = pre_label*255
    datalists["post_s2"]["label"] = classId2rgb(post_label, config)

    # 需要进一步处理灾后数据，以计算F1分数
    if "santa-" in name or "palu-" in name:
        pre_label = np.where((pre_label >= 1) & (pre_label <= 5), 1, 0)
        post_label = np.where(post_label>=5, 0, post_label)
        post_label = np.where(post_label>=3, 1, 0).astype(np.int8) # 临时放置
    elif "palisades-" in name or "eaton-" in name:
        pre_label = np.where(pre_label==1, 1, 0)
        post_label = np.where(post_label==1, 1, 0)
    elif "hawaii-" in name or "antakya-" in name or "kahramanmara-" in name:
        pre_label = np.where(pre_label>=1, 1, 0)
        post_label = np.where(post_label>=2, 1, 0)


    pre_high_image = torch.from_numpy(np.array(pre_high_image/255., dtype=np.float32)).unsqueeze(0).to(torch.float32)
    # post_high_image = torch.from_numpy(np.array(post_high_image/255., dtype=np.float32)).unsqueeze(0).to(torch.float32)
    pre_s2_image = torch.from_numpy(np.array(pre_s2_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    post_s2_image = torch.from_numpy(np.array(post_s2_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    pre_s1_image = torch.from_numpy(np.array(pre_s1_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    post_s1_image = torch.from_numpy(np.array(post_s1_image, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    pre_label = torch.from_numpy(np.array(pre_label, dtype=np.float32)).unsqueeze(0).to(torch.float32)
    post_label = torch.from_numpy(np.array(post_label, dtype=np.float32)).unsqueeze(0).to(torch.float32)


    pre_s2_image = torch.where(torch.isnan(pre_s2_image), torch.tensor(0.), pre_s2_image)
    pre_s2_image = (pre_s2_image - config.s2_mean[None, :, None, None]) / config.s2_std[None, :, None, None]
    # print(config.s2_mean[None, :, None, None])

    post_s2_image = torch.where(torch.isnan(post_s2_image), torch.tensor(0.), post_s2_image)
    post_s2_image = (post_s2_image - config.s2_mean[None, :, None, None]) / config.s2_std[None, :, None, None]

    # pre_s1_image = torch.where(torch.isnan(pre_s1_image), torch.tensor(0.), pre_s1_image)
    # pre_s1_image = (pre_s1_image - config.s1_mean[None, :, None, None]) / config.s1_std[None, :, None, None]

    s1_c2_images = load_S1_C2_image(config.pre_s1_image_paths[id], mode="S1_C2").unsqueeze(dim=0)
    # print(pre_s1_image.shape, s1_c2_images.shape)
    pre_s1_image = torch.cat((pre_s1_image, s1_c2_images[:, -2:-1]), dim=2)
    post_s1_image = torch.cat((post_s1_image, s1_c2_images[:, -1:]), dim=2)

    S1_CohIfg_images = load_S1_C2_image(config.pre_s1_image_paths[id], mode="S1_CohIfg").unsqueeze(dim=0)
    pre_s1_image = torch.cat((pre_s1_image, S1_CohIfg_images[:, -2:-1]), dim=2)
    post_s1_image = torch.cat((post_s1_image, S1_CohIfg_images[:, -1:]), dim=2)

    print(s1_c2_images.shape, pre_s1_image.shape, post_s1_image.shape)

    post_s1_image = torch.cat((pre_s1_image, post_s1_image), dim=2)


    return [pre_high_image, pre_s2_image, post_s1_image, pre_label, post_label], datalists

def cal_f1socre(label, result):
    label = label.numpy().squeeze()
    result = np.where(result>=0.5, 1, 0)
    c = 1
    TP = np.logical_and(result == c, label == c).sum()
    FN = np.logical_and(result != c, label == c).sum()
    FP = np.logical_and(result == c, label != c).sum()
    TN = np.logical_and(result != c, label != c).sum()
    Recall = TP / (TP + FN)
    Precision = TP / (TP + FP)
    F1 = 2 * Recall * Precision / (Precision + Recall)
    return round(F1, 3)

def prosess_output(id, output, config, datalists, name, pre_label, post_label):
    def output2numpy(building, features):
        building = torch.argmax(building, dim=1).cpu().detach().numpy().squeeze()
        features = features
        return building, features
    
    pre_high_building, pre_high_features = output2numpy(output["pre_high_building"], output["pre_high_features"])
    pre_low_building, pre_low_features = output2numpy(output["pre_low_building"], output["pre_low_features"])
    post_low_damage, post_low_features = output2numpy(output["post_low_damage"], output["post_low_features"])

    datalists["pre_high"]["out"] = pre_high_building*255
    datalists["pre_s2"]["out"] = pre_low_building*255
    datalists["post_s2"]["out"] = post_low_damage*255


    datalists["pre_high"]["feat"] = get_tenser_mean(pre_high_features)
    datalists["pre_s2"]["feat"] = get_tenser_mean(pre_low_features)
    datalists["post_s2"]["feat"] = get_tenser_mean(post_low_features)

    pre_high_f1 = cal_f1socre(pre_label, pre_high_building)
    pre_s2_f1 = cal_f1socre(pre_label, pre_low_building)
    post_s2_f1 = cal_f1socre(post_label, post_low_damage)


    # 保存结果至文件夹
    plot_img(datalists, config.plot_save_path, name, f1=[pre_high_f1, pre_s2_f1, post_s2_f1])
    # save_to_tif(pre_high_building, save_path=os.path.join(config.pre_high_result_save_path, name), test_image_path=config.pre_high_image_paths[id])
    save_to_tif(pre_low_building, save_path=os.path.join(config.pre_s1_result_save_path, name), test_image_path=config.pre_high_image_paths[id])
    save_to_tif(post_low_damage, save_path=os.path.join(config.post_s1_result_save_path, name.replace("_pre_", "_post_")), test_image_path=config.pre_high_image_paths[id])


def test(config):
    device = torch.device(config.device)
    net = load_net(config.model, device, config.model_path)
    print(f"test datasets size: {len(config.pre_high_image_paths)}")
    
    for id, image_high_path in tqdm(enumerate(config.pre_high_image_paths)):
        datalists = {"pre_high": {}, "post_high": {}, "pre_s2": {}, "post_s2": {},}
        name = os.path.basename(image_high_path).replace(".png", ".tif")
        inputs, datalists = load_data(id, config, name, datalists)
        # =============================开始测试================================ #
        with torch.no_grad():
            output = net(inputs, device)
        prosess_output(id, output, config, datalists, name, inputs[-2], inputs[-1])


def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument
    # arg("-c", "--config_path", default="./config/palisades_s1_hrnet.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/santa_s1_LBD.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/santa_s1_hrnet.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/eaton_s1_hrnet.py", type=Path, help="Path to the config.", required=False)
    arg("-c", "--config_path", default="./config/santa_s1_LBD2hawaii.py", type=Path, help="Path to the config.", required=False)

    # arg("-c", "--config_path", default="./config/gaza_s1_uabcd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/gaza_s1_LBDv16.py", type=Path, help="Path to the config.", required=False)

    # arg("-c", "--config_path", default="./config/palu_s1_uabcd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/santa_s1_uabcd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/santa_s1_hrsicd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/gaza_s1_hrsicd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/palu_s1_hrsicd.py", type=Path, help="Path to the config.", required=False)
    # arg("-c", "--config_path", default="./config/palu_s1_LBDv16.py", type=Path, help="Path to the config.", required=False)

    return parser.parse_args()

if __name__ == "__main__":
    os.environ['CUDA_VISIBLE_DEVICES'] = '2' 
    args = get_args()
    config = py2cfg(args.config_path)
    test(config)

