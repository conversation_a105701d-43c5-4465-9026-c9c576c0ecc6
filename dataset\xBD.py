import os
import numpy as np
import random
from PIL import Image, ImageDraw
from matplotlib import pyplot as plt
import albumentations as albu

import torch
from torch.utils.data import Dataset
from torch.utils.data import DataLoader
import torchvision.transforms as transforms
from glob import glob
import warnings
import torchvision.transforms.functional as TF

warnings.filterwarnings("ignore")


class xBD_Dataset(Dataset):
    def __init__(self, mode, base_path, mosaic_ratio=None):
        self.pre_image_paths  = []
        self.post_image_paths = []
        self.pre_label_paths  = []
        self.post_label_paths = []

                
        self.mode = mode
        self.mosaic_ratio = mosaic_ratio

        file = {"train": ["train", "tier3"], "test": ["test", "hold"], "val": ["test", "hold"]}
        if self.mode == "train":
            pre_image_paths  = glob(os.path.join(base_path, file[self.mode][0], "images", "*pre*.png"))
            pre_image_paths += glob(os.path.join(base_path, file[self.mode][1], "images", "*pre*.png"))
        else:
            pre_image_paths  = glob(os.path.join(base_path, file[self.mode][0], "images", "*pre*.png"))
            pre_image_paths += glob(os.path.join(base_path, file[self.mode][1], "images", "*pre*.png"))            

        self.pre_image_paths = pre_image_paths
        self.post_image_paths = [p.replace("pre", "post") for p in pre_image_paths]
        self.pre_label_paths = [p.replace("images", "targets") for p in pre_image_paths]
        self.post_label_paths = [p.replace("images", "targets").replace("pre", "post") for p in pre_image_paths]
                    
    def __len__(self):
        return len(self.pre_image_paths)        
    
    def load_image_label(self, image_paths, labels_paths, item):
        image  = Image.open(image_paths[item])
        label  = Image.open(labels_paths[item])
        return image, label
 
    def data_augmentation(self, image, label):
        if torch.rand(1) < 0.5:
            image = image.flip(dims=[2])
            label = label.flip(dims=[1])

        if torch.rand(1) < 0.5:
            image = image.flip(dims=[1])
            label = label.flip(dims=[0])

        return image, label

    def _enhance_rgb_shape(self, high_image):
        if self.mode == "train":
            # if random.random() < 0.5: # 全局颜色随机亮度和对比度增强
            if random.random() <  0.5:
                brightness = 0.5
                high_image = TF.adjust_brightness(high_image, 1 + (random.random() - 0.5) * 2 * brightness)
            if random.random() <  0.5:
                contrast = 0.5
                high_image = TF.adjust_contrast(high_image, 1 + (random.random() - 0.5) * 2 * contrast)
            if random.random() <  0.5: # 灰度处理
                gray = TF.rgb_to_grayscale(high_image, num_output_channels=1)
                high_image = gray.expand(3, -1, -1)  # 复制为3通道
        return high_image

    

    def __getitem__(self, item):
        pre_image , pre_label  = self.load_image_label(self.pre_image_paths, self.pre_label_paths, item)
        pre_image  = np.array(pre_image).astype(np.float32)
        pre_label  = np.array(pre_label).astype(np.uint8)
               
        pre_image = torch.from_numpy(np.array(pre_image/255, dtype=np.float32)).permute(2, 0, 1)
        pre_label = torch.from_numpy(np.array(pre_label, dtype=np.uint8))
        if self.mode == 'train': # 数据增强
            pre_image, pre_label = self.data_augmentation(pre_image, pre_label)
        pre_image = self._enhance_rgb_shape(pre_image)
        return pre_image, pre_label

def dataset_collate(batch):
    pre_images = torch.stack([item[0] for item in batch])
    pre_labels = torch.stack([item[1] for item in batch])

    return pre_images, pre_images, pre_labels, pre_labels


if __name__ == "__main__":
    scale = (0.75, 0.875, 1.0, 1.125, 1.25)
    size = 512
    mosaic_ratio = 1
    base_path = "./dataset/xBD"
    # base_path = "./dataset/xBD_512"
    base_path = "/mnt/d2/zxq/BuildingDamage/dataset/xBD_512"
    train_dataset = xBD_Dataset('train', base_path, mosaic_ratio)
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=False, num_workers=0, collate_fn=dataset_collate)
    print(len(train_loader.dataset))
    for batch_id, inputs in enumerate(train_loader):
        print(inputs[0].shape, inputs[1].shape, inputs[2].shape, inputs[3].shape)
        print(torch.max(inputs[0]), torch.min(inputs[0]), torch.max(inputs[1]), torch.min(inputs[1]))
        # x = input()
        # print(len(inputs[4]), inputs[4][0].shape, inputs[4][0])
