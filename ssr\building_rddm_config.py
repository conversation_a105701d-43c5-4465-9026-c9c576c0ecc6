"""
Building RDDM Configuration File
建筑物分割RDDM模型配置文件
"""

import os
from pathlib import Path

class BuildingRDDMConfig:
    """建筑物RDDM配置类"""
    
    def __init__(self):
        # 基础设置
        self.device = 'cuda' if os.environ.get('CUDA_VISIBLE_DEVICES') else 'cpu'
        self.seed = 42
        
        # 数据设置
        self.image_size = 256
        self.channels = 1  # 分割图为单通道
        
        # 模型设置
        self.dim = 64
        self.dim_mults = (1, 2, 4, 8)
        self.timesteps = 1000
        self.sampling_timesteps = 250
        self.loss_type = 'l1'
        self.objective = 'pred_res_noise'  # 'pred_res_noise', 'pred_res_add_noise', 'pred_x0_noise'
        
        # 训练设置
        self.batch_size = 8
        self.num_epochs = 100
        self.learning_rate = 1e-4
        self.weight_decay = 1e-6
        self.gradient_clip = 1.0
        
        # 保存设置
        self.save_every = 10
        self.results_folder = './results/building_rddm'
        self.checkpoint_path = None
        
        # 数据路径设置
        self.data_root = './data'
        self.low_seg_folder = 'low_seg'
        self.high_seg_folder = 'high_seg'
        
        # 验证设置
        self.val_split = 0.2
        self.num_samples_visualize = 4
        
        # 推理设置
        self.inference_batch_size = 1
        self.save_intermediate_steps = False
        
    def get_data_paths(self):
        """获取数据路径"""
        low_seg_dir = os.path.join(self.data_root, self.low_seg_folder)
        high_seg_dir = os.path.join(self.data_root, self.high_seg_folder)
        
        if not os.path.exists(low_seg_dir) or not os.path.exists(high_seg_dir):
            raise FileNotFoundError(f"Data directories not found: {low_seg_dir} or {high_seg_dir}")
        
        # 获取所有文件路径
        low_seg_files = sorted([f for f in os.listdir(low_seg_dir) 
                               if f.lower().endswith(('.png', '.jpg', '.jpeg', '.tif', '.tiff'))])
        high_seg_files = sorted([f for f in os.listdir(high_seg_dir) 
                                if f.lower().endswith(('.png', '.jpg', '.jpeg', '.tif', '.tiff'))])
        
        # 构建完整路径
        low_seg_paths = [os.path.join(low_seg_dir, f) for f in low_seg_files]
        high_seg_paths = [os.path.join(high_seg_dir, f) for f in high_seg_files]
        
        return low_seg_paths, high_seg_paths
    
    def create_directories(self):
        """创建必要的目录"""
        os.makedirs(self.results_folder, exist_ok=True)
        os.makedirs(os.path.join(self.results_folder, 'checkpoints'), exist_ok=True)
        os.makedirs(os.path.join(self.results_folder, 'samples'), exist_ok=True)
        os.makedirs(os.path.join(self.results_folder, 'logs'), exist_ok=True)


# 预定义配置
class QuickConfig(BuildingRDDMConfig):
    """快速测试配置"""
    def __init__(self):
        super().__init__()
        self.dim = 32
        self.dim_mults = (1, 2, 4)
        self.timesteps = 500
        self.sampling_timesteps = 100
        self.batch_size = 4
        self.num_epochs = 20


class HighQualityConfig(BuildingRDDMConfig):
    """高质量配置"""
    def __init__(self):
        super().__init__()
        self.dim = 128
        self.dim_mults = (1, 2, 4, 8, 16)
        self.timesteps = 2000
        self.sampling_timesteps = 500
        self.batch_size = 2
        self.num_epochs = 200
        self.learning_rate = 5e-5


class SentinelConfig(BuildingRDDMConfig):
    """Sentinel-2专用配置"""
    def __init__(self):
        super().__init__()
        # Sentinel-2通常是10m分辨率，可能需要更大的图像尺寸
        self.image_size = 512
        self.dim = 96
        self.dim_mults = (1, 2, 4, 8)
        self.timesteps = 1500
        self.sampling_timesteps = 300
        self.batch_size = 4
        self.num_epochs = 150
        
        # Sentinel-2数据特定设置
        self.data_root = './sentinel_data'
        self.low_seg_folder = 'sentinel_seg'  # Sentinel-2分割结果
        self.high_seg_folder = 'high_res_seg'  # 高分辨率参考分割


def get_config(config_name='default'):
    """
    获取配置
    Args:
        config_name: 配置名称 ('default', 'quick', 'high_quality', 'sentinel')
    """
    if config_name == 'quick':
        return QuickConfig()
    elif config_name == 'high_quality':
        return HighQualityConfig()
    elif config_name == 'sentinel':
        return SentinelConfig()
    else:
        return BuildingRDDMConfig()


# 示例配置使用
if __name__ == "__main__":
    # 默认配置
    config = get_config('default')
    print("Default Config:")
    print(f"  Image size: {config.image_size}")
    print(f"  Batch size: {config.batch_size}")
    print(f"  Timesteps: {config.timesteps}")
    
    # Sentinel-2配置
    sentinel_config = get_config('sentinel')
    print("\nSentinel-2 Config:")
    print(f"  Image size: {sentinel_config.image_size}")
    print(f"  Data root: {sentinel_config.data_root}")
    print(f"  Low seg folder: {sentinel_config.low_seg_folder}")
