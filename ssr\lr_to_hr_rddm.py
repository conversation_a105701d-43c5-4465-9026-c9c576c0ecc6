#!/usr/bin/env python3
"""
LR到HR特征恢复的RDDM模型
基于残差扩散去噪模型，专门用于从低分辨率特征恢复高分辨率特征
输入维度: B*2*H*W (LR特征和HR特征都是2通道)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from functools import partial
from einops import rearrange, reduce
from einops.layers.torch import Rearrange

def exists(x):
    return x is not None

def default(val, d):
    if exists(val):
        return val
    return d() if callable(d) else d

def extract(a, t, x_shape):
    """从累积系数中提取对应时间步的值"""
    batch_size = t.shape[0]
    out = a.gather(-1, t)
    return out.reshape(batch_size, *((1,) * (len(x_shape) - 1)))

def cosine_beta_schedule(timesteps, s=0.008):
    """余弦调度"""
    steps = timesteps + 1
    x = torch.linspace(0, timesteps, steps, dtype=torch.float64)
    alphas_cumprod = torch.cos(((x / timesteps) + s) / (1 + s) * math.pi * 0.5) ** 2
    alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
    betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
    return torch.clip(betas, 0, 0.999)

class SinusoidalPosEmb(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, x):
        device = x.device
        half_dim = self.dim // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=device) * -emb)
        emb = x[:, None] * emb[None, :]
        emb = torch.cat((emb.sin(), emb.cos()), dim=-1)
        return emb

class ResBlock(nn.Module):
    def __init__(self, dim, dim_out=None, groups=8):
        super().__init__()
        dim_out = default(dim_out, dim)
        
        # 自动调整groups以适应小维度
        groups = min(groups, dim, dim_out)
        groups = max(groups, 1)
        
        self.block1 = nn.Sequential(
            nn.GroupNorm(groups, dim),
            nn.SiLU(),
            nn.Conv2d(dim, dim_out, 3, padding=1)
        )
        
        self.block2 = nn.Sequential(
            nn.GroupNorm(groups, dim_out),
            nn.SiLU(),
            nn.Conv2d(dim_out, dim_out, 3, padding=1)
        )
        
        self.res_conv = nn.Conv2d(dim, dim_out, 1) if dim != dim_out else nn.Identity()

    def forward(self, x):
        h = self.block1(x)
        h = self.block2(h)
        return h + self.res_conv(x)

class Attention(nn.Module):
    def __init__(self, dim, heads=4, dim_head=32):
        super().__init__()
        self.scale = dim_head ** -0.5
        self.heads = heads
        hidden_dim = dim_head * heads
        
        self.to_qkv = nn.Conv2d(dim, hidden_dim * 3, 1, bias=False)
        self.to_out = nn.Conv2d(hidden_dim, dim, 1)

    def forward(self, x):
        b, c, h, w = x.shape
        qkv = self.to_qkv(x).chunk(3, dim=1)
        q, k, v = map(lambda t: rearrange(t, 'b (h c) x y -> b h c (x y)', h=self.heads), qkv)
        
        q = q * self.scale
        sim = torch.einsum('b h d i, b h d j -> b h i j', q, k)
        sim = sim - sim.amax(dim=-1, keepdim=True).detach()
        attn = sim.softmax(dim=-1)
        
        out = torch.einsum('b h i j, b h d j -> b h i d', attn, v)
        out = rearrange(out, 'b h (x y) d -> b (h d) x y', x=h, y=w)
        return self.to_out(out)

class UNetLRtoHR(nn.Module):
    """专门用于LR到HR特征恢复的UNet"""
    
    def __init__(
        self,
        dim=64,
        dim_mults=(1, 2, 4, 8),
        channels=2,  # 输入输出都是2通道
        condition=True,  # 使用LR特征作为条件
        resnet_block_groups=8
    ):
        super().__init__()
        
        self.channels = channels
        self.condition = condition
        
        # 输入通道数：主输入 + 条件输入
        input_channels = channels
        if condition:
            input_channels += channels  # LR特征作为条件
            
        # 时间嵌入
        time_dim = dim * 4
        self.time_mlp = nn.Sequential(
            SinusoidalPosEmb(dim),
            nn.Linear(dim, time_dim),
            nn.GELU(),
            nn.Linear(time_dim, time_dim)
        )
        
        # 初始卷积
        self.init_conv = nn.Conv2d(input_channels, dim, 7, padding=3)
        
        # 下采样路径
        self.downs = nn.ModuleList([])
        dims = [dim, *map(lambda m: dim * m, dim_mults)]
        in_out = list(zip(dims[:-1], dims[1:]))
        
        for ind, (dim_in, dim_out) in enumerate(in_out):
            is_last = ind >= (len(in_out) - 1)
            
            self.downs.append(nn.ModuleList([
                ResBlock(dim_in, dim_out, groups=resnet_block_groups),
                ResBlock(dim_out, dim_out, groups=resnet_block_groups),
                Attention(dim_out) if dim_out < 512 else nn.Identity(),
                nn.Conv2d(dim_out, dim_out, 4, 2, 1) if not is_last else nn.Identity()
            ]))
        
        # 中间层
        mid_dim = dims[-1]
        self.mid_block1 = ResBlock(mid_dim, mid_dim, groups=resnet_block_groups)
        self.mid_attn = Attention(mid_dim)
        self.mid_block2 = ResBlock(mid_dim, mid_dim, groups=resnet_block_groups)
        
        # 上采样路径
        self.ups = nn.ModuleList([])
        for ind, (dim_in, dim_out) in enumerate(reversed(in_out[1:])):
            is_last = ind >= (len(in_out) - 1)
            
            self.ups.append(nn.ModuleList([
                ResBlock(dim_out * 2, dim_in, groups=resnet_block_groups),
                ResBlock(dim_in, dim_in, groups=resnet_block_groups),
                Attention(dim_in) if dim_in < 512 else nn.Identity(),
                nn.ConvTranspose2d(dim_in, dim_in, 4, 2, 1) if not is_last else nn.Identity()
            ]))
        
        # 输出层 - 预测残差和噪声
        self.out_dim = channels * 2  # 残差 + 噪声
        self.final_res_block = ResBlock(dim * 2, dim, groups=resnet_block_groups)
        self.final_conv = nn.Conv2d(dim, self.out_dim, 1)

    def forward(self, x, time, x_condition=None):
        # 条件输入拼接
        if self.condition and x_condition is not None:
            x = torch.cat((x, x_condition), dim=1)
        
        # 初始卷积
        x = self.init_conv(x)
        r = x.clone()
        
        # 时间嵌入
        t = self.time_mlp(time)
        
        # 下采样
        h = []
        for resnet1, resnet2, attn, downsample in self.downs:
            x = resnet1(x)
            x = resnet2(x)
            x = attn(x)
            h.append(x)
            x = downsample(x)
        
        # 中间层
        x = self.mid_block1(x)
        x = self.mid_attn(x)
        x = self.mid_block2(x)
        
        # 上采样
        for resnet1, resnet2, attn, upsample in self.ups:
            x = torch.cat((x, h.pop()), dim=1)
            x = resnet1(x)
            x = resnet2(x)
            x = attn(x)
            x = upsample(x)
        
        # 输出
        x = torch.cat((x, r), dim=1)
        x = self.final_res_block(x)
        output = self.final_conv(x)
        
        # 分离残差和噪声
        residual, noise = output.chunk(2, dim=1)
        return residual, noise

class LRtoHRRDDM(nn.Module):
    """LR到HR特征恢复的RDDM模型"""
    
    def __init__(
        self,
        unet_dim=64,
        unet_dim_mults=(1, 2, 4, 8),
        channels=2,
        timesteps=1000,
        sampling_timesteps=250,
        loss_type='l1',
        objective='pred_res_noise',
        alpha_scale=0.1,  # alpha系数缩放，适合特征恢复
        beta_scale=0.001  # beta系数，很小
    ):
        super().__init__()
        
        # UNet模型
        self.unet = UNetLRtoHR(
            dim=unet_dim,
            dim_mults=unet_dim_mults,
            channels=channels,
            condition=True
        )
        
        self.channels = channels
        self.num_timesteps = timesteps
        self.sampling_timesteps = sampling_timesteps
        self.loss_type = loss_type
        self.objective = objective
        self.alpha_scale = alpha_scale
        self.beta_scale = beta_scale
        
        # 生成RDDM系数
        betas = cosine_beta_schedule(timesteps)
        alphas = 1. - betas
        alphas_cumprod = torch.cumprod(alphas, dim=0)
        
        # RDDM特有的alpha和beta系数
        # alpha控制残差的影响，beta控制噪声的影响
        alphas_cumsum = torch.linspace(0.01, alpha_scale, timesteps)  # 逐渐增加的alpha
        betas_cumsum = torch.linspace(0.001, beta_scale, timesteps)   # 很小的beta
        
        # 注册为buffer
        self.register_buffer('alphas_cumsum', alphas_cumsum)
        self.register_buffer('betas_cumsum', betas_cumsum)
        
        # 损失函数
        if loss_type == 'l1':
            self.loss_fn = F.l1_loss
        elif loss_type == 'l2':
            self.loss_fn = F.mse_loss
        else:
            raise ValueError(f'unknown loss type {loss_type}')

    def predict_start_from_res_noise(self, x_t, t, x_res, noise):
        """根据RDDM公式反推x_start"""
        return (
            x_t - 
            extract(self.alphas_cumsum, t, x_t.shape) * x_res -
            extract(self.betas_cumsum, t, x_t.shape) * noise
        )

    def q_sample(self, x_start, x_condition, t, noise=None):
        """RDDM前向过程: x_t = x_start + alpha * x_res + beta * noise"""
        if noise is None:
            noise = torch.randn_like(x_start)
        
        # 计算残差: x_res = x_condition - x_start (LR到HR的差异)
        x_res = x_condition - x_start
        
        # RDDM前向过程
        alpha_cumsum = extract(self.alphas_cumsum, t, x_start.shape)
        beta_cumsum = extract(self.betas_cumsum, t, x_start.shape)
        
        x_noisy = (
            x_start +
            alpha_cumsum * x_res +
            beta_cumsum * noise
        )
        
        return x_noisy, x_res, noise

    def forward(self, hr_features, lr_features, timesteps=None):
        """训练前向传播"""
        B = hr_features.shape[0]
        device = hr_features.device
        
        # 随机采样时间步
        if timesteps is None:
            timesteps = torch.randint(0, self.num_timesteps, (B,), device=device).long()
        
        # 前向扩散过程
        x_noisy, x_res_true, noise_true = self.q_sample(hr_features, lr_features, timesteps)
        
        # UNet预测
        pred_res, pred_noise = self.unet(x_noisy, timesteps, x_condition=lr_features)
        
        # 计算损失
        loss_res = self.loss_fn(pred_res, x_res_true)
        loss_noise = self.loss_fn(pred_noise, noise_true)
        
        # 总损失
        loss = loss_res + 0.1 * loss_noise
        
        # 预测HR特征
        pred_hr = self.predict_start_from_res_noise(x_noisy, timesteps, pred_res, pred_noise)
        
        return loss, pred_hr

    @torch.no_grad()
    def enhance_features(self, lr_features, num_steps=None):
        """从LR特征恢复HR特征"""
        if num_steps is None:
            num_steps = self.sampling_timesteps
            
        device = lr_features.device
        batch_size = lr_features.shape[0]
        
        # 从LR特征开始
        x = lr_features.clone()
        
        # 逐步去噪恢复
        for i in range(num_steps):
            t = torch.full((batch_size,), i * (self.num_timesteps // num_steps), device=device, dtype=torch.long)
            
            # 预测残差和噪声
            pred_res, pred_noise = self.unet(x, t, x_condition=lr_features)
            
            # 应用残差修正
            alpha = extract(self.alphas_cumsum, t, x.shape) * (1 - i / num_steps)  # 逐渐减小
            x = x + alpha * pred_res
            
            # 确保特征在合理范围内
            x = torch.clamp(x, -3, 3)  # 特征值通常在这个范围
        
        return x

def create_lr_to_hr_model(
    dim=64,
    dim_mults=(1, 2, 4),
    channels=2,
    timesteps=500,
    sampling_timesteps=50,
    alpha_scale=0.1,
    beta_scale=0.001
):
    """创建LR到HR特征恢复模型的便捷函数"""
    return LRtoHRRDDM(
        unet_dim=dim,
        unet_dim_mults=dim_mults,
        channels=channels,
        timesteps=timesteps,
        sampling_timesteps=sampling_timesteps,
        alpha_scale=alpha_scale,
        beta_scale=beta_scale
    )
