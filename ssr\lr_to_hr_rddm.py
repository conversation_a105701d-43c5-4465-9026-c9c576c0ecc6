#!/usr/bin/env python3
"""
LR到HR特征恢复的RDDM模型
基于残差扩散去噪模型，专门用于从低分辨率特征恢复高分辨率特征
输入维度: B*2*H*W (LR特征和HR特征都是2通道)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from functools import partial
from einops import rearrange, reduce
from einops.layers.torch import Rearrange

def exists(x):
    return x is not None

def default(val, d):
    if exists(val):
        return val
    return d() if callable(d) else d

def extract(a, t, x_shape):
    """从累积系数中提取对应时间步的值"""
    batch_size = t.shape[0]
    out = a.gather(-1, t)
    return out.reshape(batch_size, *((1,) * (len(x_shape) - 1)))

def cosine_beta_schedule(timesteps, s=0.008):
    """余弦调度"""
    steps = timesteps + 1
    x = torch.linspace(0, timesteps, steps, dtype=torch.float64)
    alphas_cumprod = torch.cos(((x / timesteps) + s) / (1 + s) * math.pi * 0.5) ** 2
    alphas_cumprod = alphas_cumprod / alphas_cumprod[0]
    betas = 1 - (alphas_cumprod[1:] / alphas_cumprod[:-1])
    return torch.clip(betas, 0, 0.999)

def gen_coefficients(timesteps, schedule="decreased", sum_scale=1.0):
    """生成RDDM系数 - 参考diffusion_res.py"""
    if schedule == "increased":
        x = torch.linspace(1, timesteps, timesteps, dtype=torch.float64)
        scale = 0.5*timesteps*(timesteps+1)
        alphas = x/scale
    elif schedule == "decreased":
        x = torch.linspace(1, timesteps, timesteps, dtype=torch.float64)
        x = torch.flip(x, dims=[0])
        scale = 0.5*timesteps*(timesteps+1)
        alphas = x/scale
    elif schedule == "average":
        alphas = torch.full([timesteps], 1/timesteps, dtype=torch.float64)
    else:
        alphas = torch.full([timesteps], 1/timesteps, dtype=torch.float64)

    assert alphas.sum()-torch.tensor(1) < torch.tensor(1e-10)
    return alphas*sum_scale

class SinusoidalPosEmb(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.dim = dim

    def forward(self, x):
        device = x.device
        half_dim = self.dim // 2
        emb = math.log(10000) / (half_dim - 1)
        emb = torch.exp(torch.arange(half_dim, device=device) * -emb)
        emb = x[:, None] * emb[None, :]
        emb = torch.cat((emb.sin(), emb.cos()), dim=-1)
        return emb

class ResBlock(nn.Module):
    def __init__(self, dim, dim_out=None, groups=8):
        super().__init__()
        dim_out = default(dim_out, dim)
        
        # 自动调整groups以适应小维度
        groups = min(groups, dim, dim_out)
        groups = max(groups, 1)
        
        self.block1 = nn.Sequential(
            nn.GroupNorm(groups, dim),
            nn.SiLU(),
            nn.Conv2d(dim, dim_out, 3, padding=1)
        )
        
        self.block2 = nn.Sequential(
            nn.GroupNorm(groups, dim_out),
            nn.SiLU(),
            nn.Conv2d(dim_out, dim_out, 3, padding=1)
        )
        
        self.res_conv = nn.Conv2d(dim, dim_out, 1) if dim != dim_out else nn.Identity()

    def forward(self, x):
        h = self.block1(x)
        h = self.block2(h)
        return h + self.res_conv(x)

class Attention(nn.Module):
    def __init__(self, dim, heads=4, dim_head=32):
        super().__init__()
        self.scale = dim_head ** -0.5
        self.heads = heads
        hidden_dim = dim_head * heads
        
        self.to_qkv = nn.Conv2d(dim, hidden_dim * 3, 1, bias=False)
        self.to_out = nn.Conv2d(hidden_dim, dim, 1)

    def forward(self, x):
        b, c, h, w = x.shape
        qkv = self.to_qkv(x).chunk(3, dim=1)
        q, k, v = map(lambda t: rearrange(t, 'b (h c) x y -> b h c (x y)', h=self.heads), qkv)
        
        q = q * self.scale
        sim = torch.einsum('b h d i, b h d j -> b h i j', q, k)
        sim = sim - sim.amax(dim=-1, keepdim=True).detach()
        attn = sim.softmax(dim=-1)
        
        out = torch.einsum('b h i j, b h d j -> b h i d', attn, v)
        out = rearrange(out, 'b h (x y) d -> b (h d) x y', x=h, y=w)
        return self.to_out(out)

class UNetLRtoHR(nn.Module):
    """专门用于LR到HR特征恢复的UNet"""
    
    def __init__(
        self,
        dim=64,
        dim_mults=(1, 2, 4, 8),
        channels=2,  # 输入输出都是2通道
        condition=True,  # 使用LR特征作为条件
        resnet_block_groups=8
    ):
        super().__init__()
        
        self.channels = channels
        self.condition = condition
        
        # 输入通道数：主输入 + 条件输入
        input_channels = channels
        if condition:
            input_channels += channels  # LR特征作为条件
            
        # 时间嵌入
        time_dim = dim * 4
        self.time_mlp = nn.Sequential(
            SinusoidalPosEmb(dim),
            nn.Linear(dim, time_dim),
            nn.GELU(),
            nn.Linear(time_dim, time_dim)
        )
        
        # 初始卷积
        self.init_conv = nn.Conv2d(input_channels, dim, 7, padding=3)
        
        # 下采样路径
        self.downs = nn.ModuleList([])
        dims = [dim, *map(lambda m: dim * m, dim_mults)]
        in_out = list(zip(dims[:-1], dims[1:]))
        
        for ind, (dim_in, dim_out) in enumerate(in_out):
            is_last = ind >= (len(in_out) - 1)
            
            self.downs.append(nn.ModuleList([
                ResBlock(dim_in, dim_out, groups=resnet_block_groups),
                ResBlock(dim_out, dim_out, groups=resnet_block_groups),
                Attention(dim_out) if dim_out < 512 else nn.Identity(),
                nn.Conv2d(dim_out, dim_out, 4, 2, 1) if not is_last else nn.Identity()
            ]))
        
        # 中间层
        mid_dim = dims[-1]
        self.mid_block1 = ResBlock(mid_dim, mid_dim, groups=resnet_block_groups)
        self.mid_attn = Attention(mid_dim)
        self.mid_block2 = ResBlock(mid_dim, mid_dim, groups=resnet_block_groups)
        
        # 上采样路径
        self.ups = nn.ModuleList([])
        for ind, (dim_in, dim_out) in enumerate(reversed(in_out[1:])):
            is_last = ind >= (len(in_out) - 1)
            
            self.ups.append(nn.ModuleList([
                ResBlock(dim_out * 2, dim_in, groups=resnet_block_groups),
                ResBlock(dim_in, dim_in, groups=resnet_block_groups),
                Attention(dim_in) if dim_in < 512 else nn.Identity(),
                nn.ConvTranspose2d(dim_in, dim_in, 4, 2, 1) if not is_last else nn.Identity()
            ]))
        
        # 输出层 - 直接预测x0 (HR特征)
        self.out_dim = channels  # 直接预测HR特征
        self.final_res_block = ResBlock(dim * 2, dim, groups=resnet_block_groups)
        self.final_conv = nn.Conv2d(dim, self.out_dim, 1)

    def forward(self, x, time, x_condition=None):
        # 条件输入拼接
        if self.condition and x_condition is not None:
            x = torch.cat((x, x_condition), dim=1)
        
        # 初始卷积
        x = self.init_conv(x)
        r = x.clone()
        
        # 时间嵌入
        t = self.time_mlp(time)
        
        # 下采样
        h = []
        for resnet1, resnet2, attn, downsample in self.downs:
            x = resnet1(x)
            x = resnet2(x)
            x = attn(x)
            h.append(x)
            x = downsample(x)
        
        # 中间层
        x = self.mid_block1(x)
        x = self.mid_attn(x)
        x = self.mid_block2(x)
        
        # 上采样
        for resnet1, resnet2, attn, upsample in self.ups:
            x = torch.cat((x, h.pop()), dim=1)
            x = resnet1(x)
            x = resnet2(x)
            x = attn(x)
            x = upsample(x)
        
        # 输出 - 直接预测x0
        x = torch.cat((x, r), dim=1)
        x = self.final_res_block(x)
        pred_x0 = self.final_conv(x)

        return pred_x0

class LRtoHRRDDM(nn.Module):
    """LR到HR特征恢复的RDDM模型"""
    
    def __init__(
        self,
        unet_dim=64,
        unet_dim_mults=(1, 2, 4, 8),
        channels=2,
        timesteps=1000,
        sampling_timesteps=250,
        loss_type='l1',
        objective='pred_res_noise',
        alpha_scale=0.1,  # alpha系数缩放，适合特征恢复
        beta_scale=0.001  # beta系数，很小
    ):
        super().__init__()
        
        # UNet模型
        self.unet = UNetLRtoHR(
            dim=unet_dim,
            dim_mults=unet_dim_mults,
            channels=channels,
            condition=True
        )
        
        self.channels = channels
        self.num_timesteps = timesteps
        self.sampling_timesteps = sampling_timesteps
        self.loss_type = loss_type
        self.objective = objective
        self.alpha_scale = alpha_scale
        self.beta_scale = beta_scale
        
        # 生成RDDM系数 - 参考diffusion_res.py
        alphas = gen_coefficients(timesteps, schedule="decreased")
        alphas_cumsum = alphas.cumsum(dim=0).clamp(0, 1)
        alphas_cumsum_prev = F.pad(alphas_cumsum[:-1], (1, 0), value=1.)

        betas2 = gen_coefficients(timesteps, schedule="increased", sum_scale=beta_scale)
        betas2_cumsum = betas2.cumsum(dim=0).clamp(0, 1)
        betas_cumsum = torch.sqrt(betas2_cumsum)
        betas2_cumsum_prev = F.pad(betas2_cumsum[:-1], (1, 0), value=1.)

        # 后验方差
        posterior_variance = betas2*betas2_cumsum_prev/betas2_cumsum
        posterior_variance[0] = 0
        
        # 注册为buffer - 参考diffusion_res.py
        self.register_buffer('alphas_cumsum', alphas_cumsum)
        self.register_buffer('alphas_cumsum_prev', alphas_cumsum_prev)
        self.register_buffer('betas_cumsum', betas_cumsum)
        self.register_buffer('betas2_cumsum', betas2_cumsum)
        self.register_buffer('betas2_cumsum_prev', betas2_cumsum_prev)
        self.register_buffer('posterior_variance', posterior_variance)
        
        # 损失函数
        if loss_type == 'l1':
            self.loss_fn = F.l1_loss
        elif loss_type == 'l2':
            self.loss_fn = F.mse_loss
        else:
            raise ValueError(f'unknown loss type {loss_type}')

    def q_sample(self, x_start, x_input, t, noise=None):
        """RDDM前向过程 - 参考diffusion_res.py"""
        if noise is None:
            noise = torch.randn_like(x_start)

        # 计算残差: x_res = x_input - x_start (LR到HR的残差)
        x_res = x_input - x_start

        # RDDM前向过程: x_t = x_start + alpha * x_res + beta * noise
        alpha_cumsum = extract(self.alphas_cumsum, t, x_start.shape)
        beta_cumsum = extract(self.betas_cumsum, t, x_start.shape)

        x_noisy = (
            x_start +
            alpha_cumsum * x_res +
            beta_cumsum * noise
        )

        return x_noisy

    def forward(self, hr_features, lr_features, timesteps=None):
        """训练前向传播 - 直接预测x0"""
        B = hr_features.shape[0]
        device = hr_features.device

        # 随机采样时间步
        if timesteps is None:
            timesteps = torch.randint(0, self.num_timesteps, (B,), device=device).long()

        # 前向扩散过程
        x_noisy = self.q_sample(hr_features, lr_features, timesteps)

        # UNet直接预测x0 (HR特征)
        pred_x0 = self.unet(x_noisy, timesteps, x_condition=lr_features)

        # 计算损失 - 直接与真实HR特征比较
        loss = self.loss_fn(pred_x0, hr_features)

        return loss, pred_x0

    @torch.no_grad()
    def p_sample(self, x_input, x, t, x_input_condition=None):
        """单步采样 - 参考diffusion_res.py"""
        b, *_, device = *x.shape, x.device
        batched_times = torch.full((x.shape[0],), t, device=x.device, dtype=torch.long)

        # 直接预测x0
        pred_x0 = self.unet(x, batched_times, x_condition=x_input)

        # 构建残差
        x_res = x_input - pred_x0

        # 计算均值和方差
        alpha_t = extract(self.alphas_cumsum, batched_times, x.shape)
        alpha_prev = extract(self.alphas_cumsum_prev, batched_times, x.shape)
        beta_t = extract(self.betas_cumsum, batched_times, x.shape)

        # 计算前一步的值
        if t > 0:
            # 添加噪声
            noise = torch.randn_like(x)
            # 计算model_mean
            model_mean = pred_x0 + alpha_prev * x_res
            # 添加后验方差
            posterior_var = extract(self.posterior_variance, batched_times, x.shape)
            pred_img = model_mean + torch.sqrt(posterior_var) * noise
        else:
            pred_img = pred_x0

        return pred_img, pred_x0

    @torch.no_grad()
    def p_sample_loop(self, lr_features, shape=None, return_all_timesteps=False):
        """完整采样循环 - 参考diffusion_res.py"""
        device = lr_features.device

        if shape is None:
            shape = lr_features.shape

        # 从LR特征开始，添加少量噪声
        img = lr_features + torch.randn_like(lr_features) * 0.1

        imgs = [img]

        # 采样时间步
        timesteps = torch.linspace(self.num_timesteps - 1, 0, self.sampling_timesteps).long()

        for t in timesteps:
            img, pred_x0 = self.p_sample(lr_features, img, t)
            imgs.append(img)

        ret = img if not return_all_timesteps else imgs
        return ret

    @torch.no_grad()
    def enhance_features(self, lr_features, return_all_timesteps=False):
        """从LR特征恢复HR特征 - 主要接口"""
        return self.p_sample_loop(lr_features, lr_features.shape, return_all_timesteps)

def create_lr_to_hr_model(
    dim=64,
    dim_mults=(1, 2, 4),
    channels=2,
    timesteps=500,
    sampling_timesteps=50,
    alpha_scale=1.0,
    beta_scale=1.0
):
    """创建LR到HR特征恢复模型的便捷函数"""
    return LRtoHRRDDM(
        unet_dim=dim,
        unet_dim_mults=dim_mults,
        channels=channels,
        timesteps=timesteps,
        sampling_timesteps=sampling_timesteps,
        alpha_scale=alpha_scale,
        beta_scale=beta_scale
    )
