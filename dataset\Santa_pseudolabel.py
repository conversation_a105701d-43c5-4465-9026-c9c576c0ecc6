import os
import re
import cv2
import imageio.v2 as imageio
import numpy as np
import random
from PIL import Image, ImageDraw
from matplotlib import pyplot as plt
from datetime import datetime, timedelta
import albumentations as albu
import shutil

import torch
from torch.utils.data import Dataset
from torch.utils.data import DataLoader
import torch.nn.functional as F
import torchvision.transforms as transforms
import torchvision.transforms.functional as TF
from glob import glob
from osgeo import gdal, osr
from pyproj import Proj, Transformer
from functools import partial

class Santa_S12(Dataset):
    def __init__(self, mode):
        self.mode = mode
        file = {"train": "train", "test": "test", "hold": "hold"}
        pre_label_paths = glob(os.path.join("/mnt/d2/zxq/BDS12/dataset/santa", file[self.mode], "labels", "*santa*pre**.tif"))
        if self.mode == "train":
            pre_label_paths+= glob(os.path.join("./dataset/santa", "test", "labels", "*santa*pre**.tif"))
            pre_label_paths+= glob(os.path.join("./dataset/santa", "hold", "labels", "*santa*pre**.tif"))

        # add pseudo datasat
        # if self.mode == "train":
        #     pre_label_paths+= glob(os.path.join("./dataset/palisades", "test", "pseudo_labels", "*palisades*pre**.tif"))
        #     pre_label_paths+= glob(os.path.join("./dataset/palisades", "train", "pseudo_labels", "*palisades*pre**.tif"))

        # add LA datasat
        # if self.mode == "train":
        #     pre_label_paths+= glob(os.path.join("./dataset/palisades", "test", "labels", "*palisades*pre**.tif"))
        #     pre_label_paths+= glob(os.path.join("./dataset/palisades", "train", "labels", "*palisades*pre**.tif"))
        #     pre_label_paths+= glob(os.path.join("./dataset/eaton", "test", "labels", "*eaton*pre**.tif"))
        #     pre_label_paths+= glob(os.path.join("./dataset/eaton", "train", "labels", "*eaton*pre**.tif"))

        if self.mode == "test":
            # pre_label_paths = glob(os.path.join("./dataset/palisades", "test", "labels", "*palisades*pre**.tif"))
            # pre_label_paths+= glob(os.path.join("./dataset/palisades", "train", "labels", "*palisades*pre**.tif"))
            pre_label_paths = glob(os.path.join("./dataset/hawaii", "test", "labels", "*hawaii*pre**.tif"))
            pre_label_paths+= glob(os.path.join("./dataset/hawaii", "train", "labels", "*hawaii*pre**.tif"))

            # 删除多余图片
            delete_2_paths = []
            for pre_label_path in pre_label_paths:
                if "disaster_2" in pre_label_path or "_2.tif" in pre_label_path:
                    # pre_label_paths.remove(pre_label_path)
                    continue
                else:
                    delete_2_paths.append(pre_label_path)
            pre_label_paths = delete_2_paths


        self.s2_mean_std = np.loadtxt("./dataset/santa/santa_s2_mean_std3.txt")
        self.s2_mean, self.s2_std = self.s2_mean_std[0,:], self.s2_mean_std[1,:]

        self.pre_high_image_paths = []
        self.post_high_image_paths = []
        self.pre_s2_image_paths = []
        self.post_s2_image_paths = []
        self.pre_s1_image_paths = []
        self.post_s1_image_paths = []
        self.pre_label_paths = []
        self.post_label_paths = []

        for item, pre_label_path in enumerate(pre_label_paths):
            image_name = pre_label_path.split(".tif")[0]
            self.pre_high_image_paths.append(image_name.replace("labels", "images").replace('pseudo_', ''))
            self.post_high_image_paths.append(image_name.replace("labels", "images").replace("pre", "post").replace('pseudo_', ''))
            self.pre_s2_image_paths.append(image_name.replace("pre", "pre").replace("labels", "S2").replace('pseudo_', ''))
            self.post_s2_image_paths.append(image_name.replace("pre", "post").replace("labels", "S2_post").replace('pseudo_', ''))
            self.pre_s1_image_paths.append(image_name.replace("pre", "pre").replace("labels", "S1").replace('pseudo_', ''))
            self.post_s1_image_paths.append(image_name.replace("pre", "post").replace("labels", "S1_post").replace('pseudo_', ''))
            self.pre_label_paths.append(pre_label_path)
            self.post_label_paths.append(pre_label_path.replace("pre", "post"))

        self.pre_high_image_paths = np.array(self.pre_high_image_paths)
        self.post_high_image_paths = np.array(self.post_high_image_paths)
        self.pre_s2_image_paths = np.array(self.pre_s2_image_paths)
        self.post_s2_image_paths = np.array(self.post_s2_image_paths)
        self.pre_label_paths = np.array(self.pre_label_paths)
        self.post_label_paths = np.array(self.post_label_paths)
        self.pre_s1_image_paths = np.array(self.pre_s1_image_paths)
        self.post_s1_image_paths = np.array(self.post_s1_image_paths)

    def __len__(self):
        return len(self.pre_label_paths)        

    def _load_high_image(self, image_path):
        dataset = gdal.Open(os.path.join(image_path + ".tif"))
        high_image = dataset.ReadAsArray()
        high_image = torch.from_numpy(np.array(high_image).astype(dtype=np.float32)/255.)
        return high_image


    def _load_S2_image(self, image_path):
        s2_image_paths = glob(os.path.join(image_path +  "_S2**.tif"))
        s2_image_paths.sort()
        if "S2_post" in image_path:
            s2_image_paths = s2_image_paths[-1:]
        # print(s2_image_paths, image_path)

        s2_time_series_images = []
        for s2_image_path in s2_image_paths:
            dataset = gdal.Open(s2_image_path)
            image_array = dataset.ReadAsArray()
            x_size = dataset.RasterXSize
            y_size = dataset.RasterYSize
            s2_time_series_images.append(image_array)
        s2_time_series_images = np.array(s2_time_series_images).reshape((len(s2_image_paths), -1, y_size, x_size))
        s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)

        # 数据归一化
        s2_time_series_images = torch.from_numpy(np.array(s2_time_series_images, dtype=np.float32))
        s2_time_series_images = torch.where(torch.isnan(s2_time_series_images), torch.tensor(0.), s2_time_series_images)
        s2_time_series_images = (s2_time_series_images - self.s2_mean[None, :, None, None]) / self.s2_std[None, :, None, None]
        return s2_time_series_images


    def _load_label(self, label_path):
        label = gdal.Open(label_path)
        label = label.ReadAsArray()
        label = torch.from_numpy(np.array(label, dtype=np.float32))
        label = torch.where(torch.isnan(label), torch.tensor(0.), label).to(torch.float32)
        return label
    
    def _data_augmentation(self, pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label):
        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[2])
            pre_s2_images = pre_s2_images.flip(dims=[3])
            post_s2_images = post_s2_images.flip(dims=[3])
            pre_label = pre_label.flip(dims=[1])
            post_label = post_label.flip(dims=[1])

        if torch.rand(1) < 0.5:
            pre_high_image = pre_high_image.flip(dims=[1])
            pre_s2_images = pre_s2_images.flip(dims=[2])
            post_s2_images = post_s2_images.flip(dims=[2])
            pre_label = pre_label.flip(dims=[0])
            post_label = post_label.flip(dims=[0])

        # k = torch.randint(0, 4, (1,)).item()
        # if k > 0:
        #     pre_high_image = torch.rot90(pre_high_image, k=k, dims=(1, 2))
        #     pre_s2_images = torch.rot90(pre_s2_images, k=k, dims=(2, 3))
        #     post_s2_images = torch.rot90(post_s2_images, k=k, dims=(2, 3))
        #     pre_label = torch.rot90(pre_label, k=k, dims=(0, 1))
        #     post_label = torch.rot90(post_label, k=k, dims=(0, 1))

        return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label


    def _enhance_rgb_shape(self, high_image, pre_label, image_path):
        if self.mode == "train" and "santa" in image_path:
            # if random.random() < 0.5: # 全局颜色随机亮度和对比度增强
            if random.random() <  0.5:
                brightness = 0.5
                high_image1 = TF.adjust_brightness(high_image, 1 + (random.random() - 0.5) * 2 * brightness)
            if random.random() <  0.5:
                contrast = 0.5
                high_image2 = TF.adjust_contrast(high_image, 1 + (random.random() - 0.5) * 2 * contrast)
            if random.random() <  0.5: # 灰度处理
                gray = TF.rgb_to_grayscale(high_image, num_output_channels=1)
                high_image3 = gray.expand(3, -1, -1)  # 复制为3通道

            # if random.random() <  0.5: # 建筑物对象纹理破坏: 黑白图像块, 背景为1、内容为0
            #     pre_label1 = torch.where(pre_label==1, 0, 1).unsqueeze(0)
            #     high_image4 = high_image * pre_label1
            # if random.random() < 0.5: # 建筑物对象纹理破坏: 内部颜色随机抖动
            #     # pre_label2 = torch.where(pre_label==1, 1, 0)#.unsqueeze(0)
            #     noise = torch.randn_like(high_image) * 0.05
            #     high_image5 = high_image + noise #*pre_label2
                # print(high_image.shape, noise.shape, pre_label.shape)
                # high_image = torch.clamp(high_image, 0, 1)
        return high_image
    
        def to_numpy(img):
            img = img.detach().cpu().numpy()
            # print(img.shape)
            img = np.transpose(img, (1, 2, 0))  # [H, W, C]
            return np.clip(img, 0, 1)

        # 绘图
        plt.figure(figsize=(18, 6))
        imgs, titles = [], []
        imgs = [high_image, high_image1, high_image2, high_image3, high_image4, high_image5]
        titles = ["orignal", "bright", "contrast", "gray", "black", "color"]
        for i, img in enumerate(imgs):
            plt.subplot(1, len(imgs), i+1)
            plt.imshow(to_numpy(img))
            plt.title(titles[i])
            plt.axis('off')
        plt.tight_layout()
        plt.show()
        plt.savefig("./high_rgb_dataaug.png")
        # x = input()
        return high_image
    
    def _random_rgb_shift(self, image, shift_range=(-0.05, 0.05)):
        if random.random() > 0.5:
            scale_range=(0.9, 1.1)
            scale = torch.empty(13).uniform_(*scale_range).to(image.device)
            image[:, :13] = image[:, :13] * scale.view(1, -1, 1, 1)
        if random.random() > 0.5:
            for i, band in enumerate([3, 2, 1]):  # R=4, G=3, B=2（从1开始计数）
                shift = torch.empty(1).uniform_(*shift_range).item()
                image[:, band] += shift
        return image

    def __getitem__(self, item):
        image_path = self.pre_label_paths[item]
        pre_high_image = self._load_high_image(self.pre_high_image_paths[item])

        pre_s2_images = self._load_S2_image(self.pre_s2_image_paths[item])
        post_s2_images = self._load_S2_image(self.post_s2_image_paths[item])

        pre_label = self._load_label(self.pre_label_paths[item])
        post_label = self._load_label(self.post_label_paths[item])
        if ("santa" in image_path or "palu" in image_path) and "/labels/" in image_path:
            pre_label = torch.where(pre_label>=0.5, 1, 0).to(torch.float32)
            post_label = torch.where(post_label>=5, 1, post_label).to(torch.float32)
            post_label = torch.where(post_label>=3, 1, 0).to(torch.float32)

        elif ("palisades" in image_path or "eaton" in image_path) and "/labels/" in image_path:
            pre_label = torch.where(pre_label==1, 1, 0).to(torch.float32)
            post_label = torch.where(post_label==1, 1, 0).to(torch.float32)
 
        elif "hawaii" in image_path  and "/labels/" in image_path:
            pre_label = torch.where(pre_label>=1, 1, 0).to(torch.float32)
            post_label = torch.where(post_label>=3, 1, 0).to(torch.float32)

        # elif ("palisades" in image_path or "eaton" in image_path) and "/pseudo_labels/" in image_path:
            # pre_label = torch.where(pre_label>=0.7, 1, pre_label).to(torch.float32)
            # pre_label = torch.where(pre_label<=0.3, 0, pre_label).to(torch.float32)
            # pre_label = torch.where((pre_label > 0.3) & (pre_label < 0.7), 255, pre_label).to(torch.float32)

            # post_label = torch.where(post_label>=0.7, 1, post_label).to(torch.float32)        
            # post_label = torch.where(post_label<=0.3, 0, post_label).to(torch.float32)
            # post_label = torch.where((post_label > 0.3) & (post_label < 0.7), 255, post_label).to(torch.float32)

            # pre_label = torch.where(pre_label>=0.5, 1, 0).to(torch.float32)
            # post_label = torch.where(post_label>=0.5, 1, 0).to(torch.float32)        


        pseudo_label = torch.ones(1) if "/pseudo_labels/" in image_path else torch.zeros(1) # 判断是否使用 CE or KL 损失函数

        # print("/pseudo_labels/" in image_path, self.pre_label_paths[item], self.post_label_paths[item])

        if self.mode == "train":
            pre_high_image = self._enhance_rgb_shape(pre_high_image, pre_label, image_path)
            # pre_s2_images = self._random_rgb_shift(pre_s2_images)
            # post_s2_images = self._random_rgb_shift(post_s2_images)

            pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label = self._data_augmentation(pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label)
        return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label, pseudo_label, self.pre_label_paths[item], self.post_label_paths[item]

def dataset_collate(batch):
    pre_high_image = torch.stack([item[0] for item in batch])
    pre_s2_images = torch.stack([item[1] for item in batch])
    post_s2_images = torch.stack([item[2] for item in batch])
    pre_label = torch.stack([item[3] for item in batch])
    post_label = torch.stack([item[4] for item in batch])
    pseudo_label = torch.stack([item[5] for item in batch])
    pre_label_paths = [item[6] for item in batch]
    post_label_paths = [item[7] for item in batch]
    return pre_high_image, pre_s2_images, post_s2_images, pre_label, post_label, pseudo_label, pre_label_paths, post_label_paths


def remove():
    # pre_label_paths = glob(os.path.join("./dataset/palisades", "test", "labels", "*palisades*.tif"))
    pre_label_paths = glob(os.path.join("./dataset/palisades", "train", "labels", "*palisades*.tif"))
    # print(pre_label_path)
    for pre_label_path in pre_label_paths:
        pseudo_label_path = pre_label_path.replace("train", "test").replace("labels", "pseudo_labels")
        if os.path.exists(pseudo_label_path):
            pseudo_label_save_path = pre_label_path.replace("labels", "pseudo_labels")
            os.makedirs(os.path.dirname(pseudo_label_save_path), exist_ok=True)
            print(pseudo_label_path, pseudo_label_save_path)
            shutil.move(src=pseudo_label_path, dst=pseudo_label_save_path)
    return


if __name__ == "__main__":
    # remove()
    train_dataset = Santa_S12('test')
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True, num_workers=1, drop_last=False)
    print(len(train_loader.dataset))
    for batch_id, inputs in enumerate(train_loader):
        print(batch_id, inputs[0].shape, inputs[1].shape, inputs[2].shape, inputs[3].shape, inputs[4].shape)
        # print(torch.max(inputs[0]), torch.min(inputs[0]), torch.max(inputs[1]), torch.min(inputs[1]))
        # print(torch.max(inputs[2]), torch.min(inputs[2]), torch.max(inputs[3]), torch.min(inputs[3]))
        # print(torch.max(inputs[4]), torch.min(inputs[4]), torch.max(inputs[5]), torch.min(inputs[5]))
        # print(batch_id, torch.max(inputs[3]), torch.min(inputs[3]), torch.max(inputs[4]), torch.min(inputs[4]), inputs[5].shape)
    #     x = input()

