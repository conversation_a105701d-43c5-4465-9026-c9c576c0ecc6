import os
import numpy as np
from osgeo import gdal
import matplotlib.pyplot as plt
import torch
from tqdm import tqdm
from glob import glob
from sklearn.manifold import TSNE
import random

def load_high_image(image_path):
    dataset = gdal.Open(image_path)
    return dataset.ReadAsArray()

def load_label(label_path):
    label = gdal.Open(label_path).ReadAsArray()
    # label = np.where(label ==1, 1, 0).astype(np.uint8)
    return  label.astype(np.float32)

def load_S2_image(image_path):
    s2_image_paths = glob(os.path.join(image_path +  "_S2**.tif"))
    s2_image_paths.sort()
    s2_image_paths = s2_image_paths[-1:]
    # print(s2_image_paths, image_path)

    s2_time_series_images = []
    for s2_image_path in s2_image_paths:
        dataset = gdal.Open(s2_image_path)
        image_array = dataset.ReadAsArray()
        x_size = dataset.RasterXSize
        y_size = dataset.RasterYSize
        s2_time_series_images.append(image_array)
    s2_time_series_images = np.array(s2_time_series_images).reshape((len(s2_image_paths), -1, y_size, x_size))
    s2_time_series_images = np.delete(s2_time_series_images, 13, axis=1)
    s2_time_series_images = np.nan_to_num(s2_time_series_images)
    # s2_time_series_images = np.where(s2_time_series_images == None, 0, s2_time_series_images)

    s2_time_series_images = torch.from_numpy(np.array(s2_time_series_images, dtype=np.float32))
    s2_time_series_images = torch.nn.functional.interpolate(s2_time_series_images, size=(384, 384), mode='bilinear', align_corners=True)
    s2_time_series_images = s2_time_series_images.numpy()

    return s2_time_series_images


def collect_high_masked_pixels(image_dir, max_pixels=10000):
    image_lists = []
    for split in ["train", "test", "hold"]:
        image_lists += glob(os.path.join(image_dir, f"{split}/images/*pre*.tif"))

    image_lists.sort()
    all_masked_pixels = []

    for image_list in tqdm(image_lists, desc=f"Processing {os.path.basename(image_dir)}"):
        label_path = image_list.replace("/images/", "/labels/")
        if not os.path.exists(label_path):
            continue

        rgb = load_high_image(image_list)
        mask = load_label(label_path)

        # [3, H, W] -> H*W x 3
        rgb = rgb.transpose(1, 2, 0)
        masked_pixels = rgb[mask == 1]  # [N, 3]

        if masked_pixels.shape[0] > 0:
            all_masked_pixels.append(masked_pixels)

    # 拼接并采样以统一大小
    if all_masked_pixels:
        np.random.seed(0)
        all_masked_pixels = np.concatenate(all_masked_pixels, axis=0)  # shape: (N, 3)
        print(all_masked_pixels.shape)
        indices = np.random.choice(all_masked_pixels.shape[0], max_pixels, replace=False)
        all_masked_pixels = all_masked_pixels[indices]
        return all_masked_pixels
    else:
        return np.empty((0, 3))

def get_high_label_tsneplot():
    # 路径设置
    santa_image_dir = "/mnt/d2/zxq/BDS12/dataset/santa/"
    palis_image_dir = "/mnt/d2/zxq/BDS12/dataset/palisades/"
    eaton_image_dir = "/mnt/d2/zxq/BDS12/dataset/eaton/"
    palu_image_dir = "/mnt/d2/zxq/BDS12/dataset/palu/"

    # 提取特征
    santa_pixels = collect_high_masked_pixels(santa_image_dir)
    palis_pixels = collect_high_masked_pixels(palis_image_dir)
    eaton_pixels = collect_high_masked_pixels(eaton_image_dir)
    palu_pixels = collect_high_masked_pixels(palu_image_dir)

    # 统一采样数量
    min_len = min(len(santa_pixels), len(palis_pixels), len(eaton_pixels), len(palu_pixels))
    santa = santa_pixels[:min_len]
    palis = palis_pixels[:min_len]
    eaton = eaton_pixels[:min_len]
    palu = palu_pixels[:min_len]

    X = np.concatenate([santa, palis, eaton, palu], axis=0)  # [N, 3]
    y = np.array([0] * min_len + [1] * min_len + [2] * min_len + [3] * min_len)

    print(f"X shape: {X.shape}, y shape: {y.shape}")

    # t-SNE 降维 CPU
    tsne = TSNE(n_components=2, perplexity=30, random_state=42)
    X_tsne = tsne.fit_transform(X)
    plt.figure(figsize=(10, 8))
    colors = ['red', 'blue', 'green', 'purple']
    labels = ['Santa', 'Palisades', 'Eaton', 'Palu']
    for i in range(4):
        plt.scatter(X_tsne[y == i, 0], X_tsne[y == i, 1], label=labels[i], s=1, alpha=0.4, color=colors[i])
    plt.legend()
    plt.title("t-SNE Visualization of Building Pixels (RGB) across 4 Events")
    plt.tight_layout()
    plt.grid(True)
    plt.savefig("tsne_high_rgb_4events_10000.png", dpi=300)
    plt.show()

    # GPU
    # from tsnecuda import TSNE
    # tsne = TSNE(n_components=2, perplexity=50)
    # X_tsne = tsne.fit_transform(X)
    # plt.figure(figsize=(10, 8))
    # for i, label in enumerate(['Santa', 'Palisades', 'Eaton', 'Palu']):
    #     plt.scatter(X_tsne[y == i, 0], X_tsne[y == i, 1], label=label, s=1, alpha=0.5)
    # plt.legend()
    # plt.title("t-SNE of Building Pixels (RGB) from 4 Events")
    # plt.tight_layout()
    # plt.savefig("tsne_four_events.png")
    # plt.show()


def collect_s2_masked_pixels(image_dir):
    image_lists = glob(os.path.join(image_dir, "hold/images/*pre*.tif"))
    image_lists += glob(os.path.join(image_dir, "test/images/*pre*.tif"))
    image_lists += glob(os.path.join(image_dir, "train/images/*pre*.tif"))

    image_lists.sort()
    random.seed(0)
    random.shuffle(image_lists)
    
    pre_masked_pixels = [[] for _ in range(17)] 
    post_masked_pixels = [[] for _ in range(17)]

    for id, image_list in enumerate(tqdm(image_lists, desc=f"Processing {os.path.basename(image_dir)}")):
        # if id >= 100:
        #     break
        image_name = image_list.split(".tif")[0]
        pre_s2 = load_S2_image(image_name.replace("/images/", "/S2/"))
        post_s2 = load_S2_image(image_name.replace("/images/", "/S2_post/").replace("pre", "post"))
        # print(image_name.replace("/images/", "/S2/"), image_name.replace("/images/", "/S2_post/").replace("pre", "post"))

        pre_label = load_label(image_list.replace("/images/", "/labels/"))
        post_label = load_label(image_list.replace("/images/", "/labels/").replace("pre", "post"))
        # print(image_list.replace("/images/", "/labels/"), image_list.replace("/images/", "/labels/").replace("pre", "post"))

        if "santa" in image_list or "palu" in image_list:
            pre_label = np.where(pre_label >= 0.5, 1, 0)
            post_label = np.where(post_label >= 5, 1, post_label)
            post_label = np.where(post_label >= 3, 1, 0)
        elif "palisades" in image_list or "eaton" in image_list:
            pre_label = np.where(pre_label == 1, 1, 0)
            post_label = np.where(post_label == 1, 1, 0)

        # pre_label = np.resize(pre_label, new_shape=(48, 48))
        # post_label = np.resize(post_label, new_shape=(48, 48))
        # print(np.max(pre_label), np.min(pre_label), np.max(post_label), np.min(post_label), np.sum(pre_label), np.sum(post_label), pre_label.shape)

        # 获取前期建筑物的像素值
        for band_idx in range(17):  # 假设影像有17个波段
            if np.sum(pre_label) > 0:
                # print(pre_s2[0, band_idx][pre_label == 1].shape)
                # pre_masked_pixels[band_idx].append(pre_s2[0, band_idx][pre_label == 1])
                pre_masked_pixels[band_idx].append(np.mean(pre_s2[0, band_idx][pre_label == 1], keepdims=True))

        # 获取后期建筑物的像素值
        for band_idx in range(17):  # 假设影像有17个波段
            if np.sum(post_label) > 0:
                # print(post_s2[0, band_idx][post_label == 1].shape, np.mean(post_s2[0, band_idx][post_label == 1], keepdims=True).shape)
                # post_masked_pixels[band_idx].append(post_s2[0, band_idx][post_label == 1])
                post_masked_pixels[band_idx].append(np.mean(post_s2[0, band_idx][post_label == 1], keepdims=True))

        # print(np.sum(pre_label), np.sum(post_label), len(pre_s2[0, band_idx][pre_label == 1]), len(post_s2[0, band_idx][post_label == 1]))
    # 将所有收集到的前后期建筑物像素值整合在一起
    pre_masked_pixels = [np.concatenate(pixels, axis=0) for pixels in pre_masked_pixels]
    post_masked_pixels = [np.concatenate(pixels, axis=0) for pixels in post_masked_pixels]
    print(np.array(pre_masked_pixels).shape, np.array(post_masked_pixels).shape)

    return [np.array(pre_masked_pixels), np.array(post_masked_pixels)]

def get_s2_label_tsneplot(sample_size=500):
    # 获取不同数据集的建筑物像素
    santa_pixels = collect_s2_masked_pixels("/mnt/d2/zxq/BDS12/dataset/santa/")
    palis_pixels = collect_s2_masked_pixels("/mnt/d2/zxq/BDS12/dataset/palisades/")
    eaton_pixels = collect_s2_masked_pixels("/mnt/d2/zxq/BDS12/dataset/eaton/")
    palu_pixels = collect_s2_masked_pixels("/mnt/d2/zxq/BDS12/dataset/palu/")

    # 从不同数据集提取前后期建筑物像素
    pre_pixels, post_pixels, labels = [], [], []

    def sample_dataset(pre_pixels_data, post_pixels_data, dataset_label):
        nonlocal pre_pixels, post_pixels, labels
        # 确保前后期样本数量一致，采样时选择较少的数量
        sample_count = min(pre_pixels_data.shape[1], post_pixels_data.shape[1], sample_size)
        
        # 抽样，确保前后期样本数量一致
        sampled_indices_pre = np.random.choice(pre_pixels_data.shape[1], size=sample_count, replace=False)
        sampled_indices_post = np.random.choice(post_pixels_data.shape[1], size=sample_count, replace=False)
        
        pre_pixels_data = pre_pixels_data[:, sampled_indices_pre]
        post_pixels_data = post_pixels_data[:, sampled_indices_post]

        pre_pixels.append(pre_pixels_data.T)  # 转置以匹配 (n_samples, n_features)
        post_pixels.append(post_pixels_data.T)  # 转置以匹配 (n_samples, n_features)
        # 为每个数据集的每个灾前和灾后样本添加标签
        labels.extend([dataset_label] * sample_count)  # 灾前标签
        labels.extend([dataset_label+1] * sample_count)  # 灾后标签
        
    # 对每个数据集进行抽样
    sample_dataset(santa_pixels[0], santa_pixels[1], dataset_label=1)  # Santa
    sample_dataset(palis_pixels[0], palis_pixels[1], dataset_label=3)  # Palisades
    sample_dataset(eaton_pixels[0], eaton_pixels[1], dataset_label=5)  # Palisades
    sample_dataset(palu_pixels[0], palu_pixels[1], dataset_label=7)  # Palu

    # 合并所有前后期建筑物像素值
    pre_pixels_data = np.concatenate(pre_pixels, axis=0)
    post_pixels_data = np.concatenate(post_pixels, axis=0)
    
    # 检查拼接后的形状
    print(f"pre_pixels_data shape: {pre_pixels_data.shape}")
    print(f"post_pixels_data shape: {post_pixels_data.shape}")

    # 合并前后期建筑物像素值
    all_pixels = np.concatenate([pre_pixels_data, post_pixels_data], axis=0)
    labels = np.array(labels)

    # 检查合并后的数据大小是否一致
    assert all_pixels.shape[0] == labels.size, f"Shape mismatch: {all_pixels.shape[0]} vs {labels.size}"

    # 使用 t-SNE 降维到2D
    tsne = TSNE(n_components=2, perplexity=30, learning_rate=200, random_state=42)
    tsne_result = tsne.fit_transform(all_pixels)

    # 绘制 t-SNE 图
    plt.figure(figsize=(10, 8))
    colors = ['red', 'blue', 'green', 'purple', 'orange', 'cyan', 'magenta', 'yellow']
    labels_text = ['Santa - Pre', 'Santa - Post', 'Palisades - Pre', 'Palisades - Post', 'Eaton - Pre', 'Eaton - Post', 'Palu - Pre', 'Palu - Post']

    # 对不同类别绘制散点图
    for i in range(8):        
        # 绘制灾前样本（圆点
        if (i + 1) % 2 == 0:  # 偶数为灾后（使用三角形）
            marker = '^'
        else:  # 奇数为灾前（使用圆点）
            marker = 'o'
        # if i in [0,1,4,5]:
        # if i in [0,1,2,3]:
        # if i in [0,1,6,7]:
        # if i in [4,5,6,7]:
        # if i in [2,3,4,5,]:

        plt.scatter(tsne_result[labels == i + 1, 0], tsne_result[labels == i + 1, 1], label=labels_text[i], s=10, alpha=0.6, color=colors[i], marker=marker)

    plt.legend()
    plt.title("t-SNE Visualization of Pre and Post Building Pixels")
    plt.xlabel("t-SNE Component 1")
    plt.ylabel("t-SNE Component 2")
    plt.tight_layout()
    plt.grid(True)
    
    # 保存和显示
    plt.savefig("tsne_pre_post_building_pixels_8_mean.png", dpi=300)
    plt.show()


def get_diff_feature_tsneplost():
    pt_paths = glob("/mnt/d2/zxq/BDS12/result/palisades/lbdv20/santa2palisades_pvtdiff/santa2palisades_pvtdiff/plot_feature/**.pt")
    print(len(pt_paths))
    feat_lists, feat_index = [], []
    for pt_index, pt_path in enumerate(pt_paths):
        pt_data = torch.load(pt_path)
        pt_data = torch.nn.functional.interpolate(pt_data, scale_factor=4, mode='bilinear')
        post_label = load_label(label_path=os.path.join("/mnt/d2/zxq/BDS12/result/palisades/post_label", os.path.basename(pt_path).replace(".pt", ".tif")))
        post_label = torch.from_numpy(post_label).to(torch.float32).unsqueeze(0).unsqueeze(0)

        pre_label = load_label(label_path=os.path.join("/mnt/d2/zxq/BDS12/result/palisades/pre_label", os.path.basename(pt_path).replace(".pt", ".tif").replace("post", "pre")))
        pre_label = torch.from_numpy(pre_label).to(torch.float32).unsqueeze(0).unsqueeze(0)

        # print(pt_data.shape, label.shape, torch.max(label), torch.min(label))

        pre_mask = (pre_label >= 1).float()
        post_mask = (post_label == 1).float()
        feat = pt_data.squeeze(0).permute(1, 2, 0).reshape(-1, pt_data.shape[1]).cpu()
        post_mask = post_mask.squeeze().reshape(-1)
        pre_mask = torch.abs(pre_mask.squeeze().reshape(-1) - post_mask)

        undamage_feat = feat[pre_mask == 1]
        damage_feat = feat[post_mask == 1]
        print(pt_index, damage_feat.shape, undamage_feat.shape)
        if undamage_feat.shape[0] > damage_feat.shape[0] and damage_feat.shape[0]==0:
            feat_lists.append(torch.mean(undamage_feat, dim=0, keepdim=True))
            if "santa-rosa" in pt_path:
                feat_index.append(0)
            else:
                feat_index.append(2)
        if damage_feat.shape[0] > 0:
            feat_lists.append(torch.mean(damage_feat, dim=0, keepdim=True))
            if "santa-rosa" in pt_path:
                feat_index.append(1)
            else:
                feat_index.append(3)
        # if pt_index>=200:
        #     break
    feat_lists = torch.cat(feat_lists, dim=0).numpy()
    feat_index = np.vstack(feat_index).squeeze()
    print(feat_lists.shape, feat_index.shape)

    tsne = TSNE(n_components=2, random_state=42)
    tsne_result = tsne.fit_transform(feat_lists)
    colors = ['red', 'blue', 'green', 'purple', 'orange', 'cyan', 'magenta', 'yellow']
    labels_text = ['Santa - Pre', 'Santa - Post', 'Palisades - Pre', 'Palisades - Post', 'Eaton - Pre', 'Eaton - Post', 'Palu - Pre', 'Palu - Post']

    plt.figure(figsize=(8, 6))
    # 对不同类别绘制散点图
    for i in range(4):        
        # 绘制灾前样本（圆点
        if (i + 1) % 2 == 0:  # 偶数为灾后（使用三角形）
            marker = '^'
        else:  # 奇数为灾前（使用圆点）
            marker = 'o'
        plt.scatter(tsne_result[feat_index == i, 0], tsne_result[feat_index == i, 1], label=labels_text[i], s=10, alpha=0.6, color=colors[i], marker=marker)

    plt.legend()
    plt.title("T-SNE of Undamage vs. Damage Features from Santa nad Palisades")
    plt.tight_layout()
    plt.show()
    plt.savefig("./diff_feature2.png")

    return


if __name__ == "__main__":
    # get_high_label_tsneplot()
    # get_s2_label_tsneplot(sample_size=1000)
    get_diff_feature_tsneplost()
